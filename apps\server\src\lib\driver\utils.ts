import { getActiveConnection } from '../server-utils';
import { getContext } from 'hono/context-storage';
import type { gmail_v1 } from '@googleapis/gmail';
import { connection } from '@zero/db/schema';
import type { HonoContext } from '../../ctx';
import { createDriver } from '../driver';
import { toByteArray } from 'base64-js';
import { and, eq } from 'drizzle-orm';
import type { MailManager } from './types';

// 驱动实例缓存，避免频繁重连
const driverCache = new Map<string, { driver: MailManager; lastUsed: number; lastValidated: number }>();

// 当前活跃连接ID，用于检测驱动器切换
let currentActiveConnectionId: string | null = null;

// 缓存清理间隔（5分钟）
const CACHE_CLEANUP_INTERVAL = 5 * 60 * 1000;
// 驱动实例过期时间（10分钟）
const DRIVER_EXPIRE_TIME = 10 * 60 * 1000;
// 连接验证间隔（2分钟）
const VALIDATION_INTERVAL = 2 * 60 * 1000;

// 定期清理过期的驱动实例
setInterval(() => {
  const now = Date.now();
  for (const [key, { lastUsed }] of driverCache.entries()) {
    if (now - lastUsed > DRIVER_EXPIRE_TIME) {
      console.log(`清理过期的驱动实例: ${key}`);
      driverCache.delete(key);
    }
  }
}, CACHE_CLEANUP_INTERVAL);

export const FatalErrors = ['invalid_grant'];

export const deleteActiveConnection = async () => {
  const c = getContext<HonoContext>();
  const activeConnection = await getActiveConnection();
  if (!activeConnection) return console.log('No connection ID found');
  const session = await c.var.auth.api.getSession({ headers: c.req.raw.headers });
  if (!session) return console.log('No session found');
  try {
    await c.var.auth.api.signOut({ headers: c.req.raw.headers });
    await c.var.db
      .delete(connection)
      .where(and(eq(connection.userId, session.user.id), eq(connection.id, activeConnection.id)));
  } catch (error) {
    console.error('Server: Error deleting connection:', error);
    throw error;
  }
};

export const getActiveDriver = async () => {
  const c = getContext<HonoContext>();
  const session = await c.var.auth.api.getSession({ headers: c.req.raw.headers });
  if (!session) throw new Error('Invalid session');
  const activeConnection = await getActiveConnection();
  if (!activeConnection) throw new Error('Invalid connection');

  console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 后端getActiveDriver调用`);
  console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 当前活跃连接: ${activeConnection.id} (${activeConnection.email})`);
  console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 上次活跃连接: ${currentActiveConnectionId}`);

  // 🎯 检测驱动器切换
  if (currentActiveConnectionId && currentActiveConnectionId !== activeConnection.id) {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 检测到驱动器切换: ${currentActiveConnectionId} -> ${activeConnection.id}`);
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🧹 强制清理IMAP连接池`);

    // 🚨 关键修复：先清理IMAP连接池，再清理驱动器缓存
    try {
      // 方法1：清理所有缓存的驱动实例的IMAP连接
      for (const [key, cachedDriver] of driverCache.entries()) {
        if (cachedDriver.driver && typeof cachedDriver.driver.clearConnectionPool === 'function') {
          cachedDriver.driver.clearConnectionPool();
          console.log(`[${new Date().toLocaleString('zh-CN')}] 🧹 已清理驱动器连接池: ${key}`);
        }
      }

      // 方法2：清理全局IMAP连接池（双重保险）
      const { ImapConnectionPool } = await import('./imap-smtp');
      if (ImapConnectionPool && typeof ImapConnectionPool.clearGlobalPool === 'function') {
        ImapConnectionPool.clearGlobalPool();
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🧹 已清理全局IMAP连接池`);
      }
    } catch (error) {
      console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 清理IMAP连接池时出错:`, error);
    }

    // 清理驱动器缓存
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🧹 清理旧驱动器缓存`);
    driverCache.clear();
  }

  // 🚨 关键修复：立即更新当前活跃连接ID
  const oldConnectionId = currentActiveConnectionId;
  currentActiveConnectionId = activeConnection.id;
  console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 更新活跃连接ID: ${oldConnectionId} -> ${currentActiveConnectionId}`);

  // 创建缓存键，基于连接ID和配置
  const cacheKey = `${activeConnection.id}-${activeConnection.providerId}-${activeConnection.email}`;

  // 检查缓存中是否有有效的驱动实例
  const cached = driverCache.get(cacheKey);
  if (cached) {
    const now = Date.now();
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🚀 使用缓存的驱动实例: ${activeConnection.email}`);
    cached.lastUsed = now;
    cached.lastValidated = now; // 更新验证时间，避免过期
    return cached.driver;
  }

  console.log(`[${new Date().toLocaleString('zh-CN')}] 🔧 创建新的驱动实例: ${activeConnection.email}`);

  let driver: MailManager;

  // For IMAP/SMTP connections, we need to pass additional configuration from the scope field
  if (activeConnection.providerId === 'credential') {
    if (!activeConnection.refreshToken) {
      throw new Error('IMAP/SMTP connection is missing password, please reconnect');
    }

    try {
      // Parse the IMAP/SMTP settings from the scope field
      const config = JSON.parse(activeConnection.scope);

      // 🎯 对于所有 IMAP provider，都使用 'credential' 作为实际的 driver 类型
      driver = createDriver('credential', {
        auth: {
          accessToken: activeConnection.accessToken || '',
          refreshToken: activeConnection.refreshToken,
          email: activeConnection.email,
          // Pass through the IMAP/SMTP configuration
          ...config.auth,
        },
      });
    } catch (error) {
      console.error('Error parsing IMAP/SMTP configuration:', error);
      throw new Error('Invalid IMAP/SMTP configuration');
    }
  } else {
    // For OAuth providers, we need both refreshToken and accessToken
    if (!activeConnection.accessToken || !activeConnection.refreshToken)
      throw new Error('Invalid connection');

    driver = createDriver(activeConnection.providerId, {
      auth: {
        accessToken: activeConnection.accessToken,
        refreshToken: activeConnection.refreshToken,
        userId: activeConnection.userId,
        email: activeConnection.email,
      },
    });
  }

  // 将新创建的驱动实例添加到缓存
  const now = Date.now();
  driverCache.set(cacheKey, {
    driver,
    lastUsed: now,
    lastValidated: now,
  });

  console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 驱动实例已缓存: ${cacheKey}`);
  return driver;
};

export const fromBase64Url = (str: string) => str.replace(/-/g, '+').replace(/_/g, '/');

export const fromBinary = (str: string) =>
  new TextDecoder().decode(toByteArray(str.replace(/-/g, '+').replace(/_/g, '/')));

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const findHtmlBody = (parts: any[]): string => {
  for (const part of parts) {
    if (part.mimeType === 'text/html' && part.body?.data) {
      return part.body.data;
    }
    if (part.parts) {
      const found = findHtmlBody(part.parts);
      if (found) return found;
    }
  }
  console.log('⚠️ Driver: No HTML content found in message parts');
  return '';
};

export class StandardizedError extends Error {
  code: string;
  operation: string;
  context?: Record<string, unknown>;
  originalError: unknown;
  constructor(
    error: Error & { code: string },
    operation: string,
    context?: Record<string, unknown>,
  ) {
    super(error?.message || 'An unknown error occurred');
    this.name = 'StandardizedError';
    this.code = error?.code || 'UNKNOWN_ERROR';
    this.operation = operation;
    this.context = context;
    this.originalError = error;
  }
}

export function sanitizeContext(context?: Record<string, unknown>) {
  if (!context) return undefined;
  const sanitized = { ...context };
  const sensitive = ['tokens', 'refresh_token', 'code', 'message', 'raw', 'data'];
  for (const key of sensitive) {
    if (key in sanitized) {
      sanitized[key] = '[REDACTED]';
    }
  }
  return sanitized;
}

/**
 * Retrieves the original sender address for a forwarded email from SimpleLogin
 * from the headers of a Gmail email. Header: `X-SimpleLogin-Original-From`
 */
export function getSimpleLoginSender(payload: gmail_v1.Schema$Message['payload']) {
  return payload?.headers?.find((h) => h.name === 'X-SimpleLogin-Original-From')?.value || null;
}
