services:
  db:
    container_name: zerodotemail-db
    image: postgres:17
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: zerodotemail
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - 5432:5432
    volumes:
      - postgres-data:/var/lib/postgresql/data

  valkey:
    container_name: zerodotemail-redis
    image: docker.io/bitnami/valkey:8.0
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - VALKEY_DISABLE_COMMANDS=FLUSHDB,FLUSHALL
    ports:
      - 6379:6379
    volumes:
      - valkey-data:/bitnami/valkey/data

  upstash-proxy:
    container_name: zerodotemail-upstash-proxy
    image: hiett/serverless-redis-http:latest
    environment:
      SRH_MODE: env
      SRH_TOKEN: upstash-local-token
      SRH_CONNECTION_STRING: 'redis://valkey:6379'
    ports:
      - 8079:80

volumes:
  valkey-data:
  postgres-data:
