VITE_PUBLIC_APP_URL=http://localhost:80
VITE_PUBLIC_BACKEND_URL=http://localhost:8787

DATABASE_URL="*************************************************/zerodotemail"

# Change this to a random string, use `openssl rand -hex 32` to generate a 32 character string
BETTER_AUTH_SECRET=550e8400e29b41d4a716446655440000
BETTER_AUTH_URL=http://localhost:80

COOKIE_DOMAIN="localhost"

# Change to your project's client ID and secret, these work with localhost:8787
GOOGLE_CLIENT_ID=439216317135-l82ffr472gu0m3nf7ie51v2ncr1nq7iv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Upstash/Local Redis Instance
REDIS_URL="http://*************:6380"
REDIS_TOKEN="upstash-local-token"

# Resend API Key
RESEND_API_KEY=

# OpenAI API Key
OPENAI_API_KEY=

#AI PROMPT
AI_SYSTEM_PROMPT=""

NODE_ENV="development"

AUTUMN_SECRET_KEY=am_sk_test_a4JJh9DgbRqgfLdxFHcjtj0VYsWuXzAUFmMDSNPAYT