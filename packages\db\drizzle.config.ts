import { type Config } from "drizzle-kit";
import { config } from "dotenv";

// 🚀 确保环境变量被正确加载
config({ path: "../../.env" });

console.log("🔍 DATABASE_URL:", process.env.DATABASE_URL ? "已设置" : "未设置");

export default {
  schema: "./src/schema.ts",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL || "postgresql://postgres:postgres@localhost:5432/zerodotemail",
  },
  out: "./migrations",
  tablesFilter: ["mail0_*"],
  // 🚀 添加详细日志
  verbose: true,
  strict: true,
} satisfies Config;
