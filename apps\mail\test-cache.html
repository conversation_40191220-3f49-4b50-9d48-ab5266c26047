<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存清单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .folder {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .folder h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>📋 缓存清单测试页面</h1>
    
    <div>
        <button onclick="initializeCache()">🏗️ 初始化缓存</button>
        <button onclick="loadManifest()">📋 加载清单</button>
        <button onclick="clearCache()">🗑️ 清空缓存</button>
        <button onclick="simulateCache()">🧪 模拟缓存</button>
    </div>

    <div id="manifest-display"></div>
    
    <div class="log" id="log"></div>

    <script type="module">
        // 模拟 IndexedDB 缓存清单管理器
        class TestCacheManifestManager {
            constructor() {
                this.dbName = 'zero-cache-manifest';
                this.db = null;
            }

            async initialize() {
                return new Promise((resolve, reject) => {
                    const request = indexedDB.open(this.dbName, 1);
                    
                    request.onerror = () => reject(request.error);
                    request.onsuccess = () => {
                        this.db = request.result;
                        resolve();
                    };
                    
                    request.onupgradeneeded = (event) => {
                        const db = event.target.result;
                        if (!db.objectStoreNames.contains('manifests')) {
                            db.createObjectStore('manifests', { keyPath: 'accountId' });
                        }
                    };
                });
            }

            async getManifest(accountId) {
                if (!this.db) await this.initialize();
                
                return new Promise((resolve, reject) => {
                    const transaction = this.db.transaction(['manifests'], 'readonly');
                    const store = transaction.objectStore('manifests');
                    const request = store.get(accountId);
                    
                    request.onerror = () => reject(request.error);
                    request.onsuccess = () => resolve(request.result || null);
                });
            }

            async updateManifest(accountId, accountEmail, updates) {
                if (!this.db) await this.initialize();
                
                const existing = await this.getManifest(accountId);
                
                const manifest = {
                    accountId,
                    accountEmail,
                    lastUpdated: Date.now(),
                    folders: existing?.folders || {},
                    ...updates,
                };

                return new Promise((resolve, reject) => {
                    const transaction = this.db.transaction(['manifests'], 'readwrite');
                    const store = transaction.objectStore('manifests');
                    const request = store.put(manifest);
                    
                    request.onerror = () => reject(request.error);
                    request.onsuccess = () => resolve();
                });
            }

            async initializeFolders(accountId, accountEmail, folders) {
                const manifest = await this.getManifest(accountId);
                const folderData = {};

                folders.forEach(folder => {
                    folderData[folder] = manifest?.folders[folder] || {
                        totalEmails: 0,
                        cachedPages: 0,
                        cachedEmails: 0,
                        lastCachedCursor: '',
                        isCompleted: false,
                        lastCacheTime: 0,
                    };
                });

                await this.updateManifest(accountId, accountEmail, { folders: folderData });
            }

            async updateFolderProgress(accountId, folderName, progress) {
                const manifest = await this.getManifest(accountId);
                if (!manifest) return;

                if (!manifest.folders[folderName]) {
                    manifest.folders[folderName] = {
                        totalEmails: 0,
                        cachedPages: 0,
                        cachedEmails: 0,
                        lastCachedCursor: '',
                        isCompleted: false,
                        lastCacheTime: 0,
                    };
                }

                const updateData = { ...progress, lastCacheTime: Date.now() };
                if (progress.totalEmails === undefined) {
                    delete updateData.totalEmails;
                }

                Object.assign(manifest.folders[folderName], updateData);
                await this.updateManifest(accountId, manifest.accountEmail, { folders: manifest.folders });
            }

            async estimateFolderTotal(accountId, folderName, firstPageCount, hasMore) {
                if (!hasMore) {
                    await this.updateFolderProgress(accountId, folderName, {
                        totalEmails: firstPageCount,
                    });
                    log(`📊 ${folderName} 总数确定: ${firstPageCount} 封邮件`);
                } else {
                    log(`📊 ${folderName} 有更多邮件，总数待确定`);
                }
            }

            async clearManifest(accountId) {
                if (!this.db) await this.initialize();
                
                return new Promise((resolve, reject) => {
                    const transaction = this.db.transaction(['manifests'], 'readwrite');
                    const store = transaction.objectStore('manifests');
                    const request = store.delete(accountId);
                    
                    request.onerror = () => reject(request.error);
                    request.onsuccess = () => resolve();
                });
            }
        }

        const cacheManager = new TestCacheManifestManager();
        const testAccountId = '<EMAIL>';

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleString('zh-CN');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function displayManifest(manifest) {
            const display = document.getElementById('manifest-display');
            
            if (!manifest) {
                display.innerHTML = '<p>暂无缓存清单数据</p>';
                return;
            }

            let html = `
                <h2>📋 缓存清单 - ${manifest.accountEmail}</h2>
                <p><strong>最后更新:</strong> ${new Date(manifest.lastUpdated).toLocaleString('zh-CN')}</p>
            `;

            Object.entries(manifest.folders).forEach(([folderName, data]) => {
                const completionRate = data.totalEmails > 0 ? 
                    Math.round((data.cachedEmails / data.totalEmails) * 100) : 0;
                
                html += `
                    <div class="folder">
                        <h3>📁 ${folderName}</h3>
                        <div class="stats">
                            <div class="stat">
                                <div class="stat-value">${data.totalEmails}</div>
                                <div class="stat-label">总邮件数</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">${data.cachedEmails}</div>
                                <div class="stat-label">已缓存</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">${data.cachedPages}</div>
                                <div class="stat-label">已缓存页数</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">${completionRate}%</div>
                                <div class="stat-label">完成度</div>
                            </div>
                        </div>
                        <p><strong>状态:</strong> ${data.isCompleted ? '✅ 已完成' : '🔄 进行中'}</p>
                        <p><strong>最后缓存:</strong> ${data.lastCacheTime ? new Date(data.lastCacheTime).toLocaleString('zh-CN') : '未开始'}</p>
                    </div>
                `;
            });

            display.innerHTML = html;
        }

        // 全局函数
        window.initializeCache = async function() {
            try {
                log('🏗️ 开始初始化缓存清单...');
                const folders = ['inbox', 'drafts', 'sent', 'archive', 'spam', 'bin'];
                await cacheManager.initializeFolders(testAccountId, testAccountId, folders);
                
                // 模拟估算文件夹总数
                const folderCounts = {
                    inbox: { count: 25, hasMore: true },
                    drafts: { count: 3, hasMore: false },
                    sent: { count: 15, hasMore: true },
                    archive: { count: 8, hasMore: false },
                    spam: { count: 0, hasMore: false },
                    bin: { count: 2, hasMore: false }
                };

                for (const [folder, data] of Object.entries(folderCounts)) {
                    await cacheManager.estimateFolderTotal(testAccountId, folder, data.count, data.hasMore);
                }

                log('✅ 缓存清单初始化完成');
                await loadManifest();
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`);
            }
        };

        window.loadManifest = async function() {
            try {
                log('📋 加载缓存清单...');
                const manifest = await cacheManager.getManifest(testAccountId);
                displayManifest(manifest);
                log('✅ 缓存清单加载完成');
            } catch (error) {
                log(`❌ 加载失败: ${error.message}`);
            }
        };

        window.clearCache = async function() {
            try {
                log('🗑️ 清空缓存清单...');
                await cacheManager.clearManifest(testAccountId);
                displayManifest(null);
                log('✅ 缓存清单已清空');
            } catch (error) {
                log(`❌ 清空失败: ${error.message}`);
            }
        };

        window.simulateCache = async function() {
            try {
                log('🧪 开始模拟缓存过程...');
                
                // 模拟缓存 inbox 的几页
                await cacheManager.updateFolderProgress(testAccountId, 'inbox', {
                    cachedPages: 1,
                    cachedEmails: 10,
                    lastCachedCursor: 'cursor1'
                });
                log('📊 inbox 第1页缓存完成 (10封邮件)');

                await cacheManager.updateFolderProgress(testAccountId, 'inbox', {
                    cachedPages: 2,
                    cachedEmails: 20,
                    lastCachedCursor: 'cursor2'
                });
                log('📊 inbox 第2页缓存完成 (20封邮件)');

                // 模拟 drafts 缓存完成
                await cacheManager.updateFolderProgress(testAccountId, 'drafts', {
                    cachedPages: 1,
                    cachedEmails: 3,
                    lastCachedCursor: '',
                    isCompleted: true,
                    totalEmails: 3
                });
                log('📊 drafts 缓存完成 (3封邮件)');

                await loadManifest();
                log('✅ 模拟缓存完成');
            } catch (error) {
                log(`❌ 模拟失败: ${error.message}`);
            }
        };

        // 页面加载时自动加载清单
        window.addEventListener('load', loadManifest);
    </script>
</body>
</html>
