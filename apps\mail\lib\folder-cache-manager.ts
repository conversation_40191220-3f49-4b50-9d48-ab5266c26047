/**
 * 邮箱文件夹自动缓存管理器 - 轮询式缓存
 * 
 * 功能：
 * - 轮询式缓存：inbox第1页 → drafts第1页 → sent第1页 → ... → inbox第2页
 * - 基于 IndexedDB 清单记录缓存进度
 * - 智能暂停和恢复
 * - 错误重试机制
 */

import { cacheManifestManager } from './cache-manifest-manager';

export interface CacheProgress {
  current: number;
  total: number;
  currentFolder: string | null;
  completedFolders: string[];
  failedFolders: string[];
}

export interface CacheResult {
  emailCount: number;
  nextCursor: string;
  hasMore: boolean;
}

export interface CacheOptions {
  interval: number; // 缓存间隔（毫秒）
  maxRetries: number; // 最大重试次数
  retryDelay: number; // 重试延迟（毫秒）
}

export type CacheStatus = 'idle' | 'running' | 'paused' | 'completed' | 'error';

export class FolderCacheManager {
  private timer: NodeJS.Timeout | null = null;
  private status: CacheStatus = 'idle';
  private retryCount = 0;
  private accountId: string = '';
  private accountEmail: string = '';

  private readonly defaultOptions: CacheOptions = {
    interval: 2 * 60 * 1000, // 2分钟
    maxRetries: 3,
    retryDelay: 30 * 1000, // 30秒
  };

  constructor(
    private options: Partial<CacheOptions> = {},
    private onProgress?: (progress: CacheProgress) => void,
    private onStatusChange?: (status: CacheStatus) => void,
    private onError?: (error: Error, folder: string) => void,
    private cacheFn?: (folder: string, cursor: string) => Promise<CacheResult>
  ) {
    this.options = { ...this.defaultOptions, ...options };
  }

  /**
   * 初始化缓存清单
   */
  public async initializeManifest(accountId: string, accountEmail: string, folders: string[]): Promise<void> {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🗂️ 初始化缓存清单: ${accountEmail}`);
    
    this.accountId = accountId;
    this.accountEmail = accountEmail;
    
    // 初始化缓存清单
    await cacheManifestManager.initializeFolders(accountId, accountEmail, folders);
    
    console.log(`[${new Date().toLocaleString('zh-CN')}] 📋 缓存清单初始化完成`);
    await this.emitProgress();
  }

  /**
   * 开始自动缓存
   */
  public async start(): Promise<void> {
    if (this.status === 'running') {
      console.log(`[${new Date().toLocaleString('zh-CN')}] ⚠️ 缓存已在运行中`);
      return;
    }

    if (!this.accountId) {
      console.log(`[${new Date().toLocaleString('zh-CN')}] ❌ 账号ID未设置，无法开始缓存`);
      return;
    }

    // 检查是否还有需要缓存的内容
    const nextTarget = await cacheManifestManager.getNextCacheTarget(this.accountId);
    if (!nextTarget) {
      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 所有文件夹已缓存完成`);
      this.setStatus('completed');
      return;
    }

    console.log(`[${new Date().toLocaleString('zh-CN')}] 🚀 开始轮询缓存: ${this.accountEmail}`);
    this.setStatus('running');
    this.scheduleNext();
  }

  /**
   * 暂停缓存
   */
  public pause(): void {
    console.log(`[${new Date().toLocaleString('zh-CN')}] ⏸️ 暂停自动缓存`);
    this.clearTimer();
    this.setStatus('paused');
  }

  /**
   * 恢复缓存
   */
  public resume(): void {
    console.log(`[${new Date().toLocaleString('zh-CN')}] ▶️ 恢复自动缓存`);
    this.setStatus('running');
    this.scheduleNext();
  }

  /**
   * 停止缓存
   */
  public stop(): void {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🛑 停止自动缓存`);
    this.clearTimer();
    this.setStatus('idle');
    this.retryCount = 0;
  }

  /**
   * 获取当前进度
   */
  public async getProgress(): Promise<CacheProgress> {
    if (!this.accountId) {
      return {
        current: 0,
        total: 0,
        currentFolder: null,
        completedFolders: [],
        failedFolders: [],
      };
    }

    const stats = await cacheManifestManager.getCacheStats(this.accountId);
    if (!stats) {
      return {
        current: 0,
        total: 0,
        currentFolder: null,
        completedFolders: [],
        failedFolders: [],
      };
    }

    const nextTarget = await cacheManifestManager.getNextCacheTarget(this.accountId);
    
    return {
      current: stats.completedFolders,
      total: stats.totalFolders,
      currentFolder: nextTarget?.folder || null,
      completedFolders: stats.folderStats.filter(f => f.isCompleted).map(f => f.folder),
      failedFolders: [], // 暂时不跟踪失败的文件夹
    };
  }

  /**
   * 获取当前状态
   */
  public getStatus(): CacheStatus {
    return this.status;
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.stop();
  }

  /**
   * 安排下一次缓存
   */
  private scheduleNext(): void {
    this.clearTimer();
    this.timer = setTimeout(() => {
      this.processNext();
    }, this.options.interval);
  }

  /**
   * 处理下一个缓存目标
   */
  private async processNext(): Promise<void> {
    if (this.status !== 'running') return;

    // 获取下一个需要缓存的目标
    const nextTarget = await cacheManifestManager.getNextCacheTarget(this.accountId);
    if (!nextTarget) {
      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 所有文件夹缓存完成`);
      this.setStatus('completed');
      return;
    }

    const { folder, page, cursor } = nextTarget;
    console.log(`[${new Date().toLocaleString('zh-CN')}] 处理文件夹: ${folder} 第${page}页`);

    try {
      // 使用外部传入的缓存函数
      if (this.cacheFn) {
        const result = await this.cacheFn(folder, cursor);
        
        // 更新缓存清单
        const currentCachedEmails = (page - 1) * 10 + result.emailCount;
        await cacheManifestManager.updateFolderProgress(this.accountId, folder, {
          cachedPages: page,
          cachedEmails: currentCachedEmails,
          lastCachedCursor: result.nextCursor,
          isCompleted: !result.hasMore,
          // 如果没有更多邮件，说明我们已经知道了总数
          totalEmails: !result.hasMore ? currentCachedEmails : undefined,
        });
      } else {
        // 如果没有缓存函数，只是模拟延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      this.retryCount = 0;
      console.log(`[${new Date().toLocaleString('zh-CN')}] 文件夹处理成功: ${folder} 第${page}页`);
      await this.emitProgress();

    } catch (error) {
      console.error(`[${new Date().toLocaleString('zh-CN')}] 文件夹处理失败: ${folder} 第${page}页`, error);

      this.retryCount++;
      if (this.retryCount >= this.options.maxRetries!) {
        console.log(`[${new Date().toLocaleString('zh-CN')}] 文件夹重试次数超限，跳过: ${folder}`);
        // 标记为完成以跳过
        await cacheManifestManager.updateFolderProgress(this.accountId, folder, {
          isCompleted: true,
        });
        this.retryCount = 0;
      }

      this.onError?.(error as Error, folder);
    }

    // 继续处理下一个
    this.scheduleNext();
  }

  /**
   * 清除定时器
   */
  private clearTimer(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * 设置状态并通知
   */
  private setStatus(status: CacheStatus): void {
    this.status = status;
    this.onStatusChange?.(status);
  }

  /**
   * 发送进度更新
   */
  private async emitProgress(): Promise<void> {
    const progress = await this.getProgress();
    this.onProgress?.(progress);
  }
}
