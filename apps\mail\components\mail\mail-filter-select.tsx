import { useEffect, useRef } from "react";
import { Toolt<PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useMail } from "./use-mail";
import {
  Archive2,
  Bell,
  CurvedArrow,
  Eye,
  Lightning,
  Mail,
  Star2,
  Tag,
  User,
  X,
  Trash,
  ScanEye,
  Plus,
  Star,
} from '../icons/icons';
import { useTranslations } from 'use-intl';
import { useQueryState } from "nuqs";
import { cn } from "@/lib/utils";
import { useParams } from "react-router";


export const Categories = () => {
  const t = useTranslations();
  const [category] = useQueryState('mail-filter', {
    defaultValue: 'All Mail',
  });
  return [
    {
      id: 'All Mail',
      name: 'All Mail',
      searchValue: '',
      icon: (
        <Mail
          className={cn('fill-[#006FFE] dark:fill-white', category === 'All Mail' && 'fill-white')}
        />
      ),
      colors:
        'border-0 bg-[#006FFE] text-white dark:bg-[#006FFE] dark:text-white dark:hover:bg-[#006FFE]/90',
    },
    {
      id: 'Flagged',
      name: 'Starred',
      searchValue: '',
      icon: (
        <Star
          className={cn('fill-[#e7d000] dark:fill-white size-[20px]', category === 'Flagged' && 'fill-white')}
        />
      ),
    },
    {
      id: 'Unread',
      name: 'Unread',
      searchValue: '',
      icon: (
        <ScanEye
          className={cn(
            'h-4 w-4 fill-[#FF4800] dark:fill-white',
            category === 'Unread' && 'fill-white',
          )}
        />
      ),
    },
  ];
};

type CategoryType = ReturnType<typeof Categories>[0];

function getCategoryColor(categoryId: string): string {
  switch (categoryId.toLowerCase()) {
    case 'primary':
      return 'bg-[#006FFE]';
    case 'all mail':
      return 'bg-[#006FFE]';
    case 'flagged':
      return 'bg-[#e7d000]';
    case 'promotions':
      return 'bg-[#F43F5E]';
    case 'personal':
      return 'bg-[#39ae4a]';
    case 'updates':
      return 'bg-[#8B5CF6]';
    case 'unread':
      return 'bg-[#FF4800]';
    default:
      return 'bg-base-primary-500';
  }
}

export default function MailFilterSelect() {
  const [mail, setMail] = useMail();
//   const [searchValue, setSearchValue] = useSearchValue();
  const categories = Categories();
  const params = useParams<{ folder: string }>();
  const folder = params?.folder ?? 'inbox';
  const [category, setCategory] = useQueryState('mail-filter', {
    defaultValue: 'All Mail',
  });
  const containerRef = useRef<HTMLDivElement>(null);
  const activeTabElementRef = useRef<HTMLButtonElement>(null);

  // Only show category selection for inbox folder
  if (folder !== 'inbox') return <div className="h-8"></div>;

  // Primary category is always the first one
  const primaryCategory = categories[0];
  if (!primaryCategory) return null;

  const renderCategoryButton = (cat: CategoryType, isOverlay = false, idx: number) => {
    const isSelected = cat.id === (category || 'Primary');
    const bgColor = getCategoryColor(cat.id);

    return (
      <Tooltip key={cat.id}>
        <TooltipTrigger asChild>
          <button
            ref={!isOverlay ? activeTabElementRef : null}
            onClick={() => {
              setCategory(cat.id);
            }}
            className={cn(
              'flex h-8 items-center justify-center gap-1 overflow-hidden rounded-lg border-none transition-all duration-300 ease-out dark:border-none',
              isSelected
                ? cn('flex-[1_0_auto] border-none px-2 text-white', bgColor)
                : 'flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-[#313131] dark:hover:bg-[#313131]/80',
            )}
            tabIndex={isOverlay ? -1 : undefined}
          >
            <div className="relative overflow-visible">{cat.icon}</div>
            {/* {!isSelected && <span>{cat.name}</span>} */}
            {isSelected && (
              <div className="flex items-center justify-center gap-2.5 px-0.5">
                <div className="animate-in fade-in-0 slide-in-from-right-4 justify-start text-sm leading-none text-white duration-300">
                  {cat.name}
                </div>
              </div>
            )}
          </button>
        </TooltipTrigger>
        {!isSelected && (
          <TooltipContent side="top" className={`${idx === 0 ? 'ml-4' : ''}`}>
            <span className="mr-2">{cat.name}</span>
          </TooltipContent>
        )}
      </Tooltip>
    );
  };

  // Update clip path when category changes
  useEffect(() => {
    const container = containerRef.current;
    const activeTabElement = activeTabElementRef.current;

    if (category && container && activeTabElement) {
      setMail({ ...mail, bulkSelected: [] });
      const { offsetLeft, offsetWidth } = activeTabElement;
      const clipLeft = Math.max(0, offsetLeft - 2);
      const clipRight = Math.min(container.offsetWidth, offsetLeft + offsetWidth + 2);
      const containerWidth = container.offsetWidth;

      if (containerWidth) {
        container.style.clipPath = `inset(0 ${Number(100 - (clipRight / containerWidth) * 100).toFixed(2)}% 0 ${Number((clipLeft / containerWidth) * 100).toFixed(2)}%)`;
      }
    }
  }, [category]);


  return (
    <div className="relative w-full">
      <div className="flex w-full items-start justify-start gap-2">
        {categories.map((cat, idx) => renderCategoryButton(cat, false, idx))}
      </div>

      <div
        aria-hidden
        className="pointer-events-none absolute inset-0 z-10 overflow-hidden transition-[clip-path] duration-300 ease-in-out"
        ref={containerRef}
      >
        <div className="flex w-full items-start justify-start gap-2">
          {categories.map((cat, idx) => renderCategoryButton(cat, true, idx))}
        </div>
      </div>
    </div>
  );
}
