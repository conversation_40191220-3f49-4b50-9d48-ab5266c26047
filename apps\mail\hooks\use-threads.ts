import { backgroundQueue<PERSON>tom, isThreadInBackgroundQueueAtom } from '@/store/backgroundQueue';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { useSearchValue } from '@/hooks/use-search-value';
import { useTRPC } from '@/providers/query-provider';
import { useSession } from '@/lib/auth-client';
import { useAtom, useAtomValue } from 'jotai';
import { useParams } from 'react-router';
import { useQueryState } from 'nuqs';
import { useMemo, useEffect } from 'react';
import { useActiveConnection } from './use-connections';
import { useQueryClient } from '@tanstack/react-query';

// 🎯 全局单例：确保邮箱切换逻辑只执行一次
let currentGlobalConnectionId: string | null = null;
let switchTimeoutId: NodeJS.Timeout | null = null;

export const useThreads = () => {
  const { folder } = useParams<{ folder: string }>();
  const [searchValue] = useSearchValue();
  const { data: session } = useSession();
  const [backgroundQueue] = useAtom(backgroundQueueAtom);
  const isInQueue = useAtomValue(isThreadInBackgroundQueueAtom);
  const trpc = useTRPC();
  const [aiLabelQuery] = useQueryState('label_name');
  const { data: activeConnection } = useActiveConnection();
  // const previousConnectionRef = useRef<string | null>(null); // 🎯 不再需要，使用全局单例
  const queryClient = useQueryClient();

  // console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 useThreads调用 - folder: ${folder}, search: ${searchValue.value}`);

  const threadsQuery = useInfiniteQuery(
    trpc.mail.listThreads.infiniteQueryOptions(
      {
        // 当folder为ai-label时，q使用url的category参数，没有则设为空字符串
        q: folder==='ai-label'?aiLabelQuery??'':searchValue.value, 
        folder,
      },
      {
        initialCursor: '',
        getNextPageParam: (lastPage) => lastPage?.nextPageToken ?? null,
        // 恢复缓存：每个连接都有独立的QueryClient，不会有UID冲突
        staleTime: 1000 * 60 * 5, // 5分钟内认为数据是新鲜的
        gcTime: 1000 * 60 * 30, // 30分钟后清理未使用的缓存
        refetchOnMount: false, // 挂载时不自动重新获取（使用缓存）
        refetchOnWindowFocus: false, // 窗口聚焦时不自动重新获取
      },
    ),
  );

  // 监控查询状态变化
  useEffect(() => {
    // console.log(`[${new Date().toLocaleString('zh-CN')}] 📊 useThreads: ${threadsQuery.status}, 获取中=${threadsQuery.isFetching}, 有数据=${!!threadsQuery.data}`);
  }, [threadsQuery.status, threadsQuery.isFetching]);

  // 🚨 监控邮箱切换并清除缓存 - 使用全局单例避免重复执行
  useEffect(() => {
    const currentConnectionId = activeConnection?.id;

    // 🎯 过滤掉无效变化：null/undefined 不算有效连接
    if (!currentConnectionId) {
      return;
    }

    // 🎯 使用全局单例，确保多个组件调用时只执行一次
    if (currentGlobalConnectionId &&
        currentGlobalConnectionId !== currentConnectionId) {

      // 清除之前的延时器
      if (switchTimeoutId) {
        clearTimeout(switchTimeoutId);
      }

      // 使用延时器防抖，确保只执行一次
      switchTimeoutId = setTimeout(() => {
        console.log(`[${new Date().toLocaleString('zh-CN')}] 📧 检测到邮箱切换: ${currentGlobalConnectionId} -> ${currentConnectionId} (${activeConnection?.email})`);

        // 🚨 使用 invalidateQueries 标记缓存过期，而不是完全删除
        queryClient.invalidateQueries({
          queryKey: [['mail', 'listThreads']],
        });

        console.log(`[${new Date().toLocaleString('zh-CN')}] � 已标记邮件列表缓存为过期，将重新验证数据`);

        // 更新全局连接ID
        currentGlobalConnectionId = currentConnectionId;
        switchTimeoutId = null;
      }, 100); // 100ms 防抖
    } else if (!currentGlobalConnectionId) {
      // 首次设置连接ID
      currentGlobalConnectionId = currentConnectionId;
    }
  }, [activeConnection?.id, queryClient, activeConnection?.email]); // 🎯 移除 threadsQuery 依赖，避免无限循环

  // Flatten threads from all pages and sort by receivedOn date (newest first)
  const threads = useMemo(
    () =>
      threadsQuery.data
        ? threadsQuery.data.pages
            .flatMap((e) => e.threads)
            .filter(Boolean)
            .filter((e) => !isInQueue(`thread:${e.id}`))
        : [],
    [threadsQuery.data, session, backgroundQueue, isInQueue],
  );

  const isEmpty = useMemo(() => threads.length === 0, [threads]);
  const isReachingEnd =
    isEmpty ||
    (threadsQuery.data &&
      !threadsQuery.data.pages[threadsQuery.data.pages.length - 1]?.nextPageToken);

  const loadMore = async () => {
    if (threadsQuery.isLoading || threadsQuery.isFetching) return;
    await threadsQuery.fetchNextPage();
  };

  return [threadsQuery, threads, isReachingEnd, loadMore] as const;
};

export const useThread = (threadId: string | null) => {
  const { data: session } = useSession();
  const [_threadId] = useQueryState('threadId');
  const { folder } = useParams<{ folder: string }>(); // 🚀 获取当前文件夹
  const id = threadId ? threadId : _threadId;
  const trpc = useTRPC();

  // console.log(`debug use-thread id:${id} folder:${folder}`);

  const threadQuery = useQuery(
    trpc.mail.get.queryOptions(
      {
        id: id!,
        folder: folder || 'inbox', // 🚀 传递当前文件夹
      },
      {
        enabled: !!id && !!session?.user.id,
        // ✅ 恢复缓存：每个连接都有独立的QueryClient，不会有UID冲突
        staleTime: 1000 * 60 * 10, // 10分钟内认为数据是新鲜的
        gcTime: 1000 * 60 * 60, // 1小时后清理未使用的缓存
        refetchOnMount: false, // 挂载时不自动重新获取（使用缓存）
        refetchOnWindowFocus: false, // 窗口聚焦时不自动重新获取
      },
    ),
  );

  const isGroupThread = useMemo(() => {
    if (!threadQuery.data?.latest?.id) return false;
    const totalRecipients = [
      ...(threadQuery.data.latest.to || []),
      ...(threadQuery.data.latest.cc || []),
      ...(threadQuery.data.latest.bcc || []),
    ].length;
    return totalRecipients > 1;
  }, [threadQuery.data]);

  return { ...threadQuery, isGroupThread };
};
