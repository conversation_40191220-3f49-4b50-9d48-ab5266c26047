import * as z from 'zod';

export const defaultUserSettings = {
  language: 'en',
  timezone: 'UTC',
  dynamicContent: false,
  externalImages: true,
  customPrompt: '',
  trustedSenders: [],
  isOnboarded: false,
  colorTheme: 'system',
} satisfies UserSettings;

export const userSettingsSchema = z.object({
  language: z.string(),
  timezone: z.string(),
  dynamicContent: z.boolean().optional(),
  externalImages: z.boolean(),
  customPrompt: z.string(),
  isOnboarded: z.boolean().optional(),
  trustedSenders: z.string().array().optional(),
  colorTheme: z.enum([
    'light',
    'dark',
    'system',
    'amber-minimal-light',
    'amethyst-haze-light',
    'bold-tech-light',
    'bubblegum-light',
    'caffeine-light',
    'candyland-light',
    'catppuccin-light',
    'claude-light',
    'claymorphism-light',
    'clean-slate-light',
    'cosmic-night-light',
    'cyberpunk-light',
    'doom-64-light',
    'elegant-luxury-light',
    'graphite-light',
    'kodama-grove-light',
    'midnight-bloom-light',
    'mocha-mousse-light',
    'modern-minimal-light',
    'mono-light',
    'nature-light',
    'neo-brutalism-light',
    'northern-lights-light',
    'notebook-light',
    'ocean-breeze-light',
    'pastel-dreams-light',
    'perpetuity-light',
    'quantum-rose-light',
    'retro-arcade-light',
    'solar-dusk-light',
    'starry-night-light',
    'sunset-horizon-light',
    'supabase-light',
    't3-chat-light',
    'tangerine-light',
    'twitter-light',
    'vercel-light',
    'vintage-paper-light'
  ]).default('system'),
});

export type UserSettings = z.infer<typeof userSettingsSchema>;
