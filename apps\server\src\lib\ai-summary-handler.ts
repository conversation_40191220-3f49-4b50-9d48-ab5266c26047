import { connection, emailAiSummaryJob, summary } from '@zero/db/schema';
import { eq, and, count, desc, sql, asc } from 'drizzle-orm';
import { getContext } from 'hono/context-storage';
import type { ParsedMessage } from '../types';
import type { HonoContext } from '../ctx';
import { redis } from './services';

function sleep(ms: number | undefined) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

const cache = redis();

const urlAiCompletions = 'https://in.aihubs.cn:18881/v1/chat/completions';
const aiRequestToken = 'sk-xqDLtFyKdFrQ8mBb7bB091F12e364e5a8f3bC3952f215222';
const aiModel = 'gpt-4o';
const maxSummaryPerEmail = 10;

export async function handleEmailByAiSummary(
  userEmail: string,
  folder: string,
  emailUid: string,
  threadMessages: ParsedMessage[],
) {
  // ai summary任务串行运行
  let isAiSummaryJobAvailable = false;
  while (!isAiSummaryJobAvailable) {
    const jobState = await cache.get<string>(`ai-summary-job:${userEmail}`);
    if (jobState === 'idle') {
      await cache.set<string>(`ai-summary-job:${userEmail}`, 'runing');
      isAiSummaryJobAvailable = true;
    }else if (jobState === null) {
      await cache.set<string>(`ai-summary-job:${userEmail}`, 'idle');
      continue;
    }
    await sleep(20000);
  }

  await sleep(10);
  console.log(
    `debug ai-summary-handler detail: userEmail:${userEmail} folder:${folder} emailUid:${emailUid}`,
  );
  const c = getContext<HonoContext>();

  const [queryJobState] = await c.var.db
    .select()
    .from(emailAiSummaryJob)
    .where(eq(emailAiSummaryJob.guid, `${userEmail}:${folder}:${emailUid}`));

  if (queryJobState && (queryJobState.state === 'pending' || queryJobState.state === 'finished')) {
    await cache.set<string>(`ai-summary-job:${userEmail}`, 'idle');
    return;
  }

  const [emailAiSummaryJobRow] = await c.var.db
    .insert(emailAiSummaryJob)
    .values({
      guid: `${userEmail}:${folder}:${emailUid}`,
      uid: emailUid,
      email: userEmail,
      folder,
      state: 'pending',
    })
    .returning();
  if (!emailAiSummaryJobRow) {
    await cache.set<string>(`ai-summary-job:${userEmail}`, 'idle');
    return;
  }

  let mailContentStr = '';
  for (const threadMessage of threadMessages) {    
    mailContentStr = mailContentStr + '\n' + threadMessage.body;
  }

  // console.log('debug ai summary mailContentStr:',mailContentStr.length,mailContentStr)

  let threadMessagesReceivedOn;
  let maxReceivedOnTimestap = 0;
  for (const threadMessage of threadMessages) {
    if (new Date(threadMessage.receivedOn).getTime() > maxReceivedOnTimestap) {
      maxReceivedOnTimestap = new Date(threadMessage.receivedOn).getTime();
      threadMessagesReceivedOn = new Date(threadMessage.receivedOn);
    }
  }

  // 判断邮件文本长度
  if (mailContentStr.length > 100) {
    const chatContent =
      "Here's an email:\n" +
      '---\n' +
      mailContentStr +
      '---\n' +
      'Summarize the entire email above as a very short paragraph.';

    console.log('debug ai summary chatContent:', chatContent);

    try {
      const aiResponse = await fetch(urlAiCompletions, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${aiRequestToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: aiModel,
          messages: [
            {
              role: 'user',
              content: chatContent,
            },
          ],
        }),
      });
      if (!aiResponse.ok) {
        // console.error(`AI label无法调用AI:${urlAiCompletions}`);
        throw new Error('failed to fetch ' + aiResponse.status);
      }

      const aiResult: any = await aiResponse.json();
      const modelResultMd: string = aiResult.choices[0].message.content;
      console.log('debug summary modelResultMd:', modelResultMd);

      const emailSummaryRows = await c.var.db
        .select()
        .from(summary)
        .where(eq(summary.email, userEmail))
        .orderBy(asc(summary.createdAt));

      if (emailSummaryRows.length >= maxSummaryPerEmail) {
        // 如果总结记录超过设定值，删除掉最旧的，再添加
        const oldestSummaryRow = emailSummaryRows[0];
        if (oldestSummaryRow) {
          await c.var.db.delete(summary).where(eq(summary.messageId, oldestSummaryRow.messageId));
        }
      }

      await c.var.db.insert(summary).values({
        messageId: `${userEmail}:${folder}:${emailUid}`,
        content: modelResultMd,
        uid: emailUid,
        email: userEmail,
        folder: folder,
      });
      
    } catch (error) {
      console.error(`AI Summary无法调用AI:${urlAiCompletions} 错误：${error}`);
    } finally {
      await cache.set<string>(`ai-summary-job:${userEmail}`, 'idle');
    }
  } else {
    //邮件长度小于特定长度

    const emailSummaryRows = await c.var.db
      .select()
      .from(summary)
      .where(eq(summary.email, userEmail))
      .orderBy(asc(summary.createdAt));

    if (emailSummaryRows.length >= maxSummaryPerEmail) {
      // 如果总结记录超过设定值，删除掉最旧的，再添加
      const oldestSummaryRow = emailSummaryRows[0];
      if (oldestSummaryRow) {
        await c.var.db.delete(summary).where(eq(summary.messageId, oldestSummaryRow.messageId));
      }
    }

    await c.var.db.insert(summary).values({
      messageId: `${userEmail}:${folder}:${emailUid}`,
      content: mailContentStr,
      uid: emailUid,
      email: userEmail,
      folder: folder,
    });
  }

  await c.var.db
    .update(emailAiSummaryJob)
    .set({
      state: 'finished',
    })
    .where(eq(emailAiSummaryJob.id, emailAiSummaryJobRow.id));
  await cache.set<string>(`ai-summary-job:${userEmail}`, 'idle');
}
