/**
 * 缓存监控 Hook - 监控用户手动缓存并同步到缓存清单
 */

import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useActiveConnection } from '@/hooks/use-connections';
import { cacheManifestManager } from '@/lib/cache-manifest-manager';

export const useCacheMonitor = () => {
  const queryClient = useQueryClient();
  const { data: activeConnection } = useActiveConnection();
  const lastSyncRef = useRef<number>(0);

  useEffect(() => {
    if (!activeConnection?.email) return;

    console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 启动缓存监控: ${activeConnection.email}`);

    // 监控 React Query 缓存变化
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      // 监控 mail.listThreads 查询的添加和更新事件
      if (event.type === 'added' || event.type === 'updated') {
        const query = event.query;
        const queryKey = query.queryKey as any;

        // 🔍 调试：打印所有查询键
        // console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 检测到查询:`, {
        //   type: event.type,
        //   queryKey: queryKey,
        //   status: query.state.status,
        //   hasData: !!query.state.data
        // });

        // 修复查询键匹配：考虑 TRPC 的 queryKeyHashFn 包装
        // 实际格式可能是: [{ cacheKey }, ["mail", "listThreads"], { q: "", folder: "inbox" }]
        let trpcQueryKey = null;
        let queryData = null;

        // 尝试不同的查询键格式
        if (queryKey[1]?.[0] === 'mail' && queryKey[1]?.[1] === 'listThreads') {
          // 格式: [{ cacheKey }, ["mail", "listThreads"], { q: "", folder: "inbox" }]
          trpcQueryKey = queryKey[1];
          queryData = queryKey[2];
        } else if (queryKey[0]?.[0] === 'mail' && queryKey[0]?.[1] === 'listThreads') {
          // 格式: [["mail", "listThreads"], { q: "", folder: "inbox" }]
          trpcQueryKey = queryKey[0];
          queryData = queryKey[1];
        }

        if (trpcQueryKey && trpcQueryKey[0] === 'mail' && trpcQueryKey[1] === 'listThreads') {
          const folder = queryData?.folder;

          // console.log(`[${new Date().toLocaleString('zh-CN')}] 📧 邮件列表查询匹配:`, {
          //   folder,
          //   status: query.state.status,
          //   hasData: !!query.state.data,
          //   queryData
          // });

          if (folder && query.state.data && query.state.status === 'success') {
            // 防止频繁同步，最多每5秒同步一次
            const now = Date.now();
            if (now - lastSyncRef.current < 5000) {
              console.log(`[${new Date().toLocaleString('zh-CN')}] ⏰ 同步频率限制，跳过`);
              return;
            }
            lastSyncRef.current = now;

            console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 检测到用户缓存变化: ${folder}`);

            // 异步执行，避免阻塞
            setTimeout(() => {
              syncUserCacheToManifest(activeConnection.email, folder, query.state.data);
            }, 100);
          }
        }
      }
    });

    return () => {
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🛑 停止缓存监控: ${activeConnection.email}`);
      unsubscribe();
    };
  }, [activeConnection?.email]);

  /**
   * 同步用户缓存到缓存清单
   */
  const syncUserCacheToManifest = async (
    accountId: string,
    folder: string,
    data: any
  ) => {
    // 添加防护措施，避免在组件卸载后执行
    if (!activeConnection?.email || accountId !== activeConnection.email) {
      return;
    }

    try {
      // 计算缓存的邮件数量和提取游标
      let totalEmails = 0;
      let pages = 0;
      let lastCursor = '';

      if (data.pages) {
        // 无限查询数据
        pages = data.pages.length;
        totalEmails = data.pages.reduce((sum: number, page: any) => {
          return sum + (page?.threads?.length || 0);
        }, 0);

        // 提取最后一页的 nextPageToken 作为游标
        const lastPage = data.pages[data.pages.length - 1];
        lastCursor = lastPage?.nextPageToken || '';
      } else if (data.threads) {
        // 单页查询数据
        pages = 1;
        totalEmails = data.threads.length;
        lastCursor = data.nextPageToken || '';
      }

      console.log(`[${new Date().toLocaleString('zh-CN')}] 📊 用户缓存统计: ${folder} - ${totalEmails}封邮件, ${pages}页, cursor: ${lastCursor}`);

      if (totalEmails > 0) {
        // 获取当前清单状态
        const manifest = await cacheManifestManager.getManifest(accountId);
        const currentFolder = manifest?.folders[folder];

        console.log(`[${new Date().toLocaleString('zh-CN')}] 📋 当前清单状态: ${folder} - ${currentFolder?.cachedEmails || 0}封邮件, ${currentFolder?.cachedPages || 0}页`);

        // 只有当用户缓存的数据比清单记录的更多时才更新，避免覆盖轮询缓存的进度
        if (!currentFolder || totalEmails > currentFolder.cachedEmails) {
          await cacheManifestManager.updateFolderProgress(accountId, folder, {
            cachedPages: Math.max(pages, currentFolder?.cachedPages || 0),
            cachedEmails: Math.max(totalEmails, currentFolder?.cachedEmails || 0),
            lastCachedCursor: lastCursor, // 保存正确的游标，让轮询系统能继续
            // 不更新 isCompleted 状态，因为用户可能只是部分浏览
          });

          console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 同步完成: ${folder} - ${totalEmails}封邮件, ${pages}页, cursor: ${lastCursor}`);
        } else {
          console.log(`[${new Date().toLocaleString('zh-CN')}] ⏭️ 跳过同步: ${folder} - 用户缓存(${totalEmails}封)不如清单缓存(${currentFolder.cachedEmails}封)多`);
        }

        console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 同步完成: ${folder} - ${totalEmails}封邮件, ${pages}页, cursor: ${lastCursor}`);
      }
    } catch (error) {
      console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 同步用户缓存失败:`, error);
    }
  };

  /**
   * 手动同步所有缓存
   */
  const syncAllCaches = async () => {
    if (!activeConnection?.email) return;

    console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 开始同步所有用户缓存`);
    
    // 获取所有邮件列表查询
    const threadQueries = queryClient.getQueryCache().findAll({
      predicate: (query) => {
        const key = query.queryKey as any;
        return key[0]?.[0] === 'mail' && key[0]?.[1] === 'listThreads';
      }
    });

    console.log(`[${new Date().toLocaleString('zh-CN')}] 📧 找到 ${threadQueries.length} 个邮件缓存`);

    for (const query of threadQueries) {
      const queryKey = query.queryKey as any;
      const queryData = queryKey[1];
      const folder = queryData?.folder;
      
      if (folder && query.state.data) {
        await syncUserCacheToManifest(activeConnection.email, folder, query.state.data);
      }
    }

    console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 所有用户缓存同步完成`);
  };

  /**
   * 获取用户缓存统计
   */
  const getUserCacheStats = () => {
    if (!activeConnection?.email) return null;

    const threadQueries = queryClient.getQueryCache().findAll({
      predicate: (query) => {
        const key = query.queryKey as any;
        return key[0]?.[0] === 'mail' && key[0]?.[1] === 'listThreads';
      }
    });

    const stats = threadQueries.map(query => {
      const queryKey = query.queryKey as any;
      const queryData = queryKey[1];
      const folder = queryData?.folder || 'unknown';
      
      let totalEmails = 0;
      let pages = 0;
      
      if (query.state.data) {
        const data = query.state.data as any;
        if (data.pages) {
          pages = data.pages.length;
          totalEmails = data.pages.reduce((sum: number, page: any) => {
            return sum + (page?.threads?.length || 0);
          }, 0);
        } else if (data.threads) {
          pages = 1;
          totalEmails = data.threads.length;
        }
      }
      
      return {
        folder,
        pages,
        totalEmails,
        status: query.state.status,
        lastUpdated: query.state.dataUpdatedAt,
      };
    });

    return {
      totalQueries: threadQueries.length,
      totalEmails: stats.reduce((sum, stat) => sum + stat.totalEmails, 0),
      folderStats: stats,
    };
  };

  return {
    syncAllCaches,
    getUserCacheStats,
  };
};
