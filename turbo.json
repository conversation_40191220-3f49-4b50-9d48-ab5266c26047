{"$schema": "https://turbo.build/schema.json", "envMode": "loose", "tasks": {"build": {"dependsOn": ["^build", "sentry:sourcemaps"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["build/**", "!.react-router/**", "dist/**"]}, "dev": {"dependsOn": ["db:studio"], "persistent": true, "cache": false}, "bun:dev": {"dependsOn": ["db:studio"], "persistent": true, "cache": false}, "start": {"cache": false}, "lint": {"outputs": []}, "db:generate": {"cache": false}, "db:migrate": {"cache": false}, "db:push": {"cache": false}, "db:studio": {"cache": false}, "sentry:sourcemaps": {"cache": false}}}