{"name": "@zero/eslint-config", "version": "0.0.0", "private": true, "main": "index.js", "type": "module", "devDependencies": {"@types/eslint": "^9.6.1", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^9.22.0", "eslint-config-next": "^14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.2.0"}}