import {
  type Account,
  type Adapter,
  betterAuth,
  type BetterAuthOptions,
  type GenericEndpointContext,
  type Verification,
} from 'better-auth';
import {
  connection,
  user as _user,
  account,
  session,
} from '@zero/db/schema';
import { createAuthMiddleware, customSession } from 'better-auth/plugins';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { getSocialProviders } from './auth-providers';
import { getContext } from 'hono/context-storage';
import { APIError } from 'better-auth/api';
import { redis, resend } from './services';
import type { HonoContext } from '../ctx';
import { createDriver } from './driver';
import { createDb } from '@zero/db';
import { eq, and, count, desc, sql } from 'drizzle-orm';

const connectionHandlerHook = async (account: Account) => {
  const c = getContext<HonoContext>();

  console.log(`🔗 处理账户连接: ${account.providerId}, 用户: ${account.userId}`);

  if (account.providerId === 'credential') {
    console.log('📧 处理 credential 账户，跳过 OAuth 流程');
    return;
  }

  if (!account.accessToken || !account.refreshToken) {
    console.error('Missing Access/Refresh Tokens', { account });
    throw new APIError('EXPECTATION_FAILED', { message: 'Missing Access/Refresh Tokens' });
  }

  const driver = createDriver(account.providerId, {
    auth: {
      accessToken: account.accessToken,
      refreshToken: account.refreshToken,
      userId: account.userId,
      email: '',
    },
  });

  const userInfo = await driver.getUserInfo().catch(() => {
    throw new APIError('UNAUTHORIZED', { message: 'Failed to get user info' });
  });

  if (!userInfo?.address) {
    console.error('Missing email in user info:', { userInfo });
    throw new APIError('BAD_REQUEST', { message: 'Missing "email" in user info' });
  }

  // 🔧 如果没有 name，使用邮箱 @ 前面的部分作为 name
  const fallbackName = userInfo.address.split('@')[0] || 'Unknown';

  const updatingInfo = {
    name: userInfo.name || fallbackName,
    picture: userInfo.photo || '',
    accessToken: account.accessToken,
    refreshToken: account.refreshToken,
    scope: driver.getScope(),
    expiresAt: new Date(Date.now() + (account.accessTokenExpiresAt?.getTime() || 3600000)),
  };

  await c.var.db
    .insert(connection)
    .values({
      providerId: account.providerId as 'google' | 'microsoft' | 'credential',
      id: crypto.randomUUID(),
      email: userInfo.address,
      userId: account.userId,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...updatingInfo,
    })
    .onConflictDoUpdate({
      target: [connection.email, connection.userId],
      set: {
        ...updatingInfo,
        updatedAt: new Date(),
      },
    });

  console.log(`✅ OAuth 连接创建成功: ${userInfo.address}`);

  //  初始化本地账号存储（OAuth登录）
  console.log(`✅ OAuth登录本地账号存储已初始化: ${userInfo.address}`);
};

let globalAuthDb: ReturnType<typeof createDb> | null = null;

const createAuthConfig = (): BetterAuthOptions => {
  const cache = redis();

  if (!globalAuthDb) {
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL is not defined');
    }
    globalAuthDb = createDb(process.env.DATABASE_URL);
  }
  const db = globalAuthDb;

  return {
    database: drizzleAdapter(db, { provider: 'pg' }),
    secondaryStorage: {
      get: async (key: string) => {
        return (await cache.get(key)) ?? null;
      },
      set: async (key: string, value: string, ttl?: number) => {
        if (ttl) await cache.set(key, value, { ex: ttl });
        else await cache.set(key, value);
      },
      delete: async (key: string) => {
        await cache.del(key);
      },
    },
    advanced: {
      ipAddress: {
        disableIpTracking: true,
      },
      cookiePrefix: process.env.NODE_ENV === 'development' ? 'better-auth-dev' : 'better-auth',
      crossSubDomainCookies: {
        enabled: true,
        domain: process.env.COOKIE_DOMAIN,
      },
    },
    baseURL: process.env.VITE_PUBLIC_BACKEND_URL,
    trustedOrigins: [
      'https://app.0.email',
      'https://sapi.0.email',
      'https://staging.0.email',
      'https://0.email',
      'http://localhost:3000',
      'http://*************:3000',
      'https://0.0.0.0',
      'https://3000-firebase-zero-*************.cluster-3gc7bglotjgwuxlqpiut7yyqt4.cloudworkstations.dev',
    ],
    session: {
      cookieCache: {
        enabled: true,
        maxAge: 60 * 60 * 24 * 7, // 7 days
      },
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
    },
    socialProviders: getSocialProviders(process.env as unknown as Record<string, string>),
    account: {
      accountLinking: {
        enabled: true,
        allowDifferentEmails: true,
        trustedProviders: ['google', 'microsoft', 'credential'],
        allowUnlinkingAll: false,
      },
    },
    hooks: {
      before: createAuthMiddleware(async (ctx) => {
        const c = getContext<HonoContext>();

        // 🎯 在登录验证之前，检查是否需要通过 connection 查找已存在用户
        if (ctx.path.startsWith('/sign-in/email')) {
          const body = ctx.body as any;
          const email = body?.email;
          const password = body?.password;

          if (email && password === 'smtp-imap-user') {
            console.log(`🔧 登录前检查 connection 表: ${email}`);

            // 🎯 先在 connection 表中查找该邮箱，按userId分组统计，选择记录最多的那个
            const connectionStats = await c.var.db
              .select({
                userId: connection.userId,
                connectionCount: count(connection.id).as('connectionCount')
              })
              .from(connection)
              .where(eq(connection.email, email))
              .groupBy(connection.userId)
              .orderBy(desc(count(connection.id)))
              .limit(1);

            if (connectionStats.length > 0) {
              const targetUserId = connectionStats[0]!.userId;
              console.log(`🎯 在 connection 表中找到邮箱 ${email}，选择记录最多的用户ID: ${targetUserId} (记录数: ${connectionStats[0]!.connectionCount})`);


              // 查找该用户的 credential account（163 账户）
              const targetUserCredentialAccount = await c.var.db
                .select()
                .from(account)
                .where(and(
                  eq(account.userId, targetUserId),
                  eq(account.providerId, 'credential')
                ))
                .limit(1);

              if (targetUserCredentialAccount.length > 0) {
                const credentialEmail = targetUserCredentialAccount[0]!.accountId;
                console.log(`🔧 找到目标用户的 credential account: ${credentialEmail}`);

                // 🎯 修改当前登录请求，使用 163 邮箱登录
                console.log(`🔄 将登录邮箱从 ${email} 改为 ${credentialEmail}`);
                (ctx.body as any).email = credentialEmail;

                console.log(`✅ 登录邮箱已修改，将使用已存在的 credential account 登录`);
              } else {
                console.log(`⚠️ 未找到用户 ${targetUserId} 的 credential account`);
              }
            } else {
              console.log(`ℹ️ 在 connection 表中未找到邮箱: ${email}`);
            }
          }
        }
      }),
      after: createAuthMiddleware(async (ctx) => {
        const c = getContext<HonoContext>();
        if (ctx.path.startsWith('/sign-up') || ctx.path.startsWith('/sign-in')) {
          const newSession = ctx.context.newSession;
          if (newSession?.user) {
            console.log(`🔧 检查是否需要修改 credential account: ${newSession.user.email}`);
            try {
              const credentialAccount = await c.var.db
                .select()
                .from(account)
                .where(
                  and(
                    eq(account.userId, newSession.user.id),
                    eq(account.providerId, 'credential')
                  )
                )
                .limit(1);

              if (credentialAccount.length > 0 && credentialAccount[0]?.accountId === newSession.user.id) {
                console.log(`🔧 修改 credential account 的 accountId: ${credentialAccount[0].accountId} → ${newSession.user.email}`);
                await c.var.db
                  .update(account)
                  .set({ accountId: newSession.user.email })
                  .where(eq(account.id, credentialAccount[0].id));
                console.log(`✅ credential account 修改成功`);
              }
            } catch (error) {
              console.error(`❌ 修改 credential account 失败:`, error);
            }
          }
        }

        if (ctx.path.startsWith('/sign-in') || ctx.path.startsWith('/sign-up')) {
          const session = ctx.context.newSession;
          if (session?.user) {
            console.log(`🔄 检查组合账号登录: ${session.user.email}`);

            // 🎯 方法1：检查是否有相同邮箱的 credential account
            const existingCredentialAccount = await c.var.db
              .select()
              .from(account)
              .where(
                and(
                  eq(account.providerId, 'credential'),
                  eq(account.accountId, session.user.email)
                )
              )
              .limit(1);

            if (existingCredentialAccount.length > 0 && existingCredentialAccount[0]?.userId !== session.user.id) {
              const targetUserId = existingCredentialAccount[0]?.userId;
              if (targetUserId) {
                console.log(`🎯 找到相关 credential 账户，目标用户ID: ${targetUserId}`);

                // 🎯 获取当前用户的 credential account 密码，用于更新目标账户
                const currentUserCredentialAccount = await c.var.db
                  .select()
                  .from(account)
                  .where(and(
                    eq(account.userId, session.user.id),
                    eq(account.providerId, 'credential')
                  ))
                  .limit(1);

                if (currentUserCredentialAccount.length > 0) {
                  const newPassword = currentUserCredentialAccount[0]!.password;
                  console.log(`🔧 更新目标用户的 credential account 密码`);

                  // 更新目标用户的 credential account 密码
                  await c.var.db
                    .update(account)
                    .set({ password: newPassword })
                    .where(eq(account.id, existingCredentialAccount[0]!.id));
                }

                await c.var.db
                  .update(account)
                  .set({ userId: targetUserId })
                  .where(eq(account.userId, session.user.id));
                await c.var.db
                  .update(connection)
                  .set({ userId: targetUserId })
                  .where(eq(connection.userId, session.user.id));
                await c.var.db
                  .delete(_user)
                  .where(eq(_user.id, session.user.id));
                console.log(`✅ 通过 credential account 组合账号登录完成: ${session.user.id} → ${targetUserId}`);
                return; // 已处理，直接返回
              }
            }

            // 🎯 方法2：检查是否有相同邮箱的 connection（用于 QQ 登录等情况）
            const existingConnection = await c.var.db
              .select()
              .from(connection)
              .where(eq(connection.email, session.user.email))
              .limit(1);

            if (existingConnection.length > 0 && existingConnection[0]?.userId !== session.user.id) {
              const targetUserId = existingConnection[0]?.userId;
              if (targetUserId) {
                console.log(`🎯 找到相关 connection，目标用户ID: ${targetUserId}`);
                await c.var.db
                  .update(account)
                  .set({ userId: targetUserId })
                  .where(eq(account.userId, session.user.id));
                await c.var.db
                  .update(connection)
                  .set({ userId: targetUserId })
                  .where(eq(connection.userId, session.user.id));
                await c.var.db
                  .delete(_user)
                  .where(eq(_user.id, session.user.id));
                console.log(`✅ 通过 connection 组合账号登录完成: ${session.user.id} → ${targetUserId}`);
                return; // 已处理，直接返回
              }
            }

            // 如果没有找到相关账户
            console.log(`ℹ️ 未找到相关账户: ${session.user.email}`);
          }
        }
      }),
    },
    databaseHooks: {
      account: {
        create: { after: connectionHandlerHook },
        update: { after: connectionHandlerHook },
      },
    },
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false,
      sendResetPassword: async ({ user, url }) => {
        await resend().emails.send({
          from: '0.email <<EMAIL>>',
          to: user.email,
          subject: 'Reset your password',
          html: `
            <h2>Reset Your Password</h2>
            <p>Click the link below to reset your password:</p>
            <a href="${url}">${url}</a>
            <p>If you didn't request this, you can safely ignore this email.</p>
          `,
        });
      },
    },
    emailVerification: {
      sendOnSignUp: false,
      autoSignInAfterVerification: true,
      sendVerificationEmail: async ({ user, token }) => {
        const verificationUrl = `${process.env.VITE_PUBLIC_APP_URL}/api/auth/verify-email?token=${token}&callbackURL=/settings/connections`;
        await resend().emails.send({
          from: '0.email <<EMAIL>>',
          to: user.email,
          subject: 'Verify your 0.email account',
          html: `
            <h2>Verify Your 0.email Account</h2>
            <p>Click the link below to verify your email:</p>
            <a href="${verificationUrl}">${verificationUrl}</a>
          `,
        });
      },
    },
    plugins: [
      customSession(async ({ user, session }) => {
        const foundUser = await db.query.user.findFirst({
          where: eq(_user.id, user.id),
        });

        let activeConnection = null;

        if (foundUser?.defaultConnectionId) {
          const [connectionDetails] = await db
            .select()
            .from(connection)
            .where(eq(connection.id, foundUser.defaultConnectionId))
            .limit(1);

          if (connectionDetails) {
            activeConnection = {
              id: connectionDetails.id,
              name: connectionDetails.name,
              email: connectionDetails.email,
              picture: connectionDetails.picture,
            };
          } else {
            await db
              .update(_user)
              .set({ defaultConnectionId: null })
              .where(eq(_user.id, user.id));
          }
        }

        if (!foundUser?.defaultConnectionId) {
          const [defaultConnection] = await db
            .select()
            .from(connection)
            .where(eq(connection.userId, user.id))
            .limit(1);

          if (defaultConnection) {
            activeConnection = {
              id: defaultConnection.id,
              name: defaultConnection.name,
              email: defaultConnection.email,
              picture: defaultConnection.picture,
            };
          } else {
            const [userAccount] = await db
              .select()
              .from(account)
              .where(eq(account.userId, user.id))
              .limit(1);
            if (userAccount) {
              const newConnectionId = crypto.randomUUID();
              // 🔧 如果没有 name，使用邮箱 @ 前面的部分作为 name
              const fallbackName = user.email.split('@')[0] || 'Unknown';

              await db.insert(connection).values({
                id: newConnectionId,
                userId: user.id,
                email: user.email,
                name: user.name || fallbackName,
                picture: user.image,
                accessToken: userAccount.accessToken,
                refreshToken: userAccount.refreshToken,
                scope: userAccount.scope || 'email',
                providerId: userAccount.providerId as 'google' | 'microsoft' | 'credential',
                expiresAt: new Date(
                  Date.now() + (userAccount.accessTokenExpiresAt?.getTime() || 3600000)
                ),
                createdAt: new Date(),
                updatedAt: new Date(),
              });
              console.warn('Created new connection for user', user.email);
            }
          }
        }

        return {
          connectionId: activeConnection?.id || null,
          activeConnection,
          user,
          session,
        };
      }),
    ],
    user: {
      deleteUser: {
        enabled: true
      }
    },
  };
};

export const createAuth = () => {
  return betterAuth(createAuthConfig());
};

export const createSimpleAuth = () => {
  return betterAuth(createAuthConfig());
};

export type Auth = ReturnType<typeof createAuth>;
export type SimpleAuth = ReturnType<typeof createSimpleAuth>;