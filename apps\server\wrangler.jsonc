{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "zero-server",
  "compatibility_date": "2025-05-01",
  "compatibility_flags": ["nodejs_compat"],
  "main": "src/server.ts",
  "env": {
    "local": {
      "ai": {
        "binding": "AI",
      },
      "vectorize": [
        {
          "binding": "VECTORIZE",
          "index_name": "threads-vector",
        },
        {
          "binding": "VECTORIZE_MESSAGE",
          "index_name": "messages-vector",
        },
      ],
      "durable_objects": {
        "bindings": [
          {
            "class_name": "DurableMailbox",
            "name": "DURABLE_MAILBOX",
          },
        ],
      },
      "migrations": [
        {
          "tag": "v1",
          "new_classes": ["DurableMailbox"],
        },
      ],
      "services": [
        {
          "binding": "zero",
          "service": "zero-worker",
        },
      ],
      "observability": {
        "enabled": true,
      },
      "hyperdrive": [
        {
          "binding": "HYPERDRIVE",
          "id": "57834ddb6716440496c8836f6d99bc9a",
          "localConnectionString": "postgresql://postgres:postgres@localhost:5432/zerodotemail",
        },
      ],
      "vars": {
        "NODE_ENV": "development",
        "COOKIE_DOMAIN": "localhost",
        "VITE_PUBLIC_BACKEND_URL": "http://localhost:8787",
        "VITE_PUBLIC_APP_URL": "http://localhost:3000",
      },
      "kv_namespaces": [
        {
          "binding": "subscribed_accounts",
          "id": "cd3ff4a80734444c98aee76ea9166e3d",
        },
        {
          "binding": "connection_labels",
          "id": "26c5a521b1294ef88d36b96e5617c428",
        },
        {
          "binding": "prompts_storage",
          "id": "1caf4e863e2149519cef97f2ba3c9851",
        },
        {
          "binding": "connection_limits",
          "id": "19d415777f554b7c8fbe6eca7dc87918",
        },
      ],
    },
    "staging": {
      "ai": {
        "binding": "AI",
      },
      "vectorize": [
        {
          "binding": "VECTORIZE",
          "index_name": "threads-vector",
        },
        {
          "binding": "VECTORIZE_MESSAGE",
          "index_name": "messages-vector",
        },
      ],
      "limits": {
        "cpu_ms": 300000,
      },
      "durable_objects": {
        "bindings": [
          {
            "class_name": "DurableMailbox",
            "name": "DURABLE_MAILBOX",
          },
        ],
      },
      "migrations": [
        {
          "tag": "v1",
          "new_classes": ["DurableMailbox"],
        },
      ],
      "services": [
        {
          "binding": "zero",
          "service": "zero-worker",
        },
      ],
      "observability": {
        "enabled": true,
      },
      "hyperdrive": [
        {
          "binding": "HYPERDRIVE",
          "id": "57834ddb6716440496c8836f6d99bc9a",
          "localConnectionString": "postgresql://postgres:postgres@localhost:5432/zerodotemail",
        },
      ],
      "vars": {
        "NODE_ENV": "development",
        "COOKIE_DOMAIN": "0.email",
        "VITE_PUBLIC_BACKEND_URL": "https://sapi.0.email",
        "VITE_PUBLIC_APP_URL": "https://staging.0.email",
      },
      "kv_namespaces": [
        {
          "binding": "subscribed_accounts",
          "id": "cd3ff4a80734444c98aee76ea9166e3d",
        },
        {
          "binding": "connection_labels",
          "id": "26c5a521b1294ef88d36b96e5617c428",
        },
        {
          "binding": "prompts_storage",
          "id": "1caf4e863e2149519cef97f2ba3c9851",
        },
        {
          "binding": "connection_limits",
          "id": "19d415777f554b7c8fbe6eca7dc87918",
        },
      ],
    },
    "production": {
      "ai": {
        "binding": "AI",
      },
      "vectorize": [
        {
          "binding": "VECTORIZE",
          "index_name": "threads-vector",
        },
        {
          "binding": "VECTORIZE_MESSAGE",
          "index_name": "messages-vector",
        },
      ],
      "observability": {
        "enabled": true,
      },
      "hyperdrive": [
        {
          "binding": "HYPERDRIVE",
          "id": "27171042364248898fc8672c0fc532a0",
          "localConnectionString": "postgresql://postgres:postgres@localhost:5432/zerodotemail",
        },
      ],
      "durable_objects": {
        "bindings": [
          {
            "class_name": "DurableMailbox",
            "name": "DURABLE_MAILBOX",
          },
        ],
      },
      "migrations": [
        {
          "tag": "v1",
          "new_classes": ["DurableMailbox"],
        },
      ],
      "services": [
        {
          "binding": "zero",
          "service": "zero-worker",
        },
      ],
      "vars": {
        "NODE_ENV": "production",
        "COOKIE_DOMAIN": "0.email",
        "VITE_PUBLIC_BACKEND_URL": "https://api.0.email",
        "VITE_PUBLIC_APP_URL": "https://0.email",
      },
      "kv_namespaces": [
        {
          "binding": "subscribed_accounts",
          "id": "0528897082354647a1c371d338db13d5",
        },
        {
          "binding": "connection_labels",
          "id": "26c5a521b1294ef88d36b96e5617c428",
        },
        {
          "binding": "prompts_storage",
          "id": "1caf4e863e2149519cef97f2ba3c9851",
        },
        {
          "binding": "connection_limits",
          "id": "19d415777f554b7c8fbe6eca7dc87918",
        },
      ],
    },
  },
}
