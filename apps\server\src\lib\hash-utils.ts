import * as crypto from 'crypto';

const algorithm = 'aes-256-cbc';
const key = Buffer.from(process.env.HASH_KEY, "hex");
const iv = Buffer.from(process.env.HASH_IV, "hex");

console.log('debug hash-util key length:',key.length);

export function encrypt(text: string): string {
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf-8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}

export function decrypt(encryptedText: string): string {
    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf-8');
    decrypted += decipher.final('utf-8');
    return decrypted;
}

// const originalText = 'Sensitive information';
// const encryptedText = encrypt(originalText);
// const decryptedText = decrypt(encryptedText);

// console.log('Original:', originalText);
// console.log('Encrypted:', encryptedText);
// console.log('Decrypted:', decryptedText);