import { useTRPC } from '@/providers/query-provider';
import { useQuery } from '@tanstack/react-query';
import { useSession } from '@/lib/auth-client';

export const useStats = () => {
  const { data: session } = useSession();
  const trpc = useTRPC();

  const statsQuery = useQuery(
    trpc.mail.count.queryOptions(void 0, {
      enabled: !!session?.user.id,
      // ✅ 恢复缓存：每个连接都有独立的QueryClient，不会有统计错乱
      staleTime: 1000 * 60 * 2, // 2分钟内认为数据是新鲜的
      gcTime: 1000 * 60 * 10, // 10分钟后清理未使用的缓存
      refetchOnMount: false, // 挂载时不自动重新获取（使用缓存）
      refetchOnWindowFocus: false, // 窗口聚焦时不自动重新获取
    }),
  );

  return statsQuery;
};
