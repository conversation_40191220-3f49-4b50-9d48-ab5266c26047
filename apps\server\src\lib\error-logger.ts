// IMAP/SMTP错误日志记录工具
import * as fs from 'fs/promises';
import * as path from 'path';

export interface ConnectionConfig {
  host: string;
  port: number;
  secure: boolean;
}

export interface ErrorLogEntry {
  id: string;
  email: string;
  errorType: 'password_error' | 'config_error';
  errorMessage: string;
  connectionType: 'IMAP' | 'SMTP';
  timestamp: string;
  config: ConnectionConfig;
  serverTimestamp: string;
}

/**
 * 记录IMAP/SMTP连接错误日志
 * @param email 用户邮箱
 * @param errorMessage 错误消息
 * @param connectionType 连接类型 (IMAP | SMTP)
 * @param config 连接配置信息
 */
export async function logConnectionError(
  email: string, 
  errorMessage: string, 
  connectionType: 'IMAP' | 'SMTP',
  config: ConnectionConfig
): Promise<void> {
  try {
    // 判断错误类型
    let errorType: 'password_error' | 'config_error' = 'config_error'; // 默认为配置错误
    
    // 密码错误的关键词
    const passwordErrorKeywords = [
      'Authentication Failed',
      'AUTHENTICATIONFAILED', 
      'Invalid credentials',
      'Login fail',
      'Account is abnormal',
      'password is incorrect',
      'Bad username or password',
      'Authentication failure',
      'Invalid login',
      'Wrong password',
      '认证失败',
      '密码错误',
      '登录失败',
      'Command failed',
      'authenticationFailed',
      'login frequency limited',
      'service is not open'
    ];
    
    // 配置错误的关键词
    const configErrorKeywords = [
      'ENOTFOUND',
      'ECONNREFUSED', 
      'ETIMEDOUT',
      'Connection timeout',
      'Host not found',
      'Port',
      'SSL',
      'TLS',
      'connect',
      '连接超时',
      '主机未找到',
      '端口',
      '连接失败',
      'timeout',
      'ECONNRESET',
      'EHOSTUNREACH'
    ];
    
    // 检查是否为密码错误
    if (passwordErrorKeywords.some(keyword => 
      errorMessage.toLowerCase().includes(keyword.toLowerCase())
    )) {
      errorType = 'password_error';
    }
    // 检查是否为配置错误
    else if (configErrorKeywords.some(keyword => 
      errorMessage.toLowerCase().includes(keyword.toLowerCase())
    )) {
      errorType = 'config_error';
    }
    
    const errorLog: ErrorLogEntry = {
      id: generateId(),
      email,
      errorType,
      errorMessage,
      connectionType,
      timestamp: new Date().toISOString(),
      config,
      serverTimestamp: new Date().toISOString()
    };
    
    // 获取当前日期作为文件名
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const logFileName = `imap-login-errors-${dateStr}.json`;
    
    // 确保logs目录存在
    await ensureLogsDirectory();
    
    const logFilePath = path.join('./logs', logFileName);
    
    // 读取现有日志文件或创建新的
    let existingLogs: ErrorLogEntry[] = [];
    try {
      const existingData = await fs.readFile(logFilePath, 'utf-8');
      existingLogs = JSON.parse(existingData);
    } catch (readError) {
      // 文件不存在或格式错误，使用空数组
      existingLogs = [];
    }
    
    // 添加新的错误日志
    existingLogs.push(errorLog);
    
    // 写入文件
    await fs.writeFile(logFilePath, JSON.stringify(existingLogs, null, 2), 'utf-8');
    
    console.log(`📝 [${new Date().toLocaleString('zh-CN')}] ${connectionType}登录错误已记录: ${errorType} - ${email} - ${errorMessage}`);
    
  } catch (error) {
    console.error(`❌ [${new Date().toLocaleString('zh-CN')}] 记录错误日志失败:`, error);
    // 不阻断主流程，静默处理错误
  }
}

/**
 * 确保logs目录存在
 */
async function ensureLogsDirectory(): Promise<void> {
  try {
    await fs.mkdir('./logs', { recursive: true });
  } catch (mkdirError) {
    // 目录可能已存在，忽略错误
  }
}

/**
 * 生成唯一ID
 */
function generateId(): string {
  return Math.random().toString(36).substring(2, 11);
}

/**
 * 读取指定日期的错误日志
 * @param date 日期字符串 (YYYY-MM-DD)，不传则读取今天的日志
 */
export async function readErrorLogs(date?: string): Promise<ErrorLogEntry[]> {
  try {
    const targetDate = date || new Date().toISOString().split('T')[0];
    const logFileName = `imap-login-errors-${targetDate}.json`;
    const logFilePath = path.join('./logs', logFileName);
    
    const data = await fs.readFile(logFilePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // 文件不存在或读取失败，返回空数组
    return [];
  }
}

/**
 * 获取错误日志统计信息
 * @param date 日期字符串 (YYYY-MM-DD)，不传则统计今天的日志
 */
export async function getErrorLogStats(date?: string): Promise<{
  totalLogs: number;
  passwordErrors: number;
  configErrors: number;
  imapErrors: number;
  smtpErrors: number;
  passwordErrorRate: string;
  configErrorRate: string;
}> {
  const logs = await readErrorLogs(date);
  
  const totalLogs = logs.length;
  const passwordErrors = logs.filter(log => log.errorType === 'password_error').length;
  const configErrors = logs.filter(log => log.errorType === 'config_error').length;
  const imapErrors = logs.filter(log => log.connectionType === 'IMAP').length;
  const smtpErrors = logs.filter(log => log.connectionType === 'SMTP').length;
  
  return {
    totalLogs,
    passwordErrors,
    configErrors,
    imapErrors,
    smtpErrors,
    passwordErrorRate: totalLogs > 0 ? (passwordErrors / totalLogs * 100).toFixed(2) + '%' : '0%',
    configErrorRate: totalLogs > 0 ? (configErrors / totalLogs * 100).toFixed(2) + '%' : '0%'
  };
}

/**
 * 获取所有日期的错误日志文件列表
 */
export async function getErrorLogDates(): Promise<string[]> {
  try {
    const logsDir = './logs';
    const files = await fs.readdir(logsDir);
    
    return files
      .filter(file => file.startsWith('imap-login-errors-') && file.endsWith('.json'))
      .map(file => file.replace('imap-login-errors-', '').replace('.json', ''))
      .sort()
      .reverse(); // 最新的在前
  } catch (error) {
    return [];
  }
}
