import { ReSummarizeThread, SummarizeMessage, SummarizeThread } from './brain.fallback.prompts';

import { EPrompts } from '../types';
import { redis } from './services';

const cache = redis();

// 在Bun环境中，我们暂时禁用这些功能，因为它们依赖于Cloudflare Workers的服务绑定 mt:暂时使用redis实现
export const enableBrainFunction = async (connection: { id: string; providerId: string }) => {
  console.log(`[Brain] enableBrainFunction called for connection ${connection.id} (${connection.providerId}) - disabled in Bun environment`);
  await cache.set<string>(`subscribed_accounts:${connection.id}`, 'enable');
  return { success: true, message: 'Brain function disabled in Bun environment' };
};

export const disableBrainFunction = async (connection: { id: string; providerId: string }) => {
  console.log(`[Brain] disableBrainFunction called for connection ${connection.id} (${connection.providerId}) - disabled in Bun environment`);
  await cache.del(`subscribed_accounts:${connection.id}`);
  return { success: true, message: 'Brain function disabled in Bun environment' };
};

// 在Bun环境中使用内存存储替代KV存储
const promptsCache = new Map<string, string>();

// 可选：添加持久化存储功能（使用文件系统）
const PROMPTS_STORAGE_FILE = './data/prompts.json';

// 初始化时加载已保存的提示词
const loadPromptsFromFile = async () => {
  try {
    const fs = await import('fs/promises');
    const data = await fs.readFile(PROMPTS_STORAGE_FILE, 'utf-8');
    const savedPrompts = JSON.parse(data);
    for (const [key, value] of Object.entries(savedPrompts)) {
      promptsCache.set(key, value as string);
    }
    console.log(`[Brain] Loaded ${Object.keys(savedPrompts).length} prompts from file`);
  } catch (error) {
    console.log(`[Brain] No existing prompts file found, starting fresh`);
  }
};

// 保存提示词到文件
const savePromptsToFile = async () => {
  try {
    const fs = await import('fs/promises');
    const path = await import('path');

    // 确保目录存在
    const dir = path.dirname(PROMPTS_STORAGE_FILE);
    await fs.mkdir(dir, { recursive: true });

    // 保存到文件
    const promptsObject = Object.fromEntries(promptsCache);
    await fs.writeFile(PROMPTS_STORAGE_FILE, JSON.stringify(promptsObject, null, 2));
    console.log(`[Brain] Saved ${promptsCache.size} prompts to file`);
  } catch (error) {
    console.error(`[Brain] Failed to save prompts to file:`, error);
  }
};

// 初始化加载
loadPromptsFromFile();

const getPromptName = (connectionId: string, prompt: EPrompts) => {
  return `${connectionId}-${prompt}`;
};

export const getPrompt = async (promptName: string, fallback: string) => {
  console.log(`[Brain] Getting prompt: ${promptName}`);

  // 在Bun环境中，使用内存缓存替代KV存储
  const existingPrompt = promptsCache.get(promptName);
  if (!existingPrompt) {
    console.log(`[Brain] Prompt not found, using fallback for: ${promptName}`);
    promptsCache.set(promptName, fallback);
    // 异步保存到文件，不阻塞返回
    savePromptsToFile().catch(console.error);
    return fallback;
  }
  return existingPrompt;
};

export const getPrompts = async ({ connectionId }: { connectionId: string }) => {
  const prompts: Record<EPrompts, string> = {
    [EPrompts.SummarizeMessage]: '',
    [EPrompts.ReSummarizeThread]: '',
    [EPrompts.SummarizeThread]: '',
    // [EPrompts.ThreadLabels]: '',
    // [EPrompts.Chat]: '',
  };
  const fallbackPrompts = {
    [EPrompts.SummarizeMessage]: SummarizeMessage,
    [EPrompts.ReSummarizeThread]: ReSummarizeThread,
    [EPrompts.SummarizeThread]: SummarizeThread,
    // [EPrompts.ThreadLabels]: '',
    // [EPrompts.Chat]: '',
  };
  for (const promptType of Object.values(EPrompts)) {
    const promptName = getPromptName(connectionId, promptType);
    const prompt = await getPrompt(promptName, fallbackPrompts[promptType]);
    prompts[promptType] = prompt;
  }
  return prompts;
};
