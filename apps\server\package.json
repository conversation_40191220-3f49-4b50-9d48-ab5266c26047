{"name": "@zero/server", "type": "module", "private": true, "scripts": {"dev": "wrangler dev --show-interactive-dev-session=false --experimental-vectorize-bind-to-prod --env local", "deploy": "wrangler deploy", "types": "wrangler types --env local", "bun:dev": "bun --env-file=.env --watch ./src/server.ts", "bun:dev:hot": "bun --env-file=.env --watch --hot run ./src/server.ts", "bun:print-env": "bun --env-file=.env --print process.env"}, "exports": {"./trpc": "./src/trpc/index.ts", "./auth": "./src/lib/auth.ts", "./auth-providers": "./src/lib/auth-providers.ts"}, "dependencies": {"@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.21", "@coinbase/cookie-manager": "1.1.8", "@googleapis/gmail": "12.0.0", "@googleapis/people": "3.0.9", "@hono/trpc-server": "^0.3.4", "@microsoft/microsoft-graph-client": "^3.0.7", "@microsoft/microsoft-graph-types": "^2.40.0", "@react-email/components": "^0.0.41", "@react-email/render": "^1.1.0", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "@zero/db": "workspace:*", "ai": "^4.3.13", "autumn-js": "catalog:", "base64-js": "1.5.1", "better-auth": "catalog:", "date-fns": "^4.1.0", "dedent": "^1.6.0", "drizzle-orm": "0.43.1", "email-addresses": "^5.0.0", "google-auth-library": "9.15.1", "he": "^1.2.0", "hono": "^4.7.8", "hono-party": "^0.0.12", "jsonrepair": "^3.12.0", "mimetext": "^3.0.27", "p-retry": "6.2.1", "partyserver": "^0.0.71", "react": "19.0.0", "remeda": "2.21.3", "resend": "^4.5.1", "sanitize-html": "^2.16.0", "string-strip-html": "^13.4.12", "superjson": "catalog:", "wrangler": "catalog:", "zod": "catalog:"}, "devDependencies": {"@types/he": "1.2.3", "@types/node": "^22.9.0", "@types/react": "19.0.10", "@types/sanitize-html": "2.13.0", "typescript": "catalog:"}}