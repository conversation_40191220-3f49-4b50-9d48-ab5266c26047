import { useTRPC } from '@/providers/query-provider';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router';

export const useSummary = (threadId: string | null) => {
  const trpc = useTRPC();
  const { folder } = useParams<{ folder: string }>();
  const summaryQuery = useQuery(
    trpc.brain.generateSummary.queryOptions(
      { threadId: threadId!, folder: folder??'' },
      {
        enabled: !!threadId,
      },
    ),
  );

  return summaryQuery;
};

export const useBrainState = () => {
  const trpc = useTRPC();
  const brainStateQuery = useQuery(trpc.brain.getState.queryOptions());

  return brainStateQuery;
};
