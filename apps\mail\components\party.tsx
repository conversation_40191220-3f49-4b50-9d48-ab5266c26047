import { useActiveConnection } from '@/hooks/use-connections';
import { useQueryClient } from '@tanstack/react-query';
import { useTRPC } from '@/providers/query-provider';
import { usePartySocket } from 'partysocket/react';
import { useThreads } from '@/hooks/use-threads';
import { useLabels } from '@/hooks/use-labels';
import { useSession } from '@/lib/auth-client';
import { funnel } from 'remeda';
import { useEffect, useRef } from 'react';

const DEBOUNCE_DELAY = 10_000; // 10 seconds is appropriate for real-time notifications

export const NotificationProvider = ({ headers }: { headers: Record<string, string> }) => {
  const trpc = useTRPC();
  const { refetch: refetchLabels } = useLabels();
  const queryClient = useQueryClient();
  const [{ refetch: refetchThreads }] = useThreads();
  const { data: activeConnection } = useActiveConnection();
  const wsConnectedRef = useRef(false);

  //   const handleRefetchLabels = useCallback(async () => {
  //     await refetchLabels();
  //   }, [refetchLabels]);

  //   const handleRefetchThreads = useCallback(async () => {
  //     await refetchThreads();
  //   }, [refetchThreads]);

  const labelsDebouncer = funnel(
    () => queryClient.invalidateQueries({ queryKey: trpc.labels.list.queryKey() }),
    { minQuietPeriodMs: DEBOUNCE_DELAY },
  );
  const threadsDebouncer = funnel(
    () => queryClient.invalidateQueries({ queryKey: trpc.mail.listThreads.queryKey() }),
    { minQuietPeriodMs: DEBOUNCE_DELAY },
  );

  usePartySocket({
    party: 'durable-mailbox',
    room: activeConnection?.id ? `${activeConnection.id}` : 'general',
    prefix: 'zero',
    debug: true,
    maxRetries: 5, // 增加重试次数
    retryBackoff: true, // 启用指数退避重试
    query: {
      token: headers['cookie'],
    },
    host: import.meta.env.VITE_PUBLIC_BACKEND_URL!,
    onClose: (event) => {
      console.warn(`[${new Date().toLocaleString('zh-CN')}] 🔌 WebSocket连接关闭 - 代码: ${event.code}, 原因: ${event.reason}`);
      wsConnectedRef.current = false;
    },
    onError: (error) => {
      console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ WebSocket连接错误:`, error);
      wsConnectedRef.current = false;
    },
    onOpen: () => {
      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ WebSocket连接已建立 - 房间: ${activeConnection?.id || 'general'}`);
      wsConnectedRef.current = true;
    },
    onMessage: async (message: MessageEvent<string>) => {
      try {
        console.warn('party message', message);
        const { threadIds, type } = JSON.parse(message.data);
        if (type === 'refresh') {
          labelsDebouncer.call();
          await Promise.all(
            threadIds.map(async (threadId: string) => {
              await queryClient.invalidateQueries({
                queryKey: trpc.mail.get.queryKey({ id: threadId }),
              });
            }),
          );
          console.warn('refetched labels & threads', threadIds);
        } else if (type === 'list') {
          threadsDebouncer.call();
          labelsDebouncer.call();
          await Promise.all(
            threadIds.map(async (threadId: string) => {
              await queryClient.invalidateQueries({
                queryKey: trpc.mail.get.queryKey({ id: threadId }),
              });
            }),
          );
          console.warn('refetched threads, added', threadIds);
        }
      } catch (error) {
        console.error('error parsing party message', error);
      }
    },
  });

  // 备用定时刷新机制：如果WebSocket连接失败，每2分钟自动刷新一次
  useEffect(() => {
    const interval = setInterval(() => {
      if (!wsConnectedRef.current) {
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 WebSocket未连接，执行备用刷新`);
        threadsDebouncer.call();
        labelsDebouncer.call();
      }
    }, 15 * 60 * 1000); // 15分钟

    return () => clearInterval(interval);
  }, [threadsDebouncer, labelsDebouncer]);

  return <></>;
};
