import {
  PersistQueryClientProvider,
  type PersistedClient,
  type Persister,
} from '@tanstack/react-query-persist-client';
import { createTRPCClient, httpBatchLink, loggerLink } from '@trpc/client';
import { QueryCache, QueryClient, hashKey } from '@tanstack/react-query';
import { createTRPCContext } from '@trpc/tanstack-react-query';
import { useMemo, useState, useEffect, type PropsWithChildren } from 'react';
import type { AppRouter } from '@zero/server/trpc';
import { CACHE_BURST_KEY } from '@/lib/constants';
import { signOut } from '@/lib/auth-client';
import { get, set, del } from 'idb-keyval';
import superjson from 'superjson';
import { getActiveEmail, setActiveOnLogin } from '@/lib/local-account-storage';

function createIDBPersister(idbValidKey: IDBValidKey = 'zero-query-cache') {
  return {
    persistClient: async (client: PersistedClient) => {
      await set(idbValidKey, client);
    },
    restoreClient: async () => {
      return await get<PersistedClient>(idbValidKey);
    },
    removeClient: async () => {
      await del(idbValidKey);
    },
  } satisfies Persister;
}

export const makeQueryClient = (userEmail: string | null) => {
  const queryClient = new QueryClient({
    queryCache: new QueryCache({
      onError: (err, { meta }) => {
        if (meta && meta.noGlobalError === true) return;
        if (meta && typeof meta.customError === 'string') console.error(meta.customError);
        else if (
          err.message === 'Required scopes missing' ||
          err.message.includes('Invalid connection')
        ) {
          signOut({
            fetchOptions: {
              onSuccess: () => {
                if (window.location.href.includes('/login')) return;
                window.location.href = '/login?error=required_scopes_missing';
              },
            },
          });
        } else console.error(err.message || 'Something went wrong');
      },

    }),
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
        queryKeyHashFn: (queryKey) => hashKey([{ userEmail }, ...queryKey]),
        staleTime: 1000 * 60 * 60 * 5, // 5小时认为数据是新鲜的，便于调试
        gcTime: 1000 * 60 * 30, // 30分钟后清理未使用的缓存
      },
      mutations: {
        onError: (err) => console.error(err.message),
      },
    },
  });

  return queryClient;
};

// 🎯 多 QueryClient 管理器 - 每个邮箱账号一个独立的 QueryClient
let queryClientPool = new Map<string, {
  queryClient: QueryClient;
  persister: Persister;
  lastUsed: number;
}>();

const getOrCreateQueryClient = (email: string) => {
  if (typeof window === 'undefined') {
    return {
      queryClient: makeQueryClient(email),
      persister: createIDBPersister(`zero-query-cache-${email}`)
    };
  }

  const cacheKey = email || 'default';

  if (!queryClientPool.has(cacheKey)) {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🆕 为邮箱 ${email} 创建新的 QueryClient`);

    const queryClient = makeQueryClient(email);
    const persister = createIDBPersister(`zero-query-cache-${cacheKey}`);

    queryClientPool.set(cacheKey, {
      queryClient,
      persister,
      lastUsed: Date.now()
    });
  } else {
    console.log(`[${new Date().toLocaleString('zh-CN')}] ♻️ 复用邮箱 ${email} 的 QueryClient`);
    // 更新最后使用时间
    const existing = queryClientPool.get(cacheKey)!;
    existing.lastUsed = Date.now();
  }

  return queryClientPool.get(cacheKey)!;
};

// 🎯 获取所有活跃的 QueryClient（用于调试）
export const getActiveQueryClients = () => {
  return Array.from(queryClientPool.entries()).map(([email, { lastUsed }]) => ({
    email,
    lastUsed: new Date(lastUsed).toLocaleString('zh-CN')
  }));
};

// 🎯 清理长时间未使用的 QueryClient（可选的内存优化）
export const cleanupUnusedQueryClients = (maxAge = 1000 * 60 * 30) => { // 30分钟
  const now = Date.now();
  const toDelete: string[] = [];

  queryClientPool.forEach((value, key) => {
    if (now - value.lastUsed > maxAge && key !== 'default') {
      toDelete.push(key);
    }
  });

  toDelete.forEach(key => {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 清理未使用的 QueryClient: ${key}`);
    queryClientPool.delete(key);
  });

  return toDelete.length;
};

const getUrl = () => import.meta.env.VITE_PUBLIC_BACKEND_URL + '/api/trpc';

export const { TRPCProvider, useTRPC, useTRPCClient } = createTRPCContext<AppRouter>();

export const trpcClient = createTRPCClient<AppRouter>({
  links: [
    loggerLink({
      enabled: () => true,
      logger: (opts) => {
        const { direction, type, path } = opts;
        if (direction === 'down' && type === 'query') {
          console.log(`[${new Date().toLocaleString('zh-CN')}] 🔵 网络请求 - ${path}`);
        }
        if (direction === 'up' && type === 'query') {
          console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 网络响应 - ${path}`);
        }
      }
    }),
    httpBatchLink({
      transformer: superjson,
      url: getUrl(),
      methodOverride: 'POST',
      maxItems: 1,
      fetch: (url, options) =>
        fetch(url, { ...options, credentials: 'include' }).then((res) => {
          const currentPath = new URL(window.location.href).pathname;
          const redirectPath = res.headers.get('X-Zero-Redirect');
          if (!!redirectPath && redirectPath !== currentPath) window.location.href = redirectPath;
          return res;
        }),
    }),
  ],
});

export function QueryProvider({
  children,
  userEmail,
}: PropsWithChildren<{ userEmail: string | null }>) {
  // 🎯 智能初始化：避免不必要的缓存切换
  const initialEmail = useMemo(() => {
    const localEmail = getActiveEmail();

    // 🎯 优先级：传入的用户邮箱 > 本地存储邮箱 > default
    // 这样确保登录时立即使用正确的邮箱，避免"顿一下"现象
    const finalEmail = userEmail || localEmail || 'default';

    // 🎯 如果传入了用户邮箱，且与本地存储不同，立即更新本地存储
    if (userEmail && userEmail !== localEmail) {
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔧 登录邮箱与本地存储不同，立即更新: ${localEmail} -> ${userEmail}`);
      try {
        setActiveOnLogin(userEmail);
      } catch (error) {
        console.warn('更新本地存储失败:', error);
      }
    }

    console.log(`[${new Date().toLocaleString('zh-CN')}] 🎯 QueryProvider初始化邮箱: ${finalEmail}`);
    return finalEmail;
  }, [userEmail]);

  const [effectiveEmail, setEffectiveEmail] = useState<string | null>(initialEmail);

  // 🎯 直接监听邮箱切换事件
  useEffect(() => {
    // 监听用户主动切换邮箱
    const handleSwitchToEmail = (e: CustomEvent) => {
      const newEmail = e.detail.email;
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 用户切换邮箱: ${effectiveEmail} -> ${newEmail}`);
      console.log(`[${new Date().toLocaleString('zh-CN')}] 📊 当前活跃的 QueryClient:`, getActiveQueryClients());
      setEffectiveEmail(newEmail);
    };

    window.addEventListener('switchToEmail', handleSwitchToEmail as EventListener);
    return () => window.removeEventListener('switchToEmail', handleSwitchToEmail as EventListener);
  }, [effectiveEmail]);

  // 🎯 使用多 QueryClient 管理器
  const { queryClient, persister } = useMemo(() => {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🔧 创建/获取 QueryClient: ${effectiveEmail || 'default'}`);
    const client = getOrCreateQueryClient(effectiveEmail || 'default');

    // 🎯 添加缓存状态监听
    const cache = client.queryClient.getQueryCache();
    console.log(`[${new Date().toLocaleString('zh-CN')}] 📊 当前缓存中的查询数量: ${cache.getAll().length}`);

    return client;
  }, [effectiveEmail]);

  // 🎯 开发环境下添加全局调试工具
  useEffect(() => {
    if (typeof window !== 'undefined' && import.meta.env.DEV) {
      (window as any).__ZERO_QUERY_DEBUG__ = {
        getActiveQueryClients,
        cleanupUnusedQueryClients,
        getCurrentEmail: () => effectiveEmail,
        getQueryClientPool: () => queryClientPool
      };
    }
  }, [effectiveEmail]);

  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister,
        buster: CACHE_BURST_KEY,
        maxAge: 1000 * 60 * 60 * 24, // 24 hours for better caching
      }}
    >
      <TRPCProvider trpcClient={trpcClient} queryClient={queryClient}>
        {children}
      </TRPCProvider>
    </PersistQueryClientProvider>
  );
}