import { z } from 'zod';

export const shortcutSchema = z.object({
  keys: z.array(z.string()),
  action: z.string(),
  type: z.enum(['single', 'combination']),
  description: z.string(),
  scope: z.string(),
  preventDefault: z.boolean().optional(),
  ignore: z.boolean().optional(),
});

export type Shortcut = z.infer<typeof shortcutSchema>;
export type ShortcutType = Shortcut['type'];

const threadDisplayShortcuts: Shortcut[] = [
  // {
  //   keys: ['i'],
  //   action: 'viewEmailDetails',
  //   type: 'single',
  //   description: 'View email details',
  //   scope: 'thread-display',
  // },
  // {
  //   keys: ['mod', 'p'],
  //   action: 'printEmail',
  //   type: 'combination',
  //   description: 'Print email',
  //   scope: 'thread-display',
  // },
  {
    keys: ['r'],
    action: 'reply',
    type: 'single',
    description: 'Reply to email',
    scope: 'thread-display',
  },
  {
    keys: ['a'],
    action: 'replyAll',
    type: 'single',
    description: 'Reply all',
    scope: 'thread-display',
  },
  {
    keys: ['f'],
    action: 'forward',
    type: 'single',
    description: 'Forward email',
    scope: 'thread-display',
  },
  {
    keys: ['meta', 'backspace'],
    action: 'delete',
    type: 'single',
    description: 'Move to Bin',
    scope: 'thread-display',
  },
];

const navigation: Shortcut[] = [
  {
    keys: ['g', 'd'],
    action: 'goToDrafts',
    type: 'combination',
    description: 'Go to drafts',
    scope: 'navigation',
  },
  {
    keys: ['g', 'i'],
    action: 'inbox',
    type: 'combination',
    description: 'Go to inbox',
    scope: 'navigation',
  },
  {
    keys: ['g', 't'],
    action: 'sentMail',
    type: 'combination',
    description: 'Go to sent mail',
    scope: 'navigation',
  },
  {
    keys: ['g', 's'],
    action: 'goToSettings',
    type: 'combination',
    description: 'Go to general settings',
    scope: 'navigation',
  },
  {
    keys: ['g', 'a'],
    action: 'goToArchive',
    type: 'combination',
    description: 'Go to archive',
    scope: 'navigation',
  },
  {
    keys: ['g', 'b'],
    action: 'goToBin',
    type: 'combination',
    description: 'Go to bin',
    scope: 'navigation',
  },
];

const globalShortcuts: Shortcut[] = [
  {
    keys: ['mod', '/'],
    action: 'search',
    type: 'combination',
    description: 'Search',
    scope: 'global',
  },
  // {
  //   keys: ['?'],
  //   action: 'helpWithShortcuts',
  //   type: 'single',
  //   description: 'Show keyboard shortcuts',
  //   scope: 'global',
  // },
  // {
  //   keys: ['z'],
  //   action: 'undoLastAction',
  //   type: 'single',
  //   description: 'Undo last action',
  //   scope: 'global',
  // },
  {
    keys: ['c'],
    action: 'newEmail',
    type: 'single',
    description: 'Compose new email',
    scope: 'global',
    preventDefault: true,
  },
  {
    keys: ['mod', 'k'],
    action: 'commandPalette',
    type: 'combination',
    description: 'Open command palette',
    scope: 'global',
  },
];

const mailListShortcuts: Shortcut[] = [
  {
    keys: ['r'],
    action: 'markAsRead',
    type: 'single',
    description: 'Mark as read',
    scope: 'mail-list',
  },
  {
    keys: ['u'],
    action: 'markAsUnread',
    type: 'single',
    description: 'Mark as unread',
    scope: 'mail-list',
  },
  // {
  //   keys: ['i'],
  //   action: 'markAsImportant',
  //   type: 'single',
  //   description: 'Mark as important',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: ['a'],
  //   action: 'bulkArchive',
  //   type: 'single',
  //   description: 'Bulk archive',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: ['d'],
  //   action: 'bulkDelete',
  //   type: 'single',
  //   description: 'Bulk delete',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: ['s'],
  //   action: 'bulkStar',
  //   type: 'single',
  //   description: 'Bulk star',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: ['u'],
  //   action: 'bulkUnstar',
  //   type: 'single',
  //   description: 'Bulk unstar',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: [''],
  //   action: 'exitSelectionMode',
  //   type: 'single',
  //   description: 'Exit selection mode',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: ['m'],
  //   action: 'muteThread',
  //   type: 'single',
  //   description: 'Mute thread',
  //   scope: 'mail-list',
  // },
  {
    keys: ['e'],
    action: 'archiveEmail',
    type: 'single',
    description: 'Archive email',
    scope: 'mail-list',
  },
  {
    keys: ['escape'],
    action: 'exitSelectionMode',
    type: 'single',
    description: 'Exit selection mode',
    scope: 'mail-list',
  },
  // {
  //   keys: ['!'],
  //   action: 'markAsSpam',
  //   type: 'single',
  //   description: 'Mark as spam',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: ['v'],
  //   action: 'moveToFolder',
  //   type: 'single',
  //   description: 'Move to folder',
  //   scope: 'mail-list',
  // },

  // {
  //   keys: ['o'],
  //   action: 'expandEmailView',
  //   type: 'single',
  //   description: 'Expand email view',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: ['#'],
  //   action: 'delete',
  //   type: 'single',
  //   description: 'Delete email',
  //   scope: 'mail-list',
  // },
  {
    keys: ['mod', 'a'],
    action: 'selectAll',
    type: 'combination',
    description: 'Select all emails',
    scope: 'mail-list',
    preventDefault: true,
  },
  // {
  //   keys: ['j'],
  //   action: 'scrollDown',
  //   type: 'single',
  //   description: 'Scroll down',
  //   scope: 'mail-list',
  // },
  // {
  //   keys: ['k'],
  //   action: 'scrollUp',
  //   type: 'single',
  //   description: 'Scroll up',
  //   scope: 'mail-list',
  // },
  {
    keys: ['1'],
    action: 'showImportant',
    type: 'single',
    description: 'Show important',
    scope: 'mail-list',
  },
  {
    keys: ['2'],
    action: 'showAllMail',
    type: 'single',
    description: 'Show all mail',
    scope: 'mail-list',
  },
  {
    keys: ['3'],
    action: 'showPersonal',
    type: 'single',
    description: 'Show personal',
    scope: 'mail-list',
  },
  {
    keys: ['4'],
    action: 'showUpdates',
    type: 'single',
    description: 'Show updates',
    scope: 'mail-list',
  },
  {
    keys: ['5'],
    action: 'showPromotions',
    type: 'single',
    description: 'Show promotions',
    scope: 'mail-list',
  },
  {
    keys: ['6'],
    action: 'showUnread',
    type: 'single',
    description: 'Show unread',
    scope: 'mail-list',
  },
  {
    keys: ['alt', 'shift', 'click'],
    action: 'selectUnderCursor',
    type: 'combination',
    description: 'Select under cursor',
    scope: 'mail-list',
    ignore: true,
  },
];

const composeShortcuts: Shortcut[] = [
  {
    keys: ['mod', 'Enter'],
    action: 'sendEmail',
    type: 'combination',
    description: 'Send email',
    scope: 'compose',
  },
  {
    keys: ['escape'],
    action: 'closeCompose',
    type: 'single',
    description: 'Close compose',
    scope: 'compose',
  },
];

export const keyboardShortcuts: Shortcut[] = [
  ...navigation,
  ...threadDisplayShortcuts,
  ...globalShortcuts,
  ...mailListShortcuts,
  ...composeShortcuts,
];
