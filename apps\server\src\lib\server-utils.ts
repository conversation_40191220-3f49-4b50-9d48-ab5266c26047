import { connection, user } from '@zero/db/schema';
import { getContext } from 'hono/context-storage';
import type { HonoContext } from '../ctx';
import { createDriver } from './driver';
import { and, eq } from 'drizzle-orm';

export const getActiveConnection = async () => {
  const c = getContext<HonoContext>();
  const { session, db } = c.var;
  if (!session?.user) throw new Error('Session Not Found');

  const userData = await db.query.user.findFirst({
    where: eq(user.id, session.user.id),
  });

  if (userData?.defaultConnectionId) {
    const activeConnection = await db.query.connection.findFirst({
      where: and(
        eq(connection.userId, session.user.id),
        eq(connection.id, userData.defaultConnectionId),
      ),
    });
    if (activeConnection) {
      // Validate connection before returning
      validateConnection(activeConnection);
      return activeConnection;
    }
  }

  const firstConnection = await db.query.connection.findFirst({
    where: and(eq(connection.userId, session.user.id)),
  });
  if (!firstConnection) {
    console.error(`No connections found for user ${session.user.id}`);
    throw new Error('No connections found for user');
  }

  // Validate connection based on provider type
  validateConnection(firstConnection);

  return firstConnection;
};

// Helper function to validate connections based on provider type
const validateConnection = (activeConnection: typeof connection.$inferSelect) => {
  console.log(`Validating connection for provider: ${activeConnection.providerId}, email: ${activeConnection.email}`);
  console.log(`Connection details: accessToken=${!!activeConnection.accessToken}, refreshToken=${!!activeConnection.refreshToken}`);

  // Different connection types have different authentication requirements
  if (activeConnection.providerId === 'credential') {
    // For IMAP/SMTP connections, we only need the refreshToken which contains the password
    if (!activeConnection.refreshToken) {
      console.error('IMAP/SMTP connection validation failed: missing refreshToken');
      throw new Error('IMAP/SMTP connection is missing password, please reconnect');
    }
    console.log('IMAP/SMTP connection validation passed');
  } else if (
    activeConnection.providerId === 'google' ||
    activeConnection.providerId === 'microsoft'
  ) {
    // For OAuth providers, we need both refreshToken and accessToken
    if (!activeConnection.refreshToken || !activeConnection.accessToken) {
      console.error('OAuth connection validation failed: missing tokens');
      throw new Error(
        'OAuth connection is not properly authorized, please reconnect the connection',
      );
    }
    console.log('OAuth connection validation passed');
  } else {
    // For other providers, make a general check
    console.log(`Unknown provider: ${activeConnection.providerId}, applying general validation`);
    if (!activeConnection.refreshToken || !activeConnection.accessToken) {
      console.error('General connection validation failed: missing tokens');
      throw new Error('Connection is not properly authorized, please reconnect the connection');
    }
    console.log('General connection validation passed');
  }
};

export const connectionToDriver = (activeConnection: typeof connection.$inferSelect) => {
  // 🎯 预定义的 IMAP provider 列表
  const imapProviders = [
    'credential',
    'imap-163-com',
    'imap-qq-com',
    'imap-gmail-com',
    'imap-126-com',
    'imap-sina-com',
    'imap-sohu-com',
    'imap-yeah-net',
    'imap-onemails-ai'
  ];

  // For IMAP/SMTP connections, we need to pass additional configuration from the scope field
  if (imapProviders.includes(activeConnection.providerId)) {
    // For IMAP/SMTP connections, we only need the refreshToken which contains the password
    if (!activeConnection.refreshToken) {
      throw new Error('IMAP/SMTP connection is missing password, please reconnect');
    }

    try {
      // Parse the IMAP/SMTP settings from the scope field
      const config = JSON.parse(activeConnection.scope);

      // 🎯 对于所有 IMAP provider，都使用 'credential' 作为实际的 driver 类型
      const driver = createDriver('credential', {
        auth: {
          accessToken: activeConnection.accessToken || '',
          refreshToken: activeConnection.refreshToken,
          email: activeConnection.email,
          // Pass through the IMAP/SMTP configuration
          ...config.auth,
        },
      });
      return driver;
    } catch (error) {
      console.error('Error parsing IMAP/SMTP configuration:', error);
      throw new Error('Invalid IMAP/SMTP configuration');
    }
  } else {
    // For OAuth providers, we need both refreshToken and accessToken
    if (!activeConnection.accessToken || !activeConnection.refreshToken) {
      throw new Error('OAuth connection is not properly authorized, please reconnect the connection');
    }

    // Regular OAuth connections
    return createDriver(activeConnection.providerId, {
      auth: {
        userId: activeConnection.userId,
        accessToken: activeConnection.accessToken,
        refreshToken: activeConnection.refreshToken,
        email: activeConnection.email,
      },
    });
  }
};
