import { drizzle } from 'drizzle-orm/postgres-js';
import * as schema from './schema';
import postgres from 'postgres';

// 连接池缓存，避免创建过多连接
const connectionCache = new Map<string, ReturnType<typeof drizzle<typeof schema>>>();

export const createDb = (url: string) => {
  // 检查是否已有缓存的连接
  if (connectionCache.has(url)) {
    return connectionCache.get(url)!;
  }

  // 创建新连接，配置连接池参数
  const conn = postgres(url, {
    max: 10, // 最大连接数
    idle_timeout: 20, // 空闲超时时间（秒）
    max_lifetime: 60 * 30, // 连接最大生存时间（秒）
  });

  const db = drizzle(conn, { schema, logger: false });

  // 缓存连接
  connectionCache.set(url, db);

  return db;
};

export type DB = ReturnType<typeof createDb>;
