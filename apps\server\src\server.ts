//import { env, WorkerEntrypoint } from 'cloudflare:workers';

// 🕐 全局时间戳日志功能 - 重写所有 console 方法（带颜色编码）
const originalConsole = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug,
};

// ANSI 颜色代码
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  gray: '\x1b[90m',
};

const addTimestamp = (level: string, color: string, ...args: any[]) => {
  // 获取中国时区时间 (UTC+8)
  const now = new Date();
  const chinaTime = new Date(now.getTime() + 8 * 60 * 60 * 1000); // UTC+8
  const timestamp = chinaTime.toISOString().replace('T', ' ').replace('Z', '').slice(0, 19);

  const coloredTimestamp = `${colors.gray}[${timestamp} CST]${colors.reset}`;
  const coloredLevel = `${color}[${level}]${colors.reset}`;
  return [coloredTimestamp, coloredLevel, ...args];
};

// 重写 console 方法（带颜色）
console.log = (...args: any[]) =>
  originalConsole.log(...addTimestamp('LOG', colors.green, ...args));
console.error = (...args: any[]) =>
  originalConsole.error(...addTimestamp('ERROR', colors.red, ...args));
console.warn = (...args: any[]) =>
  originalConsole.warn(...addTimestamp('WARN', colors.yellow, ...args));
console.info = (...args: any[]) =>
  originalConsole.info(...addTimestamp('INFO', colors.cyan, ...args));
console.debug = (...args: any[]) =>
  originalConsole.debug(...addTimestamp('DEBUG', colors.magenta, ...args));

// 启动日志
console.log('🚀 Zero Mail Server 启动中...');

import { contextStorage } from 'hono/context-storage';
import { routePartykitRequest } from 'partyserver';
import { DurableMailbox } from './lib/party.js';
// import { autumnApi } from './routes/autumn.js';
import { trpcServer } from '@hono/trpc-server';
import { chatHandler } from './routes/chat.js';
import { appRouter } from './trpc/index.js';
import type { HonoContext } from './ctx.js';
import { createAuth } from './lib/auth.js';
import { createDb } from '@zero/db';
// import { Autumn } from 'autumn-js';
import { cors } from 'hono/cors';
import { Hono } from 'hono';
import { connection, userUsageDaily, user as user_ } from '@zero/db/schema';
import { eq, and, sql } from 'drizzle-orm';

// 创建全局数据库实例，避免重复创建连接
export const globalDb = createDb(process.env.DATABASE_URL!);

// 支付相关变量
export const subscribePayApiSecret = '6s9flrez7ym3z13v';
export const subscribePayServiceHost = 'https://so.aihubs.cn:15958';
type FeatureDailyLimitMap = {
  ai_write: {
    free: number;
    Lite: number;
    Pro: number;
  };
  ai_chat: {
    free: number;
    Lite: number;
    Pro: number;
  }
}
export const featureDailyLimitMap: FeatureDailyLimitMap = {
  ai_write: {
    free: 200,
    Lite: 100,
    Pro: 500,
  },
  ai_chat: {
    free: 200,
    Lite: 100,
    Pro: 500,
  },
};
export const devCode = 'dev1';

const api = new Hono<HonoContext>()
  .use(contextStorage())
  .use('*', async (c, next) => {
    c.set('db', globalDb);
    try {
      // <--- 添加 try
      const auth = createAuth(); // createAuth() 在内部会调用 getSocialProviders
      c.set('auth', auth);
      console.log('[DEBUG] Auth instance created successfully.'); // <--- 添加调试日志
    } catch (e) {
      // <--- 添加 catch
      console.error('[DEBUG] Failed to create Auth instance:', e); // <--- 添加错误日志
    }
    const session = await c.var.auth?.api?.getSession({ headers: c.req.raw.headers }); // 添加可选链以防 auth 未成功创建
    c.set('session', session);
    c.set('sessionUser', session?.user);
    // const autumn = new Autumn({ secretKey: process.env.AUTUMN_SECRET_KEY });
    // c.set('autumn', autumn);
    await next();
  })
  // .route('/autumn', autumnApi)

  // 订阅支付相关接口 mt
  .get('/system/packagePlan/listPlan', async (c) => {
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    try {
      const response = await fetch(subscribePayServiceHost + '/api/system/packagePlan/listPlan');

      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }

      // 后面再添加类型，先用any
      const jsonData: any = await response.json();
      return c.json(jsonData);
    } catch (error) {
      console.error('Error:', error);
      return c.text('Failed to fetch data', 500);
    }
  })
  .post('/pay/customer_portal', async (c) => {
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    // const body = await c.req.json();
    // const appUsername = body['appUsername'];
    const appUsername = c.var.sessionUser.id;
    try {
      const response = await fetch(subscribePayServiceHost + '/api/pay/customer_portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: subscribePayApiSecret,
          appId: 2,
          appUsername: appUsername+devCode,
          returnUrl: process.env.VITE_PUBLIC_APP_URL,
        }),
      });
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }
      const jsonData: any = await response.json();
      return c.json(jsonData);
    } catch (error) {
      console.error('Error:', error);
      return c.text('Failed to fetch data', 500);
    }
  })
  .post('/pay/subscribe/stripePay', async (c) => {
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    const body = await c.req.json();
    // const appUsername = body['appUsername'];
    const appUsername = c.var.sessionUser.id;
    const priceId = body['priceId'];
    try {
      const response = await fetch(subscribePayServiceHost + '/api/pay/subscribe/stripePay', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: subscribePayApiSecret,
          appId: 2,
          priceId: priceId,
          appUsername: appUsername+devCode,
          successUrl: process.env.VITE_PUBLIC_APP_URL,
          cancelUrl: process.env.VITE_PUBLIC_APP_URL,
        }),
      });
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }
      const jsonData: any = await response.json();
      return c.json(jsonData);
    } catch (error) {
      console.error('Error:', error);
      return c.text('Failed to fetch data', 500);
    }
  })
  .post('/pay/subscribe/statusInfo', async (c) => {
    console.log('c sessionUser:', c.var.sessionUser);
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    // const body = await c.req.json();
    // const appUsername = body['appUsername'];
    const appUsername = c.var.sessionUser.id;
    try {
      const response = await fetch(subscribePayServiceHost + '/api/pay/subscribe/statusInfo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: subscribePayApiSecret,
          appId: 2,
          appUsername: appUsername+devCode,
        }),
      });
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }
      const jsonData: any = await response.json();
      return c.json(jsonData);
    } catch (error) {
      console.error('Error:', error);
      return c.text('Failed to fetch data', 500);
    }
  })
  // 用户每日功能使用统计相关接口
  .post('/daily_usage/check', async (c) => {
    console.log('c sessionUser:', c.var.sessionUser);
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    type reqBody = {
      featureName: 'ai_chat' | 'ai_write';
    }
    const body: reqBody = await c.req.json();
    const featureName = body['featureName'];
    if (!featureName) {
      return c.text('no featureName', 400);
    }
    // 获取用户会员信息
    const userEmail = c.var.sessionUser.email;

    let userSubscribeLevel: 'free' | 'Lite' | 'Pro' = 'free';
    try {
      const response = await fetch(subscribePayServiceHost + '/api/pay/subscribe/statusInfo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: subscribePayApiSecret,
          appId: 2,
          appUsername: c.var.sessionUser.id+devCode,
        }),
      });
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }
      const jsonData: any = await response.json();
      if (jsonData?.data) {
        userSubscribeLevel = jsonData.data.planName;
      }
    } catch (error) {
      console.error('Error:', error);
    }
    const totalDayLimit = featureDailyLimitMap[featureName][userSubscribeLevel];
    // 获取此刻的UTC日期
    const now = new Date();
    const utcMonthInt = now.getUTCMonth()+1;
    let utcMonthStr = '';
    if (utcMonthInt <= 9) {
      utcMonthStr = '0' + utcMonthInt;
    }else {
      utcMonthStr = '' + utcMonthInt;
    }
    const utcDateInt = now.getUTCDate();
    let utcDateStr = '';
    if (utcDateInt <= 9) {
      utcDateStr = '0' + utcDateInt;
    }else {
      utcDateStr = '' + utcDateInt
    }
    const todayUTCStr = `${now.getUTCFullYear()}-${utcMonthStr}-${utcDateStr}`
    console.log("todayUTCStr: ",todayUTCStr);

    const userId = c.var.sessionUser.id;
    // const db = createDb(env.HYPERDRIVE.connectionString);
    const queryResult = await c.var.db
      .select()
      .from(userUsageDaily)
      .where(and(
        eq(userUsageDaily.userFingerprint, userEmail),
        eq(userUsageDaily.featureName, featureName),
        eq(userUsageDaily.dayStr, todayUTCStr)
      ));
    if (!queryResult.length || !queryResult[0]) {
      // 没数据
      await c.var.db
        .insert(userUsageDaily)
        .values({ userId: userId, userFingerprint: userEmail, featureName: featureName, dailyCount: 0, dayStr: todayUTCStr });
      return c.json({
        dayLimit: totalDayLimit,
        dayUsage: 0,
        canUse: totalDayLimit > 0
      })
    }else {
      const todayUsage = queryResult[0];
      const currentDayUsage = todayUsage.dailyCount;
      if (todayUsage.userId !== userId) {
        // 在同一天，用户删除账号后又使用同一邮箱注册
        await c.var.db.update(userUsageDaily)
        .set({ userId: userId })
        .where(eq(userUsageDaily.id, todayUsage.id));
      }
      return c.json({
        dayLimit: totalDayLimit,
        dayUsage: currentDayUsage,
        canUse: totalDayLimit > currentDayUsage
      })
    }
  })
  .post('/daily_usage/track', async (c) => {
    console.log('c sessionUser:', c.var.sessionUser);
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    const body = await c.req.json();
    const featureName = body['featureName'];
    if (!featureName) {
      return c.text('no featureName', 400);
    }
    let numberUsed = body['numberUsed'];
    if (!numberUsed) {
      numberUsed = 1;
    }
    const userId = c.var.sessionUser.id;
    const userEmail = c.var.sessionUser.email;
    // 获取此刻的UTC日期
    const now = new Date();
    const utcMonthInt = now.getUTCMonth()+1;
    let utcMonthStr = '';
    if (utcMonthInt <= 9) {
      utcMonthStr = '0' + utcMonthInt;
    }else {
      utcMonthStr = '' + utcMonthInt;
    }
    const utcDateInt = now.getUTCDate();
    let utcDateStr = '';
    if (utcDateInt <= 9) {
      utcDateStr = '0' + utcDateInt;
    }else {
      utcDateStr = '' + utcDateInt;
    }
    const todayUTCStr = `${now.getUTCFullYear()}-${utcMonthStr}-${utcDateStr}`
    console.log("todayUTCStr: ",todayUTCStr);

    // const db = createDb(env.HYPERDRIVE.connectionString);
    const queryResult = await c.var.db
      .select()
      .from(userUsageDaily)
      .where(and(
        eq(userUsageDaily.userFingerprint, userEmail),
        eq(userUsageDaily.featureName, featureName),
        eq(userUsageDaily.dayStr, todayUTCStr)
      ));
    if (!queryResult?.length || !queryResult[0]) {
      // 没数据
      await c.var.db
        .insert(userUsageDaily)
        .values({ userId: userId, userFingerprint: userEmail, featureName: featureName, dailyCount: numberUsed, dayStr: todayUTCStr });
      return c.json({
        action: 'track_daily_usage',
        success: 'true'
      });
    }else {
      const todayUsage = queryResult[0];
      await c.var.db.update(userUsageDaily)
      .set({ dailyCount: todayUsage.dailyCount + numberUsed })
      .where(eq(userUsageDaily.id, todayUsage.id));
      if (todayUsage.userId !== userId) {
        // 在同一天，用户删除账号后又使用同一邮箱注册
        await c.var.db.update(userUsageDaily)
        .set({ userId: userId })
        .where(eq(userUsageDaily.id, todayUsage.id));
      }
      return c.json({
        action: 'track_daily_usage',
        success: 'true'
      });
    }
  })
  // 根据邮箱获取第一个链接的邮箱
  .post('/auth_email/first_connect_email', async (c) => {
    const body = await c.req.json();
    const email = body['email'];
    if (!email) {
      return c.text('no email', 400);
    }
    // 根据邮箱找到userId
    const [connectionDetail] = await c.var.db
    .select()
    .from(connection)
    .where(eq(connection.email, email));
    if (connectionDetail) {
      const userId = connectionDetail.userId;
      const [userDetail] = await c.var.db
      .select()
      .from(user_)
      .where(eq(user_.id, userId));
      if (userDetail) {
        return c.json({
          firstEmail: userDetail.email
        })
      }else {
        return c.text('User not found', 400);
      }
    }else {
      return c.json({
        firstEmail: email
      })
    }
  })

  .post('/chat', chatHandler)
  //   .post('/public-chat', publicChatHandler) // ssshhhh
  .on(['GET', 'POST'], '/auth/*', (c) => c.var.auth.handler(c.req.raw))
  .use(
    trpcServer({
      endpoint: '/api/trpc',
      router: appRouter,
      createContext: (_, c) => ({ c, session: c.var['session'], db: c.var['db'] }),
      allowMethodOverride: true,
      onError: (opts) => {
        console.error('Error in TRPC handler:', opts.error);
      },
    }),
  )
  .onError(async (err, c) => {
    if (err instanceof Response) return err;
    console.error('Error in Hono handler:', err);
    return c.json(
      {
        error: 'Internal Server Error',
        message: err instanceof Error ? err.message : 'Unknown error',
      },
      500,
    );
  });

const app = new Hono<HonoContext>()
  .use(
    '*',
    cors({
      origin: (c) => {
        if (c.includes(process.env.COOKIE_DOMAIN)) {
          return c;
        } else {
          return null;
        }
      },
      credentials: true,
      allowHeaders: ['Content-Type', 'Authorization'],
      exposeHeaders: ['X-Zero-Redirect'],
    }),
  )
  .route('/api', api)
  .get('/health', (c) => c.json({ message: 'Zero Server is Up!' }))
  .get('/', (c) => c.redirect(`${process.env.VITE_PUBLIC_APP_URL}`));

app.get('/', (c) => c.text('Hello Bun!'));

// 添加IMAP测试端点
app.get('/test-imap', async (c) => {
  try {
    console.log('=== 服务器内部IMAP测试 ===');
    const { testImapConnection } = await import('./lib/debug-connection.js');

    const result = await testImapConnection(
      'imap.mail.me.com',
      993,
      true,
      '<EMAIL>',
      'lidl-gezh-qlax-xrbr',
      false,
    );

    return c.json({
      success: result.success,
      error: result.error,
      message: '服务器内部IMAP测试完成',
    });
  } catch (error) {
    console.error('服务器内部IMAP测试失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: '服务器内部IMAP测试异常',
    });
  }
});



export default {
  port: process.env['PORT'] || 8787,
  host: '0.0.0.0',
  fetch: app.fetch,
};
