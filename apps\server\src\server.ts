//import { env, WorkerEntrypoint } from 'cloudflare:workers';

// 🕐 全局时间戳日志功能 - 重写所有 console 方法（带颜色编码）
const originalConsole = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug,
};

// ANSI 颜色代码
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  gray: '\x1b[90m',
};

const addTimestamp = (level: string, color: string, ...args: any[]) => {
  // 获取中国时区时间 (UTC+8)
  const now = new Date();
  const chinaTime = new Date(now.getTime() + 8 * 60 * 60 * 1000); // UTC+8
  const timestamp = chinaTime.toISOString().replace('T', ' ').replace('Z', '').slice(0, 19);

  const coloredTimestamp = `${colors.gray}[${timestamp} CST]${colors.reset}`;
  const coloredLevel = `${color}[${level}]${colors.reset}`;
  return [coloredTimestamp, coloredLevel, ...args];
};

// 重写 console 方法（带颜色）
console.log = (...args: any[]) =>
  originalConsole.log(...addTimestamp('LOG', colors.green, ...args));
console.error = (...args: any[]) =>
  originalConsole.error(...addTimestamp('ERROR', colors.red, ...args));
console.warn = (...args: any[]) =>
  originalConsole.warn(...addTimestamp('WARN', colors.yellow, ...args));
console.info = (...args: any[]) =>
  originalConsole.info(...addTimestamp('INFO', colors.cyan, ...args));
console.debug = (...args: any[]) =>
  originalConsole.debug(...addTimestamp('DEBUG', colors.magenta, ...args));

// 启动日志
console.log('🚀 Zero Mail Server 启动中...');

import { contextStorage } from 'hono/context-storage';
import { routePartykitRequest } from 'partyserver';
import { DurableMailbox } from './lib/party.js';
// import { autumnApi } from './routes/autumn.js';
import { trpcServer } from '@hono/trpc-server';
import { chatHandler } from './routes/chat.js';
import { appRouter } from './trpc/index.js';
import type { HonoContext } from './ctx.js';
import { createAuth } from './lib/auth.js';
import { createDb } from '@zero/db';
// import { Autumn } from 'autumn-js';
import { cors } from 'hono/cors';
import { Hono } from 'hono';
import { connection, userUsageDaily, user as user_, imapSmtpConfig } from '@zero/db/schema';
import { eq, and, sql } from 'drizzle-orm';

// 创建全局数据库实例，避免重复创建连接
export const globalDb = createDb(process.env.DATABASE_URL!);

// 支付相关变量
export const subscribePayApiSecret = '6s9flrez7ym3z13v';
export const subscribePayServiceHost = 'https://so.aihubs.cn:15958';
type FeatureDailyLimitMap = {
  ai_write: {
    free: number;
    Lite: number;
    Pro: number;
  };
  ai_chat: {
    free: number;
    Lite: number;
    Pro: number;
  }
}
export const featureDailyLimitMap: FeatureDailyLimitMap = {
  ai_write: {
    free: 200,
    Lite: 100,
    Pro: 500,
  },
  ai_chat: {
    free: 200,
    Lite: 100,
    Pro: 500,
  },
};
export const devCode = 'dev1';

// 邮箱配置获取工具函数（数据库 → Thunderbird → 默认）
export async function getEmailConfig(domain: string, db: any) {
  try {
    // 1. 先从数据库查询
    const [dbConfig] = await db
      .select()
      .from(imapSmtpConfig)
      .where(eq(imapSmtpConfig.email, domain))
      .limit(1);

    if (dbConfig) {
      console.log(`从数据库获取到 ${domain} 的配置`);
      return {
        success: true,
        source: 'database',
        config: {
          imapHost: dbConfig.imapHost,
          imapPort: dbConfig.imapPort,
          imapSecure: dbConfig.imapSecure,
          smtpHost: dbConfig.smtpHost,
          smtpPort: dbConfig.smtpPort,
          smtpSecure: dbConfig.smtpSecure,
        }
      };
    }

    // 2. 数据库没有，从 Thunderbird 获取
    console.log(`从 Thunderbird 获取 ${domain} 的配置`);
    const response = await fetch(`https://autoconfig.thunderbird.net/v1.1/${domain}`);

    if (response.ok) {
      const xmlText = await response.text();
      const imapMatch = xmlText.match(/<incomingServer type="imap">[\s\S]*?<\/incomingServer>/);
      const smtpMatch = xmlText.match(/<outgoingServer type="smtp">[\s\S]*?<\/outgoingServer>/);

      if (imapMatch && smtpMatch) {
        const imapHost = imapMatch[0].match(/<hostname>(.*?)<\/hostname>/)?.[1];
        const imapPort = parseInt(imapMatch[0].match(/<port>(.*?)<\/port>/)?.[1] || '993');
        const imapSecure = imapMatch[0].includes('<socketType>SSL</socketType>');

        const smtpHost = smtpMatch[0].match(/<hostname>(.*?)<\/hostname>/)?.[1];
        const smtpPort = parseInt(smtpMatch[0].match(/<port>(.*?)<\/port>/)?.[1] || '465');
        const smtpSecure = smtpMatch[0].includes('<socketType>SSL</socketType>');

        if (imapHost && smtpHost) {
          console.log(`成功从 Thunderbird 获取 ${domain} 的配置`);
          return {
            success: true,
            source: 'thunderbird',
            config: { imapHost, imapPort, imapSecure, smtpHost, smtpPort, smtpSecure }
          };
        }
      }
    }
  } catch (error) {
    console.error(`获取 ${domain} 配置失败:`, error);
  }

  // 3. 都没有，返回失败
  return {
    success: false,
    source: 'none',
    message: `未找到 ${domain} 的配置`
  };
}

const api = new Hono<HonoContext>()
  .use(contextStorage())
  .use('*', async (c, next) => {
    c.set('db', globalDb);
    try {
      // <--- 添加 try
      const auth = createAuth(); // createAuth() 在内部会调用 getSocialProviders
      c.set('auth', auth);
      console.log('[DEBUG] Auth instance created successfully.'); // <--- 添加调试日志
    } catch (e) {
      // <--- 添加 catch
      console.error('[DEBUG] Failed to create Auth instance:', e); // <--- 添加错误日志
    }
    const session = await c.var.auth?.api?.getSession({ headers: c.req.raw.headers }); // 添加可选链以防 auth 未成功创建
    c.set('session', session);
    c.set('sessionUser', session?.user);
    // const autumn = new Autumn({ secretKey: process.env.AUTUMN_SECRET_KEY });
    // c.set('autumn', autumn);
    await next();
  })
  // .route('/autumn', autumnApi)

  // 订阅支付相关接口 mt
  .get('/system/packagePlan/listPlan', async (c) => {
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    try {
      const response = await fetch(subscribePayServiceHost + '/api/system/packagePlan/listPlan');

      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }

      // 后面再添加类型，先用any
      const jsonData: any = await response.json();
      return c.json(jsonData);
    } catch (error) {
      console.error('Error:', error);
      return c.text('Failed to fetch data', 500);
    }
  })
  .post('/pay/customer_portal', async (c) => {
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    // const body = await c.req.json();
    // const appUsername = body['appUsername'];
    const appUsername = c.var.sessionUser.id;
    try {
      const response = await fetch(subscribePayServiceHost + '/api/pay/customer_portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: subscribePayApiSecret,
          appId: 2,
          appUsername: appUsername+devCode,
          returnUrl: process.env.VITE_PUBLIC_APP_URL,
        }),
      });
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }
      const jsonData: any = await response.json();
      return c.json(jsonData);
    } catch (error) {
      console.error('Error:', error);
      return c.text('Failed to fetch data', 500);
    }
  })
  .post('/pay/subscribe/stripePay', async (c) => {
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    const body = await c.req.json();
    // const appUsername = body['appUsername'];
    const appUsername = c.var.sessionUser.id;
    const priceId = body['priceId'];
    try {
      const response = await fetch(subscribePayServiceHost + '/api/pay/subscribe/stripePay', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: subscribePayApiSecret,
          appId: 2,
          priceId: priceId,
          appUsername: appUsername+devCode,
          successUrl: process.env.VITE_PUBLIC_APP_URL,
          cancelUrl: process.env.VITE_PUBLIC_APP_URL,
        }),
      });
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }
      const jsonData: any = await response.json();
      return c.json(jsonData);
    } catch (error) {
      console.error('Error:', error);
      return c.text('Failed to fetch data', 500);
    }
  })
  .post('/pay/subscribe/statusInfo', async (c) => {
    console.log('c sessionUser:', c.var.sessionUser);
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    // const body = await c.req.json();
    // const appUsername = body['appUsername'];
    const appUsername = c.var.sessionUser.id;
    try {
      const response = await fetch(subscribePayServiceHost + '/api/pay/subscribe/statusInfo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: subscribePayApiSecret,
          appId: 2,
          appUsername: appUsername+devCode,
        }),
      });
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }
      const jsonData: any = await response.json();
      return c.json(jsonData);
    } catch (error) {
      console.error('Error:', error);
      return c.text('Failed to fetch data', 500);
    }
  })
  // 用户每日功能使用统计相关接口
  .post('/daily_usage/check', async (c) => {
    console.log('c sessionUser:', c.var.sessionUser);
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    type reqBody = {
      featureName: 'ai_chat' | 'ai_write';
    }
    const body: reqBody = await c.req.json();
    const featureName = body['featureName'];
    if (!featureName) {
      return c.text('no featureName', 400);
    }
    // 获取用户会员信息
    const userEmail = c.var.sessionUser.email;

    let userSubscribeLevel: 'free' | 'Lite' | 'Pro' = 'free';
    try {
      const response = await fetch(subscribePayServiceHost + '/api/pay/subscribe/statusInfo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: subscribePayApiSecret,
          appId: 2,
          appUsername: c.var.sessionUser.id+devCode,
        }),
      });
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.status}`);
      }
      const jsonData: any = await response.json();
      if (jsonData?.data) {
        userSubscribeLevel = jsonData.data.planName;
      }
    } catch (error) {
      console.error('Error:', error);
    }
    const totalDayLimit = featureDailyLimitMap[featureName][userSubscribeLevel];
    // 获取此刻的UTC日期
    const now = new Date();
    const utcMonthInt = now.getUTCMonth()+1;
    let utcMonthStr = '';
    if (utcMonthInt <= 9) {
      utcMonthStr = '0' + utcMonthInt;
    }else {
      utcMonthStr = '' + utcMonthInt;
    }
    const utcDateInt = now.getUTCDate();
    let utcDateStr = '';
    if (utcDateInt <= 9) {
      utcDateStr = '0' + utcDateInt;
    }else {
      utcDateStr = '' + utcDateInt
    }
    const todayUTCStr = `${now.getUTCFullYear()}-${utcMonthStr}-${utcDateStr}`
    console.log("todayUTCStr: ",todayUTCStr);

    const userId = c.var.sessionUser.id;
    // const db = createDb(env.HYPERDRIVE.connectionString);
    const queryResult = await c.var.db
      .select()
      .from(userUsageDaily)
      .where(and(
        eq(userUsageDaily.userFingerprint, userEmail),
        eq(userUsageDaily.featureName, featureName),
        eq(userUsageDaily.dayStr, todayUTCStr)
      ));
    if (!queryResult.length || !queryResult[0]) {
      // 没数据
      await c.var.db
        .insert(userUsageDaily)
        .values({ userId: userId, userFingerprint: userEmail, featureName: featureName, dailyCount: 0, dayStr: todayUTCStr });
      return c.json({
        dayLimit: totalDayLimit,
        dayUsage: 0,
        canUse: totalDayLimit > 0
      })
    }else {
      const todayUsage = queryResult[0];
      const currentDayUsage = todayUsage.dailyCount;
      if (todayUsage.userId !== userId) {
        // 在同一天，用户删除账号后又使用同一邮箱注册
        await c.var.db.update(userUsageDaily)
        .set({ userId: userId })
        .where(eq(userUsageDaily.id, todayUsage.id));
      }
      return c.json({
        dayLimit: totalDayLimit,
        dayUsage: currentDayUsage,
        canUse: totalDayLimit > currentDayUsage
      })
    }
  })
  .post('/daily_usage/track', async (c) => {
    console.log('c sessionUser:', c.var.sessionUser);
    if (!c.var.sessionUser) {
      return c.text('not login', 401);
    }
    const body = await c.req.json();
    const featureName = body['featureName'];
    if (!featureName) {
      return c.text('no featureName', 400);
    }
    let numberUsed = body['numberUsed'];
    if (!numberUsed) {
      numberUsed = 1;
    }
    const userId = c.var.sessionUser.id;
    const userEmail = c.var.sessionUser.email;
    // 获取此刻的UTC日期
    const now = new Date();
    const utcMonthInt = now.getUTCMonth()+1;
    let utcMonthStr = '';
    if (utcMonthInt <= 9) {
      utcMonthStr = '0' + utcMonthInt;
    }else {
      utcMonthStr = '' + utcMonthInt;
    }
    const utcDateInt = now.getUTCDate();
    let utcDateStr = '';
    if (utcDateInt <= 9) {
      utcDateStr = '0' + utcDateInt;
    }else {
      utcDateStr = '' + utcDateInt;
    }
    const todayUTCStr = `${now.getUTCFullYear()}-${utcMonthStr}-${utcDateStr}`
    console.log("todayUTCStr: ",todayUTCStr);

    // const db = createDb(env.HYPERDRIVE.connectionString);
    const queryResult = await c.var.db
      .select()
      .from(userUsageDaily)
      .where(and(
        eq(userUsageDaily.userFingerprint, userEmail),
        eq(userUsageDaily.featureName, featureName),
        eq(userUsageDaily.dayStr, todayUTCStr)
      ));
    if (!queryResult?.length || !queryResult[0]) {
      // 没数据
      await c.var.db
        .insert(userUsageDaily)
        .values({ userId: userId, userFingerprint: userEmail, featureName: featureName, dailyCount: numberUsed, dayStr: todayUTCStr });
      return c.json({
        action: 'track_daily_usage',
        success: 'true'
      });
    }else {
      const todayUsage = queryResult[0];
      await c.var.db.update(userUsageDaily)
      .set({ dailyCount: todayUsage.dailyCount + numberUsed })
      .where(eq(userUsageDaily.id, todayUsage.id));
      if (todayUsage.userId !== userId) {
        // 在同一天，用户删除账号后又使用同一邮箱注册
        await c.var.db.update(userUsageDaily)
        .set({ userId: userId })
        .where(eq(userUsageDaily.id, todayUsage.id));
      }
      return c.json({
        action: 'track_daily_usage',
        success: 'true'
      });
    }
  })
  // 根据邮箱获取第一个链接的邮箱
  .post('/auth_email/first_connect_email', async (c) => {
    const body = await c.req.json();
    const email = body['email'];
    if (!email) {
      return c.text('no email', 400);
    }
    // 根据邮箱找到userId
    const [connectionDetail] = await c.var.db
    .select()
    .from(connection)
    .where(eq(connection.email, email));
    if (connectionDetail) {
      const userId = connectionDetail.userId;
      const [userDetail] = await c.var.db
      .select()
      .from(user_)
      .where(eq(user_.id, userId));
      if (userDetail) {
        return c.json({
          firstEmail: userDetail.email
        })
      }else {
        return c.text('User not found', 400);
      }
    }else {
      return c.json({
        firstEmail: email
      })
    }
  })

  // 邮箱配置获取接口（数据库 → Thunderbird） -1 通过测试
  .get('/email-config/:domain', async (c) => {
    const domain = c.req.param('domain');
    if (!domain) {
      return c.json({ error: '域名参数缺失' }, 400);
    }

    // 🎯 获取查询参数中的邮箱地址（可选）
    const email = c.req.query('email');

    const result = await getEmailConfig(domain, c.var.db);

    // 🎯 如果未找到配置，记录错误日志
    if (!result.success && result.source === 'none') {
      try {
        const { logConnectionError } = await import('./lib/error-logger');
        await logConnectionError(
          email || `unknown@${domain}`, // 优先使用真实邮箱地址，否则使用域名构造
          result.message || `未找到 ${domain} 的配置`,
          'IMAP', // 配置查询失败记录为IMAP类型
          {
            host: domain,
            port: 0,
            secure: false
          }
        );
      } catch (logError) {
        console.error('记录配置查询错误日志失败:', logError);
      }
    }

    return c.json(result);
  })

  // -添加邮箱配置接口 db 2 通过测试
  .post('/email-config', async (c) => {
    
    try {
      const body = await c.req.json();
      const { domain, imapHost, imapPort, imapSecure, smtpHost, smtpPort, smtpSecure } = body;

      if (!domain || !imapHost || !smtpHost) {
        return c.json({ error: '必填字段缺失' }, 400);
      }

      // 插入或更新配置
      await c.var.db.insert(imapSmtpConfig).values({
        id: crypto.randomUUID(),
        email: domain,
        type: 'both',
        imapHost,
        imapPort: imapPort || 993,
        imapSecure: imapSecure ?? true,
        smtpHost,
        smtpPort: smtpPort || 465,
        smtpSecure: smtpSecure ?? true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }).onConflictDoUpdate({
        target: [imapSmtpConfig.email],
        set: {
          imapHost,
          imapPort: imapPort || 993,
          imapSecure: imapSecure ?? true,
          smtpHost,
          smtpPort: smtpPort || 465,
          smtpSecure: smtpSecure ?? true,
          updatedAt: new Date(),
        },
      });

      console.log(`成功添加/更新 ${domain} 的邮箱配置`);
      return c.json({
        success: true,
        message: `${domain} 配置已保存`
      });
    } catch (error) {
      console.error('添加邮箱配置失败:', error);
      return c.json({
        success: false,
        error: '添加邮箱配置失败',
        message: error instanceof Error ? error.message : String(error)
      }, 500);
    }
  })

  // 获取邮箱配置（通过邮箱地址）--暂时没用。 可删除
  .post('/email-config/get-with-fallback', async (c) => {
    try {
      const body = await c.req.json();
      const { email } = body;

      if (!email) {
        return c.json({ error: '邮箱参数缺失' }, 400);
      }

      const domain = email.split('@')[1];
      if (!domain) {
        return c.json({ error: '邮箱格式无效' }, 400);
      }

      const result = await getEmailConfig(domain, c.var.db);
      return c.json(result);

    } catch (error) {
      console.error('获取邮箱配置失败:', error);
      return c.json({
        success: false,
        error: '获取邮箱配置失败',
        message: error instanceof Error ? error.message : String(error)
      }, 500);
    }
  })

  .post('/chat', chatHandler)
  //   .post('/public-chat', publicChatHandler) // ssshhhh
  .on(['GET', 'POST'], '/auth/*', (c) => c.var.auth.handler(c.req.raw))
  .use(
    trpcServer({
      endpoint: '/api/trpc',
      router: appRouter,
      createContext: (_, c) => ({ c, session: c.var['session'], db: c.var['db'] }),
      allowMethodOverride: true,
      onError: (opts) => {
        console.error('Error in TRPC handler:', opts.error);
      },
    }),
  )
  .onError(async (err, c) => {
    if (err instanceof Response) return err;
    console.error('Error in Hono handler:', err);
    return c.json(
      {
        error: 'Internal Server Error',
        message: err instanceof Error ? err.message : 'Unknown error',
      },
      500,
    );
  });

const app = new Hono<HonoContext>()
  .use(
    '*',
    cors({
      origin: (c) => {
        if (c.includes(process.env.COOKIE_DOMAIN)) {
          return c;
        } else {
          return null;
        }
      },
      credentials: true,
      allowHeaders: ['Content-Type', 'Authorization'],
      exposeHeaders: ['X-Zero-Redirect'],
    }),
  )
  .route('/api', api)
  .get('/health', (c) => c.json({ message: 'Zero Server is Up!' }))
  .get('/', (c) => c.redirect(`${process.env.VITE_PUBLIC_APP_URL}`));

app.get('/', (c) => c.text('Hello Bun!'));

// 添加IMAP测试端点
app.get('/test-imap', async (c) => {
  try {
    console.log('=== 服务器内部IMAP测试 ===');
    const { testImapConnection } = await import('./lib/debug-connection.js');

    const result = await testImapConnection(
      'imap.mail.me.com',
      993,
      true,
      '<EMAIL>',
      'lidl-gezh-qlax-xrbr',
      false,
    );

    return c.json({
      success: result.success,
      error: result.error,
      message: '服务器内部IMAP测试完成',
    });
  } catch (error) {
    console.error('服务器内部IMAP测试失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: '服务器内部IMAP测试异常',
    });
  }
});

// 添加邮箱配置测试端点
app.get('/test-email-config/:domain', async (c) => {
  const domain = c.req.param('domain');
  try {
    console.log(`=== 测试邮箱配置获取: ${domain} ===`);

    const result = await getEmailConfig(domain, globalDb);

    return c.json({
      domain,
      result,
      message: `邮箱配置测试完成 - ${result.success ? `成功 (来源: ${result.source})` : '失败'}`
    });
  } catch (error) {
    console.error('邮箱配置测试失败:', error);
    return c.json({
      domain,
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: '邮箱配置测试异常'
    });
  }
});

// 添加 Thunderbird 配置测试端点
app.get('/test-thunderbird/:domain', async (c) => {
  const domain = c.req.param('domain');
  try {
    console.log(`=== 测试 Thunderbird 配置获取: ${domain} ===`);

    const response = await fetch(`https://autoconfig.thunderbird.net/v1.1/${domain}`);

    if (response.ok) {
      const xmlText = await response.text();
      console.log('Thunderbird XML 响应:', xmlText.substring(0, 500) + '...');

      // 尝试解析配置
      const imapMatch = xmlText.match(/<incomingServer type="imap">[\s\S]*?<\/incomingServer>/);
      const smtpMatch = xmlText.match(/<outgoingServer type="smtp">[\s\S]*?<\/outgoingServer>/);

      let config = null;
      if (imapMatch && smtpMatch) {
        const imapHost = imapMatch[0].match(/<hostname>(.*?)<\/hostname>/)?.[1];
        const smtpHost = smtpMatch[0].match(/<hostname>(.*?)<\/hostname>/)?.[1];
        if (imapHost && smtpHost) {
          config = { imapHost, smtpHost };
        }
      }

      return c.json({
        domain,
        success: true,
        config,
        rawXml: xmlText,
        message: 'Thunderbird 配置测试完成'
      });
    } else {
      return c.json({
        domain,
        success: false,
        status: response.status,
        message: `Thunderbird 配置服务返回 ${response.status}`
      });
    }
  } catch (error) {
    console.error('Thunderbird 配置测试失败:', error);
    return c.json({
      domain,
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: 'Thunderbird 配置测试异常'
    });
  }
});

// 添加一个简单的测试接口来验证数据库+Thunderbird的配置获取
app.get('/test-config-fallback/:domain', async (c) => {
  const domain = c.req.param('domain');
  try {
    console.log(`=== 测试配置获取回退机制: ${domain} ===`);

    // 模拟调用 /api/email-config/:domain 接口
    const response = await fetch(`${c.req.url.replace(/\/test-config-fallback\/.*$/, '')}/api/email-config/${domain}`);
    const result = await response.json();

    return c.json({
      domain,
      testResult: result,
      message: `配置获取测试完成 - ${(result as any).success ? `成功 (来源: ${(result as any).source})` : '失败'}`
    });
  } catch (error) {
    console.error('配置获取测试失败:', error);
    return c.json({
      domain,
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: '配置获取测试异常'
    });
  }
});

export default {
  port: process.env['PORT'] || 8787,
  host: '0.0.0.0',
  fetch: app.fetch,
};
