#!/usr/bin/env 使用 node start-dev.js

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动 Zero Mail 开发环境...\n');



// 启动前端服务
console.log('📱 启动前端服务...');
const frontendPath = path.join(__dirname, 'apps', 'mail');

const frontend = spawn('pnpm', ['dev'], {
  stdio: 'inherit',
  cwd: frontendPath,
  shell: true
});

// 等待2秒后启动后端
setTimeout(() => {
  console.log('🔧 启动后端服务...');
  const backendPath = path.join(__dirname, 'apps', 'server');

  const backend = spawn('pnpm', ['run', 'bun:dev'], {
    stdio: 'inherit',
    cwd: backendPath,
    shell: true
  });

  console.log('\n✅ 开发环境启动完成！');
  console.log('📱 前端: http://localhost:3000');
  console.log('🔧 后端: http://localhost:8787\n');

  // 处理退出信号
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务...');
    try {
      frontend.kill();
      backend.kill();
    } catch (e) {
      // 忽略错误
    }
    process.exit(0);
  });

  backend.on('close', (code) => {
    console.log(`后端服务退出，代码: ${code}`);
  });

}, 2000);

frontend.on('close', (code) => {
  console.log(`前端服务退出，代码: ${code}`);
});
