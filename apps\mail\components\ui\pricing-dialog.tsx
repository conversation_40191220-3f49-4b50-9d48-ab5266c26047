import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from '@/components/ui/dialog';
import { <PERSON><PERSON>heck, PurpleThick<PERSON>he<PERSON> } from '@/components/icons/icons';
// import { useBilling } from '@/hooks/use-billing';
import { Button } from '@/components/ui/button';
import { useState, useMemo } from 'react';
import { useQueryState } from 'nuqs';
import { cn } from '@/lib/utils';
import { Badge } from './badge';
import { toast } from 'sonner';
import { useActiveConnection, useConnections } from '@/hooks/use-connections';
import { useQuery } from '@tanstack/react-query';
import { useSession } from '@/lib/auth-client';

const planFeatures: any = {
  Lite: [
    {
      featureName: 'feature_1',
      featureDetail: 'feature detail text',
    },
    {
      featureName: 'feature_2',
      featureDetail: 'feature detail text',
    },
    {
      featureName: 'feature_3',
      featureDetail: 'feature detail text',
    },
  ],
  Pro: [
    {
      featureName: 'feature_1',
      featureDetail: 'feature detail text',
    },
    {
      featureName: 'feature_2',
      featureDetail: 'feature detail text',
    },
    {
      featureName: 'feature_3',
      featureDetail: 'feature detail text',
    },
  ],
};

type PlanInfo = {
  price: number;
  tokens: number;
  productId: string;
  featureId: string;
  balance: number;
};

type Plans = {
  [key: string]: PlanInfo;
};

export function PricingDialog() {
  // const { check, attach, customer } = useBilling();

  const [isLoading, setIsLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('');
  const [open, setOpen] = useQueryState('pricingDialog');
  const [paymentUrl, setPaymentUrl] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { data: session, refetch: refetchSession, isPending: isSessionPending } = useSession();
  const { data: activeConnection, refetch: refetchActiveConnection } = useActiveConnection();
  const { data } = useConnections();
  const activeAccount = useMemo(() => {
    if (!activeConnection || !data) return null;
    return data.connections?.find((connection) => connection.id === activeConnection.id);
  }, [activeConnection, data]);

  const {
    data: planListData,
  } = useQuery<any>({
    queryKey: ['package_plan_list'],
    queryFn: async () => {
      // if (!(activeAccount?.email || session?.user.email)) {
      //   throw new Error('no user email');
      // }
      const response = await fetch(
        import.meta.env.VITE_PUBLIC_BACKEND_URL + '/api/system/packagePlan/listPlan',
        {
          method: 'GET',
          credentials: 'include',
          // headers: {
          //   'Content-Type': 'application/json',
          // },
          // body: JSON.stringify({
          //   appUsername: activeAccount?.email || session?.user.email,
          // }),
        },
      );
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    },
  });
  const formatedPlanList: any = useMemo(() => {
    if (planListData?.data) {
      const allPlanList = planListData.data;
      const planListResult = [];
      for (const planItem of allPlanList) {
        if (planItem.trustApp === 2) {
          planListResult.push({
            // ...(planItem.name === 'Lite') && {someprop: propvalue},
            isPopular: planItem.name === 'Lite',
            id: planItem.id,
            name: planItem.name,
            price: planItem.price,
            duration: planItem.duration,
            defaultPrice: planItem.defaultPrice,
          });
        }
      }
      return planListResult;
    } else {
      return null;
    }
  }, [planListData]);

  // const plans: Plans = {
  //   basic: {
  //     price: 1,
  //     tokens: 100,
  //     productId: 'pro_annual',
  //     featureId: 'chat-messages',
  //     balance: 100
  //   },
  //   standard: {
  //     price: 3.9,
  //     tokens: 1000,
  //     productId: 'pro_annual',
  //     featureId: 'connections',
  //     balance: 1000
  //   },
  //   premium: {
  //     price: 9.9,
  //     tokens: 2000,
  //     productId: 'pro_annual',
  //     featureId: 'brain-activity',
  //     balance: 2000
  //   }
  // };

  const handleUpgrade = async () => {
    setIsLoading(true);
    if (!formatedPlanList) {
      toast.error('No available plan');
      return;
    }
    const currentPriceId = formatedPlanList.find((i: any) => i.id === selectedPlan).defaultPrice;
    if (!currentPriceId) {
      toast.error('Can not get price id');
      setIsLoading(false);
      return;
    }
    try {
      const res = await fetch(
        import.meta.env.VITE_PUBLIC_BACKEND_URL + '/api/pay/subscribe/stripePay',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            priceId: currentPriceId,
            // appUsername: activeAccount?.email || session?.user.email,
          }),
          credentials: 'include',
        },
      );
      if (!res.ok) {
        const error: any = new Error('An error occurred while fetching the data.');
        error.status = res.status;
        throw error;
      }
      const jsonData: any = await res.json();
      window.open(jsonData.data, '_self', 'noopener,noreferrer');
      setIsLoading(false);
    } catch (error: any) {
      toast.error('failed to get payment url:', error);
      setIsLoading(false);
    }

    // if (!attach || !check) {
    //   toast.error('支付功能暂时不可用');
    //   return;
    // }

    // if (!customer?.id) {
    //   toast.error('用户信息不可用，请先登录');
    //   return;
    // }

    // // 检查选择的套餐是否有效
    // if (!plans[selectedPlan]) {
    //   toast.error('所选套餐无效，请重新选择');
    //   return;
    // }

    // // 检查产品ID是否有效
    // if (!plans[selectedPlan].productId) {
    //   toast.error('产品信息无效，请联系客服');
    //   return;
    // }

    // // 检查功能ID是否有效
    // if (!plans[selectedPlan].featureId) {
    //   toast.error('功能信息无效，请联系客服');
    //   return;
    // }

    // setIsLoading(true);
    // setErrorMessage(null);

    // try {
    //   // 先检查功能是否可用
    //   const checkResult = await check({
    //     featureId: plans[selectedPlan].featureId,
    //     sendEvent: true
    //   });

    //   console.log('Check result:', checkResult);

    //   // 如果检查失败，显示错误信息
    //   if (checkResult.error) {
    //     setErrorMessage(checkResult.error.message || '功能检查失败');
    //     setIsLoading(false);
    //     return;
    //   }
    // } catch (error) {
    //   console.error('Check error:', error);
    //   // 检查失败不阻止继续流程，只记录日志
    // }

    // console.log('Attaching product:', {
    //   productId: plans[selectedPlan].productId,
    //   featureId: plans[selectedPlan].featureId,
    //   plan: selectedPlan,
    //   tokens: plans[selectedPlan].tokens
    // });

    // toast.promise(
    //   attach({
    //     productId: plans[selectedPlan].productId,
    //     successUrl: `${window.location.origin}/mail/inbox?success=true`,
    //     // 通过options传递featureId和数量
    //     options: [
    //       {
    //         featureId: plans[selectedPlan].featureId,
    //         quantity: plans[selectedPlan].balance // 使用balance作为数量
    //       }
    //     ],
    //     // 根据Autumn API文档，不需要在这里传递customer_id，
    //     // 它会在后端通过session自动获取
    //   }).then(result => {
    //     console.log('Attach result:', result);

    //     // 获取支付链接
    //     let url: string | null = null;

    //     // 检查不同位置的URL
    //     if (result && typeof result === 'object') {
    //       if (result.data) {
    //         console.log('Success result data:', result.data);
    //         if ('checkout_url' in result.data && typeof result.data.checkout_url === 'string') {
    //           url = result.data.checkout_url;
    //           console.log('Found checkout_url:', url);
    //         } else if ('redirect_url' in result.data && typeof result.data.redirect_url === 'string') {
    //           url = result.data.redirect_url;
    //           console.log('Found redirect_url:', url);
    //         } else if ('url' in result.data && typeof result.data.url === 'string') {
    //           url = result.data.url;
    //           console.log('Found url:', url);
    //         } else {
    //           console.warn('No URL found in result data:', result.data);
    //         }
    //       } else {
    //         console.warn('No result data available');
    //       }
    //     }

    //     if (url) {
    //       setPaymentUrl(url);
    //       console.log('Payment URL:', url);
    //     } else {
    //       console.warn('No payment URL found in result');
    //     }

    //     return result;
    //   }),
    //   {
    //     loading: '正在处理支付请求...',
    //     success: () => {
    //       // 使用之前设置的paymentUrl
    //       if (paymentUrl) {
    //         // 自动跳转到支付页面
    //         console.log('Redirecting to:', paymentUrl);

    //         // 直接跳转，不使用setTimeout
    //         window.location.href = paymentUrl;

    //         return (
    //           <div>
    //             <div>正在跳转到支付页面...</div>
    //             <div>如未自动跳转，请<a href={paymentUrl} target="_blank" rel="noopener noreferrer" className="text-[#B183FF] underline">点击此链接</a></div>
    //           </div>
    //         );
    //       }
    //       return '正在处理支付请求...';
    //     },
    //     error: (error) => {
    //       const errorMsg = error instanceof Error ? error.message : '支付处理失败，请稍后再试';
    //       setErrorMessage(errorMsg);
    //       return errorMsg;
    //     },
    //     finally: () => setIsLoading(false),
    //   }
    // );

  };

  return (
    <Dialog open={!!open} onOpenChange={(open) => setOpen(open ? 'true' : null)}>
      <DialogTrigger asChild>
        <div className="hidden" />
      </DialogTrigger>
      <DialogContent
        className="flex w-auto items-center justify-center rounded-2xl border-none p-1"
        showOverlay
      >
        <div className="relative inline-flex h-auto w-[400px] flex-col items-center justify-center overflow-hidden rounded-2xl border border-[#2D2D2D] bg-zinc-900/50 p-4 outline outline-2 outline-offset-[3.5px] outline-[#2D2D2D]">
          <div className="absolute inset-0 z-0 h-full w-full overflow-hidden">
            <img
              src="/pricing-gradient.png"
              alt="pricing-gradient"
              className="absolute -right-0 -top-52 h-auto w-full"
              height={535}
              width={535}
            />
          </div>

          {/* <div className="relative z-10 flex flex-col items-center justify-start gap-4 self-stretch w-full">
            <h3 className="text-xl font-medium text-white mt-1">选择Token套餐</h3>

            <div className="flex flex-col gap-3 w-full">
          
              <div
                className={cn(
                  "flex items-center justify-between p-3 rounded-lg border w-full cursor-pointer transition-all",
                  selectedPlan === 'basic'
                    ? "border-[#B183FF] bg-[#B183FF]/10"
                    : "border-[#2D2D2D] hover:border-[#B183FF]/50"
                )}
                onClick={() => setSelectedPlan('basic')}
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-5 h-5 rounded-full border-2 flex items-center justify-center",
                    selectedPlan === 'basic'
                      ? "border-[#B183FF]"
                      : "border-white/50"
                  )}>
                    {selectedPlan === 'basic' && (
                      <div className="w-3 h-3 rounded-full bg-[#B183FF]"></div>
                    )}
                  </div>
                  <div>
                    <div className="text-white font-medium">基础套餐</div>
                    <div className="text-xs text-white/60">100 tokens</div>
                  </div>
                </div>
                <div className="text-white font-medium">$1</div>
              </div>

              
              <div
                className={cn(
                  "flex items-center justify-between p-3 rounded-lg border w-full cursor-pointer transition-all",
                  selectedPlan === 'standard'
                    ? "border-[#B183FF] bg-[#B183FF]/10"
                    : "border-[#2D2D2D] hover:border-[#B183FF]/50"
                )}
                onClick={() => setSelectedPlan('standard')}
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-5 h-5 rounded-full border-2 flex items-center justify-center",
                    selectedPlan === 'standard'
                      ? "border-[#B183FF]"
                      : "border-white/50"
                  )}>
                    {selectedPlan === 'standard' && (
                      <div className="w-3 h-3 rounded-full bg-[#B183FF]"></div>
                    )}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-white font-medium">标准套餐</span>
                      <Badge className="text-xs py-0 h-5 bg-[#3F3F3F] text-white border border-[#656565]">
                        热门
                      </Badge>
                    </div>
                    <div className="text-xs text-white/60">400 tokens</div>
                  </div>
                </div>
                <div className="text-white font-medium">$3.9</div>
              </div>

              
              <div
                className={cn(
                  "flex items-center justify-between p-3 rounded-lg border w-full cursor-pointer transition-all",
                  selectedPlan === 'premium'
                    ? "border-[#B183FF] bg-[#B183FF]/10"
                    : "border-[#2D2D2D] hover:border-[#B183FF]/50"
                )}
                onClick={() => setSelectedPlan('premium')}
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-5 h-5 rounded-full border-2 flex items-center justify-center",
                    selectedPlan === 'premium'
                      ? "border-[#B183FF]"
                      : "border-white/50"
                  )}>
                    {selectedPlan === 'premium' && (
                      <div className="w-3 h-3 rounded-full bg-[#B183FF]"></div>
                    )}
                  </div>
                  <div>
                    <div className="text-white font-medium">高级套餐</div>
                    <div className="text-xs text-white/60">1000 tokens (节省20%)</div>
                  </div>
                </div>
                <div className="text-white font-medium">$9.9</div>
              </div>
            </div>

            <button
              className="z-10 inline-flex h-10 mt-2 cursor-pointer items-center justify-center self-stretch overflow-hidden rounded-lg bg-white p-2 outline outline-1 outline-offset-[-1px] disabled:cursor-not-allowed disabled:opacity-50 w-full"
              onClick={handleUpgrade}
              disabled={isLoading}
            >
              <div className="text-center font-medium text-black">
                {isLoading ? '处理中...' : `购买 ${plans[selectedPlan].tokens} tokens`}
              </div>
            </button>

            {paymentUrl && (
              <div className="text-center mt-2 text-sm">
                <a href={paymentUrl} target="_blank" rel="noopener noreferrer" className="text-[#B183FF] underline">
                  点击此处手动跳转到支付页面
                </a>
              </div>
            )}

            {errorMessage && (
              <div className="text-center mt-2 text-sm text-red-500">
                错误: {errorMessage}
              </div>
            )}
          </div> */}

          <div className="relative z-10 mb-[30px] flex flex-1 flex-col items-start justify-start gap-5 self-stretch">
            <h3 className="mt-1 text-xl font-medium text-white">Select Plan</h3>
            {formatedPlanList &&
              formatedPlanList.map((item: any) => {
                const featureList = planFeatures[item.name];
                return (
                  <div
                    key={item.id}
                    className={cn(
                      'flex w-full flex-1 cursor-pointer items-center justify-between rounded-lg border-2 p-3 transition-all',
                      selectedPlan === item.id
                        ? 'border-[#9659ff] bg-[#B183FF]/10'
                        : 'border-white hover:border-[#9659ff]/50',
                    )}
                    onClick={() => setSelectedPlan(item.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          'flex h-5 w-5 items-center justify-center rounded-full border-2',
                          selectedPlan === item.id ? 'border-[#9659ff]' : 'border-white/50',
                        )}
                      >
                        {selectedPlan === item.id && (
                          <div className="h-3 w-3 rounded-full bg-[#9659ff]"></div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-white">{item.name}</span>
                          {item.isPopular && (
                            <Badge className="border border-[#656565] bg-[#3F3F3F] text-white">
                              popular
                            </Badge>
                          )}
                        </div>
                        {featureList.map((f: any) => (
                          <div key={f.featureName} className="text-xs text-white/60">
                            {f.featureDetail}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="font-medium text-white">${item.price}/month</div>
                  </div>
                );
              })}
          </div>

          <button
            className="z-50 inline-flex cursor-pointer items-center justify-center gap-2.5 self-stretch overflow-hidden rounded-lg bg-white p-3 outline outline-1 outline-offset-[-1px] outline-gray-400 disabled:cursor-not-allowed disabled:opacity-50 dark:outline-[#2D2D2D]"
            onClick={handleUpgrade}
            disabled={isLoading}
          >
            <div className="flex items-center justify-center gap-2.5 px-1">
              <div className="justify-start text-center font-semibold leading-none text-black">
                {isLoading ? 'Processing...' : 'Upgrade now'}
              </div>
            </div>
          </button>

        </div>
      </DialogContent>
    </Dialog>
  );
}
