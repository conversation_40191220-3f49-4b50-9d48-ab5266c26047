import type { IntlMessages, Locale } from '@/i18n/config';
import { QueryProvider } from './query-provider';
// import { AutumnProvider } from 'autumn-js/react';
import type { PropsWithChildren } from 'react';
import { IntlProvider } from 'use-intl';

export function ServerProviders({
  children,
  messages,
  locale,
  connectionId,
  connectionEmail,
}: PropsWithChildren<{
  messages: IntlMessages;
  locale: Locale;
  connectionId: string | null;
  connectionEmail: string | null;
}>) {
  return (
    // <AutumnProvider backendUrl={import.meta.env.VITE_PUBLIC_BACKEND_URL}>
      <IntlProvider messages={messages} locale={locale} timeZone={'UTC'}>
        <QueryProvider initialConnectionId={connectionId} initialConnectionEmail={connectionEmail}>
          {children}
        </QueryProvider>
      </IntlProvider>
    // </AutumnProvider>
  );
}
