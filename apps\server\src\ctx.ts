import type { env } from 'cloudflare:workers';
// import type { Autumn } from 'autumn-js';
import type { Auth } from './lib/auth';
import type { DB } from '@zero/db';

export type SessionUser = NonNullable<Awaited<ReturnType<Auth['api']['getSession']>>>['user'];

export type HonoVariables = {
  auth: Auth;
  session: Awaited<ReturnType<Auth['api']['getSession']>>;
  sessionUser?: SessionUser;
  db: DB;
  // autumn: Autumn;
};

export type HonoContext = { Variables: HonoVariables; Bindings: typeof env };
