/**
 * 全局缓存 Hook - 在用户登录后自动启动轮询缓存
 */

import { useState, useEffect, useRef } from 'react';
import { FolderCacheManager, type CacheStatus, type CacheProgress } from '@/lib/folder-cache-manager';
import { cacheManifestManager } from '@/lib/cache-manifest-manager';
import { useTRPCClient } from '@/providers/query-provider';
import { useActiveConnection } from '@/hooks/use-connections';
import { toast } from 'sonner';

interface CacheSettings {
  enabled: boolean;
  interval: number; // 分钟
  maxPages: number;
}

export const useGlobalCache = () => {
  const [status, setStatus] = useState<CacheStatus>('idle');
  const [progress, setProgress] = useState<CacheProgress>({
    current: 0,
    total: 0,
    currentFolder: null,
    completedFolders: [],
    failedFolders: [],
  });

  const managerRef = useRef<FolderCacheManager | null>(null);
  const autoStartTimerRef = useRef<NodeJS.Timeout | null>(null);
  const trpcClient = useTRPCClient();
  const { data: activeConnection } = useActiveConnection();

  // 获取缓存设置
  const getCacheSettings = (): CacheSettings => {
    try {
      const userEmail = activeConnection?.email || 'default';
      const settingsKey = `cacheSettings-${userEmail}`;
      const savedSettings = localStorage.getItem(settingsKey);
      
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return {
          enabled: settings.enabled ?? true,
          interval: settings.interval ?? 2,
          maxPages: settings.maxPages ?? 5,
        };
      }
    } catch (error) {
      console.error('获取缓存设置失败:', error);
    }
    
    // 默认设置
    return {
      enabled: true,
      interval: 2,
      maxPages: 5,
    };
  };

  // 缓存文件夹函数 - 支持轮询缓存
  const cacheFolder = async (folder: string, cursor: string): Promise<{
    emailCount: number;
    nextCursor: string;
    hasMore: boolean;
  }> => {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 📥 开始预缓存文件夹: ${folder}，cursor: ${cursor}`);

    try {
      const pageSize = 10; // 每页10封邮件

      const result = await trpcClient.mail.listThreads.query({
        folder,
        max: pageSize,
        q: '',
        cursor
      });

      const emailCount = result.threads.length;
      const nextCursor = result.nextPageToken || '';
      const hasMore = !!result.nextPageToken && emailCount > 0;

      console.log(`[${new Date().toLocaleString('zh-CN')}] 📊 ${folder} 缓存了 ${emailCount} 封邮件，hasMore: ${hasMore}，nextCursor: ${nextCursor}`);

      return {
        emailCount,
        nextCursor,
        hasMore,
      };
    } catch (error) {
      console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 文件夹预缓存失败: ${folder}`, error);
      throw error;
    }
  };

  // 初始化缓存管理器
  useEffect(() => {
    if (!activeConnection?.email) return;

    const settings = getCacheSettings();
    if (!settings.enabled) {
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🚫 缓存已禁用 (${activeConnection.email})`);
      return;
    }

    console.log(`[${new Date().toLocaleString('zh-CN')}] 🏗️ 初始化全局缓存管理器 (${activeConnection.email})`);

    // 清理之前的定时器
    if (autoStartTimerRef.current) {
      clearTimeout(autoStartTimerRef.current);
      autoStartTimerRef.current = null;
    }

    const initializeManager = async () => {
      managerRef.current = new FolderCacheManager(
        {
          interval: settings.interval * 60 * 1000,
          maxRetries: 3,
          retryDelay: 30 * 1000,
        },
        // 进度回调
        (newProgress) => {
          setProgress(newProgress);
          console.log(`[${new Date().toLocaleString('zh-CN')}] 📊 全局缓存进度: ${newProgress.current}/${newProgress.total}`);
        },
        // 状态回调
        (newStatus) => {
          setStatus(newStatus);
          console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 全局缓存状态: ${newStatus}`);
          
          if (newStatus === 'completed') {
            toast.success('所有文件夹缓存完成！', {
              description: `已缓存 ${progress.completedFolders.length} 个文件夹`,
            });
          } else if (newStatus === 'error') {
            toast.error('文件夹缓存出现错误');
          }
        },
        // 错误回调
        (error, folder) => {
          console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 全局缓存错误: ${folder}`, error);
          // 不显示每个文件夹的错误toast，避免过多通知
        },
        // 缓存函数
        cacheFolder
      );

      // 初始化缓存清单
      const folders = ['inbox', 'drafts', 'sent', 'archive', 'spam', 'bin'];
      await managerRef.current.initializeManifest(
        activeConnection.email,
        activeConnection.email,
        folders
      );

      // 检查是否需要初始化文件夹数据（只对空的文件夹进行初始化）
      console.log(`[${new Date().toLocaleString('zh-CN')}] 📊 检查文件夹初始化状态`);
      const manifest = await cacheManifestManager.getManifest(activeConnection.email);

      for (const folder of folders) {
        const folderData = manifest?.folders[folder];

        // 只对没有缓存数据的文件夹进行初始化
        if (!folderData || (folderData.cachedEmails === 0 && folderData.cachedPages === 0)) {
          try {
            console.log(`[${new Date().toLocaleString('zh-CN')}] 🆕 初始化文件夹: ${folder}`);
            const result = await cacheFolder(folder, '');

            // 更新缓存进度（记录第一页的缓存）
            await cacheManifestManager.updateFolderProgress(
              activeConnection.email,
              folder,
              {
                cachedPages: result.emailCount > 0 ? 1 : 0,
                cachedEmails: result.emailCount,
                lastCachedCursor: result.nextCursor,
                isCompleted: !result.hasMore,
                totalEmails: !result.hasMore ? result.emailCount : undefined,
              }
            );

            console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ ${folder} 初始化完成: ${result.emailCount} 封邮件，hasMore: ${result.hasMore}`);
          } catch (error) {
            console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 初始化 ${folder} 失败:`, error);
          }
        } else {
          console.log(`[${new Date().toLocaleString('zh-CN')}] ⏭️ 跳过 ${folder} 初始化: 已有 ${folderData.cachedEmails} 封邮件，${folderData.cachedPages} 页`);
        }
      }

      // 2分钟后自动开始
      // console.log(`[${new Date().toLocaleString('zh-CN')}] ⏰ 设置全局缓存自动启动定时器（2分钟后）`);
      autoStartTimerRef.current = setTimeout(async () => {
        if (managerRef.current && status === 'idle') {
          console.log(`[${new Date().toLocaleString('zh-CN')}] 🚀 自动启动全局文件夹缓存`);
          await managerRef.current.start();
        } else {
          console.log(`[${new Date().toLocaleString('zh-CN')}] ⚠️ 无法自动启动全局缓存，当前状态: ${status}`);
        }
      }, 2 * 60 * 1000);
    };

    initializeManager().catch(error => {
      console.error('初始化缓存管理器失败:', error);
    });

    return () => {
      if (autoStartTimerRef.current) {
        clearTimeout(autoStartTimerRef.current);
        autoStartTimerRef.current = null;
      }
      if (managerRef.current) {
        managerRef.current.destroy();
      }
    };
  }, [activeConnection?.email]);

  return {
    status,
    progress,
    manager: managerRef.current,
  };
};
