import {
  <PERSON>alog,
  DialogContent,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from '@/components/ui/dialog';
import {
  Archive2,
  Bell,
  CurvedArrow,
  Eye,
  Lightning,
  Mail,
  Star2,
  Tag,
  User,
  X,
  Trash,
  <PERSON>an<PERSON>ye,
  Plus,
  Inbox,
  FolderOpen,
} from '../icons/icons';

const Spam = ({ className = '' }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    className={className}
  >
    <path
      d="M14.6667 3.33333L12.6667 1.33333C12.4889 1.15555 12.2444 1.05555 12 1.05555C11.7555 1.05555 11.5111 1.15555 11.3333 1.33333L4.66667 8.00001V13.3333C4.66667 13.7023 4.96134 14 5.33334 14H13.3333C13.7023 14 14 13.7023 14 13.3333V5.33333C14 4.96433 13.7023 4.66666 13.3333 4.66666H11.3333V2.66666C11.3333 2.29766 11.0357 2.00001 10.6667 2.00001H4.66667L2 4.66666H1.33334C0.96434 4.66666 0.66667 4.96433 0.66667 5.33333V13.3333C0.66667 13.7023 0.96434 14 1.33334 14H13.3333C13.7023 14 14 13.7023 14 13.3333V3.33333C14 2.96433 13.7023 2.66666 13.3333 2.66666H2L4.66667 0H10.6667L14.6667 3.33333ZM6.00001 10.6667H10C10.369 10.6667 10.6667 10.369 10.6667 10C10.6667 9.631 10.369 9.33333 10 9.33333C9.63101 9.33333 9.33334 9.631 9.33334 10C9.33334 10.369 9.63101 10.6667 10 10.6667H6.00001C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667Z"
      fill="#9D9D9D"
    />
  </svg>
);

const Drafts = ({ className = '' }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    className={className}
  >
    <path
      d="M13.3333 2.66666H2.66667C1.93334 2.66666 1.33334 3.26666 1.33334 4V12C1.33334 12.7333 1.93334 13.3333 2.66667 13.3333H13.3333C14.0667 13.3333 14.6667 12.7333 14.6667 12V4C14.6667 3.26666 14.0667 2.66666 13.3333 2.66666ZM13.3333 4V12H2.66667V4H13.3333ZM5.33334 7.33334H10.6667C11.0333 7.33334 11.3333 7.63334 11.3333 8.00001C11.3333 8.36667 11.0333 8.66667 10.6667 8.66667H5.33334C4.96667 8.66667 4.66667 8.36667 4.66667 8.00001C4.66667 7.63334 4.96667 7.33334 5.33334 7.33334ZM4.66667 5.33334H11.3333V6H4.66667V5.33334Z"
      fill="#9D9D9D"
    />
  </svg>
);

const Sent = ({ className = '' }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    className={className}
  >
    <path
      d="M13.6667 2.33334L3.66667 7.33334C3.26667 7.53334 2.86667 7.33334 2.66667 6.93334C2.46667 6.53334 2.66667 6.13334 3.06667 5.93334L13.0667 0.93334C13.4667 0.73334 13.8667 0.93334 14 1.33334C14.2 1.73334 14 2.13334 13.6667 2.33334ZM4.06667 9.53334L3.86667 8.93334C3.76667 8.73334 3.86667 8.53334 4.06667 8.43334C4.26667 8.33334 4.46667 8.33334 4.66667 8.53334L5.66667 9.53334C5.86667 9.73334 5.86667 10.0667 5.66667 10.2667C5.46667 10.4667 5.13334 10.4667 4.93334 10.2667C4.73334 10.0667 4.73334 9.73334 4.93334 9.53334H4.06667V9.53334Z"
      fill="url(#paint0_linear)"
    />
    <defs>
      <linearGradient
        id="paint0_linear"
        x1="4.06667"
        y1="0.93334"
        x2="5.66667"
        y2="10.2667"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#39AE4A" />
        <stop offset="1" stopColor="#39AE4A" />
      </linearGradient>
    </defs>
  </svg>
);
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { useActiveConnection, useConnections } from '@/hooks/use-connections';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Command, RefreshCcw, Settings2Icon, TrashIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ThreadDisplay } from '@/components/mail/thread-display';
import { trpcClient, useTRPC } from '@/providers/query-provider';
import { backgroundQueueAtom } from '@/store/backgroundQueue';
import { handleUnsubscribe } from '@/lib/email-utils.client';
import { useMediaQuery } from '../../hooks/use-media-query';
import { useSearchValue } from '@/hooks/use-search-value';
import { MailList } from '@/components/mail/mail-list';
import { useHotkeysContext } from 'react-hotkeys-hook';
import { useParams, useNavigate } from 'react-router';
import { useMail } from '@/components/mail/use-mail';
import { SidebarToggle } from '../ui/sidebar-toggle';
import { PricingDialog } from '../ui/pricing-dialog';
import { useBrainState } from '@/hooks/use-summary';
import { clearBulkSelectionAtom } from './use-mail';
import AISidebar from '@/components/ui/ai-sidebar';
import { cleanSearchValue, cn } from '@/lib/utils';
import { useThreads } from '@/hooks/use-threads';
import AIToggleButton from '../ai-toggle-button';
import { useIsMobile } from '@/hooks/use-mobile';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { useSession } from '@/lib/auth-client';
import { ScrollArea } from '../ui/scroll-area';
import { useStats } from '@/hooks/use-stats';
import { useTranslations } from 'use-intl';
import { SearchBar } from './search-bar';
import { useQueryState } from 'nuqs';
import { useAtom } from 'jotai';
import { toast } from 'sonner';

interface ITag {
  id: string;
  name: string;
  usecase: string;
  text: string;
}

export const defaultLabels = [
  {
    name: 'to respond',
    usecase: 'emails you need to respond to. NOT sales, marketing, or promotions.',
  },
  {
    name: 'FYI',
    usecase:
      'emails that are not important, but you should know about. NOT sales, marketing, or promotions.',
  },
  {
    name: 'comment',
    usecase:
      'Team chats in tools like Google Docs, Slack, etc. NOT marketing, sales, or promotions.',
  },
  {
    name: 'notification',
    usecase: 'Automated updates from services you use. NOT sales, marketing, or promotions.',
  },
  {
    name: 'promotion',
    usecase: 'Sales, marketing, cold emails, special offers or promotions. NOT to respond to.',
  },
  {
    name: 'meeting',
    usecase: 'Calendar events, invites, etc. NOT sales, marketing, or promotions.',
  },
  {
    name: 'billing',
    usecase: 'Billing notifications. NOT sales, marketing, or promotions.',
  },
];

const AutoLabelingSettings = () => {
  const trpc = useTRPC();
  const [open, setOpen] = useState(false);
  const { data: storedLabels } = useQuery(trpc.brain.getLabels.queryOptions());
  const { mutateAsync: updateLabels, isPending } = useMutation(
    trpc.brain.updateLabels.mutationOptions(),
  );
  const [labels, setLabels] = useState<ITag[]>([]);
  const [newLabel, setNewLabel] = useState({ name: '', usecase: '' });
  const { mutateAsync: EnableBrain, isPending: isEnablingBrain } = useMutation(
    trpc.brain.enableBrain.mutationOptions(),
  );
  const { mutateAsync: DisableBrain, isPending: isDisablingBrain } = useMutation(
    trpc.brain.disableBrain.mutationOptions(),
  );
  const { data: brainState, refetch: refetchBrainState } = useBrainState();

  useEffect(() => {
    if (storedLabels) {
      setLabels(
        storedLabels.map((label) => ({
          id: label.name,
          name: label.name,
          text: label.name,
          usecase: label.usecase,
        })),
      );
    }
  }, [storedLabels]);

  const handleResetToDefault = useCallback(() => {
    setLabels(
      defaultLabels.map((label) => ({
        id: label.name,
        name: label.name,
        text: label.name,
        usecase: label.usecase,
      })),
    );
  }, [storedLabels]);

  const handleAddLabel = () => {
    if (!newLabel.name || !newLabel.usecase) return;
    setLabels([...labels, { id: newLabel.name, ...newLabel, text: newLabel.name }]);
    setNewLabel({ name: '', usecase: '' });
  };

  const handleDeleteLabel = (id: string) => {
    setLabels(labels.filter((label) => label.id !== id));
  };

  const handleUpdateLabel = (id: string, field: 'name' | 'usecase', value: string) => {
    setLabels(
      labels.map((label) =>
        label.id === id
          ? { ...label, [field]: value, text: field === 'name' ? value : label.text }
          : label,
      ),
    );
  };

  const handleSubmit = async () => {
    const updatedLabels = labels.map((label) => ({
      name: label.name,
      usecase: label.usecase,
    }));

    if (newLabel.name.trim() && newLabel.usecase.trim()) {
      updatedLabels.push({
        name: newLabel.name,
        usecase: newLabel.usecase,
      });
    }
    await updateLabels({ labels: updatedLabels });
    setOpen(false);
    toast.success('Labels updated successfully, Onemails will start using them.');
  };

  const handleEnableBrain = useCallback(async () => {
    toast.promise(EnableBrain({}), {
      loading: 'Enabling autolabeling...',
      success: 'Autolabeling enabled successfully',
      error: 'Failed to enable autolabeling',
      finally: async () => {
        await refetchBrainState();
      },
    });
  }, []);

  const handleDisableBrain = useCallback(async () => {
    toast.promise(DisableBrain({}), {
      loading: 'Disabling autolabeling...',
      success: 'Autolabeling disabled successfully',
      error: 'Failed to disable autolabeling',
      finally: async () => {
        await refetchBrainState();
      },
    });
  }, []);

  const handleToggleAutolabeling = useCallback(() => {
    if (brainState?.enabled) {
      handleDisableBrain();
    } else {
      handleEnableBrain();
    }
  }, [brainState?.enabled]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div className="flex items-center gap-2">
          {/* <div
            className={cn(
              'h-2 w-2 animate-pulse rounded-full',
              brainState?.enabled ? 'bg-green-400' : 'bg-red-400',
            )}
          /> */}

          <Switch
            disabled={isEnablingBrain || isDisablingBrain}
            checked={brainState?.enabled ?? false}
          />
          <span className="text-muted-foreground cursor-pointer text-xs">Auto label</span>
        </div>
      </DialogTrigger>
      <DialogContent showOverlay className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Autolabeling Settings</DialogTitle>
        </DialogHeader>
        <DialogDescription className="mb-4 mt-2">
          These are the labels Zero uses to autolabel your incoming emails. Feel free to modify them
          however you like. Zero will create a new label in your account for each label you add - if
          it does not exist already.
        </DialogDescription>
        <ScrollArea className="h-[400px]">
          <div className="space-y-2">
            {labels.map((label) => (
              <div key={label.id} className="flex items-start gap-2 rounded-lg border p-3">
                <div className="flex-1 space-y-2">
                  <input
                    type="text"
                    value={label.name}
                    onChange={(e) => handleUpdateLabel(label.id, 'name', e.target.value)}
                    className="w-full rounded-md border px-2 py-1 text-sm"
                    placeholder="Label name"
                  />
                  <textarea
                    value={label.usecase}
                    onChange={(e) => handleUpdateLabel(label.id, 'usecase', e.target.value)}
                    className="w-full rounded-md border px-2 py-1 text-sm"
                    placeholder="Label use case"
                    rows={2}
                  />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handleDeleteLabel(label.id)}
                >
                  <Trash className="h-4 w-4 fill-[#F43F5E]" />
                </Button>
              </div>
            ))}
            <div className="flex items-start gap-2 rounded-lg border p-3">
              <div className="flex-1 space-y-2">
                <input
                  type="text"
                  value={newLabel.name}
                  onChange={(e) => setNewLabel({ ...newLabel, name: e.target.value })}
                  className="w-full rounded-md border px-2 py-1 text-sm"
                  placeholder="New label name"
                />
                <textarea
                  value={newLabel.usecase}
                  onChange={(e) => setNewLabel({ ...newLabel, usecase: e.target.value })}
                  className="w-full rounded-md border px-2 py-1 text-sm"
                  placeholder="New label use case"
                  rows={2}
                />
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleAddLabel}
                disabled={!newLabel.name || !newLabel.usecase}
              >
                <Plus className="h-4 w-4 fill-white" />
              </Button>
            </div>
          </div>
        </ScrollArea>
        <DialogFooter className="mt-4">
          <div className="flex w-full justify-between">
            <Button onClick={handleToggleAutolabeling} variant="outline" size="sm">
              {brainState?.enabled ? 'Disable' : 'Enable'}
            </Button>
            <div className="flex gap-2">
              <Button onClick={handleResetToDefault} variant="outline" size="sm">
                Use default labels
              </Button>
              <Button disabled={isPending} onClick={handleSubmit} size="sm">
                Save
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export function YahooMailLayout() {
  const params = useParams<{ folder: string }>();
  const folder = params?.folder ?? 'inbox';
  const [mail, setMail] = useMail();
  const [, clearBulkSelection] = useAtom(clearBulkSelectionAtom);
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { data: session, isPending } = useSession();
  const { data: connections } = useConnections();
  const t = useTranslations();
  const prevFolderRef = useRef(folder);
  const { enableScope, disableScope } = useHotkeysContext();
  const { data: activeConnection } = useActiveConnection();

  const activeAccount = useMemo(() => {
    if (!activeConnection?.id || !connections?.connections) return null;
    return connections.connections.find((connection) => connection.id === activeConnection?.id);
  }, [activeConnection?.id, connections?.connections]);

  useEffect(() => {
    if (prevFolderRef.current !== folder && mail.bulkSelected.length > 0) {
      clearBulkSelection();
    }
    prevFolderRef.current = folder;
  }, [folder, mail.bulkSelected.length, clearBulkSelection]);

  useEffect(() => {
    if (!session?.user && !isPending) {
      navigate('/login');
    }
  }, [session?.user, isPending]);

  const [{ isFetching, refetch: refetchThreads }] = useThreads();
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const [threadId, setThreadId] = useQueryState('threadId');

  useEffect(() => {
    if (threadId) {
      console.log('Enabling thread-display scope, disabling mail-list');
      enableScope('thread-display');
      disableScope('mail-list');
    } else {
      console.log('Enabling mail-list scope, disabling thread-display');
      enableScope('mail-list');
      disableScope('thread-display');
    }

    return () => {
      console.log('Cleaning up mail/thread scopes');
      disableScope('thread-display');
      disableScope('mail-list');
    };
  }, [threadId, enableScope, disableScope]);

  const [, setActiveReplyId] = useQueryState('activeReplyId');

  const handleClose = useCallback(() => {
    setThreadId(null);
    setActiveReplyId(null);
  }, [setThreadId]);

  // Add mailto protocol handler registration
  useEffect(() => {
    // Register as a mailto protocol handler if browser supports it
    if (typeof window !== 'undefined' && 'registerProtocolHandler' in navigator) {
      try {
        // Register the mailto protocol handler
        // When a user clicks a mailto: link, it will be passed to our dedicated handler
        // which will:
        // 1. Parse the mailto URL to extract email, subject and body
        // 2. Create a draft with these values
        // 3. Redirect to the compose page with just the draft ID
        // This ensures we don't keep the email content in the URL
        navigator.registerProtocolHandler('mailto', `/api/mailto-handler?mailto=%s`);
      } catch (error) {
        console.error('Failed to register protocol handler:', error);
      }
    }
  }, []);

  const category = useQueryState('category');

  return (
    <TooltipProvider delayDuration={0}>
      <PricingDialog />
      <div className="rounded-inherit relative z-[5] flex p-0 md:mt-2">
        <ResizablePanelGroup
          direction="horizontal"
          autoSaveId="mail-panel-layout"
          className="rounded-inherit overflow-hidden"
        >
          <ResizablePanel
            defaultSize={40}
            minSize={40}
            maxSize={50}
            className={cn(
              `bg-panelLight dark:bg-panelDark mb-2 h-full w-fit shadow-sm md:rounded-2xl md:border lg:flex lg:shadow-sm dark:border-[#252525]`,
              isDesktop && threadId && 'hidden lg:block',
            )}
          >
            <div className="w-full md:h-[calc(100dvh-20px)]">
              <div
                className={cn(
                  'sticky top-0 z-[15] flex items-center justify-between gap-1.5 border-b p-2 px-[20px] transition-colors md:min-h-14 dark:border-[#252525]',
                )}
              >
                <div className="flex w-full items-center justify-between gap-2">
                  <div>
                    <SidebarToggle className="h-fit px-2" />
                  </div>

                  <div className="flex items-center gap-2">
                    <div>
                      {mail.bulkSelected.length > 0 ? (
                        <div>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <button
                                onClick={() => {
                                  setMail({ ...mail, bulkSelected: [] });
                                }}
                                className="flex h-6 items-center gap-1 rounded-md bg-[#313131] px-2 text-xs text-[#A0A0A0] hover:bg-[#252525]"
                              >
                                <X className="h-3 w-3 fill-[#A0A0A0]" />
                                <span>esc</span>
                              </button>
                            </TooltipTrigger>
                            <TooltipContent>
                              {t('common.actions.exitSelectionModeEsc')}
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      ) : null}
                    </div>
                    <AutoLabelingSettings />
                    <div className="dark:bg-iconDark/20 relative ml-2 h-3 w-0.5 rounded-full bg-[#E7E7E7]" />
                    <Button
                      onClick={() => {
                        refetchThreads();
                      }}
                      variant="ghost"
                      className="md:h-fit md:px-2"
                    >
                      <RefreshCcw className="text-muted-foreground h-4 w-4 cursor-pointer" />
                    </Button>
                  </div>
                </div>
              </div>
              <div className="p-2 px-[22px]">
                <SearchBar />
                <div className="mt-2">
{activeAccount?.providerId === 'google' && folder === 'inbox' && (
  <CategorySelect isMultiSelectMode={mail.bulkSelected.length > 0} />
)}
                </div>
              </div>
              <div
                className={cn(
                  `${category[0] === 'Important' ? 'bg-[#F59E0D]' : category[0] === 'All Mail' ? 'bg-[#006FFE]' : category[0] === 'Personal' ? 'bg-[#39ae4a]' : category[0] === 'Updates' ? 'bg-[#8B5CF6]' : category[0] === 'Promotions' ? 'bg-[#F43F5E]' : category[0] === 'Unread' ? 'bg-[#FF4800]' : 'bg-[#F59E0D]'}`,
                  'relative bottom-0.5 z-[5] h-0.5 w-full transition-opacity',
                  isFetching ? 'opacity-100' : 'opacity-0',
                )}
              />
              <div className="relative z-[1] h-[calc(100dvh-(2px+88px+49px+2px)] overflow-hidden pt-0 md:h-[calc(100dvh-9.8rem)]">
                <MailList />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle className="mr-0.5 hidden opacity-0 md:block" />

          {isDesktop && (
            <ResizablePanel
              className={cn(
                'bg-panelLight dark:bg-panelDark mb-2 h-full mr-2 w-fit rounded-2xl border shadow-sm dark:border-[#252525]',
                // Only show on md screens and larger when there is a threadId
                !threadId && 'hidden lg:block',
              )}
              defaultSize={30}
              minSize={30}
            >
              <div className="lg:h-[calc(100dvh-(20px)] relative h-[calc(100dvh-(20px))] flex-1">
                <ThreadDisplay />
              </div>
            </ResizablePanel>
          )}

          {/* Mobile Thread View */}
          {isMobile && threadId && (
            <div className="bg-panelLight dark:bg-panelDark fixed inset-0 z-50">
              <div className="flex h-full flex-col">
                <div className="h-full overflow-y-auto outline-none">
                  <ThreadDisplay />
                </div>
              </div>
            </div>
          )}

          <AISidebar />
          <AIToggleButton />
        </ResizablePanelGroup>
      </div>
    </TooltipProvider>
  );
}

function BulkSelectActions() {
  const t = useTranslations();
  const [errorQty, setErrorQty] = useState(0);
  const [threadId, setThreadId] = useQueryState('threadId');
  const [isLoading, setIsLoading] = useState(false);
  const [isUnsub, setIsUnsub] = useState(false);
  const [mail, setMail] = useMail();
  const params = useParams<{ folder: string }>();
  const folder = params?.folder ?? 'inbox';
  const [{ refetch: refetchThreads }] = useThreads();
  const { refetch: refetchStats } = useStats();
  const trpc = useTRPC();
  const { mutateAsync: markAsRead } = useMutation(trpc.mail.markAsRead.mutationOptions());
  const { mutateAsync: markAsImportant } = useMutation(
    trpc.mail.markAsImportant.mutationOptions(),
  );
  const { mutateAsync: bulkArchive } = useMutation(trpc.mail.bulkArchive.mutationOptions());
  const { mutateAsync: bulkStar } = useMutation(trpc.mail.bulkStar.mutationOptions());
  const [, setBackgroundQueue] = useAtom(backgroundQueueAtom);
  const { mutateAsync: bulkDeleteThread } = useMutation(
    trpc.mail.bulkDelete.mutationOptions(),
  );
  const queryClient = useQueryClient();

  const handleMassUnsubscribe = async () => {
    setIsLoading(true);
    toast.promise(
      Promise.all(
        mail.bulkSelected.filter(Boolean).map(async (bulkSelected) => {
          await new Promise((resolve) => setTimeout(resolve, 499));
          const emailData = await trpcClient.mail.get.query({ id: bulkSelected });
          if (emailData) {
            const firstEmail = emailData.latest;
            if (firstEmail)
              return handleUnsubscribe({ emailData: firstEmail }).catch((e) => {
                toast.error(e.message ?? 'Unknown error while unsubscribing');
                setErrorQty((eq) => eq++);
              });
          }
        }),
      ).then(async () => {
        setIsUnsub(false);
        setIsLoading(false);
        await refetchThreads();
        await refetchStats();
        setMail({ ...mail, bulkSelected: [] });
      }),
      {
        loading: 'Unsubscribing...',
        success: 'All done! you will no longer receive emails from these mailing lists.',
        error: 'Something went wrong!',
      },
    );
  };

  const onMoveSuccess = useCallback(async () => {
    if (threadId && mail.bulkSelected.includes(threadId)) setThreadId(null);
    refetchThreads();
    refetchStats();
    await Promise.all(
      mail.bulkSelected.map((threadId) =>
        queryClient.invalidateQueries({ queryKey: trpc.mail.get.queryKey({ id: threadId }) }),
      ),
    );
    setMail({ ...mail, bulkSelected: [] });
  }, [mail, setMail, refetchThreads, refetchStats, threadId, setThreadId]);

  return (
    <div className="flex items-center gap-2">
      <button
        className="flex h-8 flex-1 items-center justify-center gap-1 overflow-hidden rounded-md border bg-white px-3 text-sm transition-all duration-300 ease-out hover:bg-gray-100 dark:border-none dark:bg-[#313131] dark:hover:bg-[#313131]/80"
        onClick={() => {
          if (mail.bulkSelected.length === 0) return;
          toast.promise(markAsRead({ ids: mail.bulkSelected }).then(onMoveSuccess), {
            loading: 'Marking as read...',
            success: 'All done! marked as read',
            error: 'Something went wrong!',
          });
        }}
      >
        <div className="relative overflow-visible">
          <Eye className="fill-[#9D9D9D] dark:fill-[#9D9D9D]" />
        </div>
        <div className="flex items-center justify-center gap-2.5">
          <div className="justify-start leading-none">Mark all as read</div>
        </div>
      </button>

      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className="flex aspect-square h-8 items-center justify-center gap-1 overflow-hidden rounded-md border bg-white px-2 text-sm transition-all duration-300 ease-out hover:bg-gray-100 dark:border-none dark:bg-[#313131] dark:hover:bg-[#313131]/80"
            onClick={() => {
              if (mail.bulkSelected.length === 0) return;
              toast.promise(markAsImportant({ ids: mail.bulkSelected }).then(onMoveSuccess), {
                loading: 'Marking as important...',
                success: 'All done! marked as important',
                error: 'Something went wrong!',
              });
            }}
          >
            <div className="relative overflow-visible">
              <Lightning className="fill-[#9D9D9D] dark:fill-[#9D9D9D]" />
            </div>
          </button>
        </TooltipTrigger>
        <TooltipContent>{t('common.mail.markAsImportant')}</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className="flex aspect-square h-8 items-center justify-center gap-1 overflow-hidden rounded-md border bg-white px-2 text-sm transition-all duration-300 ease-out hover:bg-gray-100 dark:border-none dark:bg-[#313131] dark:hover:bg-[#313131]/80"
            onClick={() => {
              if (mail.bulkSelected.length === 0) return;
              toast.promise(bulkArchive({ ids: mail.bulkSelected }).then(onMoveSuccess), {
                loading: 'Moving to archive...',
                success: 'All done! moved to archive',
                error: 'Something went wrong!',
              });
            }}
          >
            <div className="relative overflow-visible">
              <Archive2 className="fill-[#9D9D9D]" />
            </div>
          </button>
        </TooltipTrigger>
        <TooltipContent>{t('common.mail.archive')}</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className="flex aspect-square h-8 items-center justify-center gap-1 overflow-hidden rounded-md border bg-white px-2 text-sm transition-all duration-300 ease-out hover:bg-gray-100 dark:border-none dark:bg-[#313131] dark:hover:bg-[#313131]/80"
            onClick={() => {
              if (mail.bulkSelected.length === 0) return;
              toast.promise(bulkStar({ ids: mail.bulkSelected }).then(onMoveSuccess), {
                loading: 'Marking as starred...',
                success: 'All done! marked as starred',
                error: 'Something went wrong!',
              });
            }}
          >
            <div className="relative overflow-visible">
              <Star2 className="fill-[#9D9D9D] stroke-[#9D9D9D] dark:stroke-[#9D9D9D]" />
            </div>
          </button>
        </TooltipTrigger>
        <TooltipContent>{t('common.mail.starAll')}</TooltipContent>
      </Tooltip>

      <Dialog onOpenChange={setIsUnsub} open={isUnsub}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DialogTrigger asChild>
              <button className="flex aspect-square h-8 items-center justify-center gap-1 overflow-hidden rounded-md border bg-white px-2 text-sm transition-all duration-300 ease-out hover:bg-gray-100 dark:border-none dark:bg-[#313131] dark:hover:bg-[#313131]/80"
              onClick={() => {
                if (mail.bulkSelected.length === 0) return;
                toast.promise(
                  new Promise((resolve, reject) => {
                    mail.bulkSelected.map((id) =>
                      setBackgroundQueue({ type: 'add', threadId: `thread:${id}` }),
                    );
                    return bulkDeleteThread({ ids: mail.bulkSelected }).then(resolve).catch(reject);
                  }).then(onMoveSuccess),
                  {
                    success: 'All done! moved to bin',
                    error: 'Something went wrong!',
                  },
                );
              }}
              >
                <div className="relative overflow-visible">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2.3}
                    stroke="currentColor"
                    className="size-4"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"
                      strokeOpacity={0.6}
                    />
                  </svg>
                </div>
              </button>
            </DialogTrigger>
          </TooltipTrigger>
        </Tooltip>

        <DialogContent
          showOverlay
          onKeyDown={(e) => {
            if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
              e.preventDefault();
              handleMassUnsubscribe();
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>Mass Unsubscribe</DialogTitle>
            <DialogDescription>
              We will remove you from all of the mailing lists in the selected threads. If your
              action is required to unsubscribe from certain threads, you will be notified.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" className="mt-3 h-8" onClick={() => setIsUnsub(false)}>
              <span>Cancel</span>{' '}
            </Button>
            <Button
              className="mt-3 h-8 [&_svg]:size-3.5"
              disabled={isLoading}
              onClick={handleMassUnsubscribe}
            >
              {<span>Unsubscribe</span>}
              <div className="flex h-5 items-center justify-center gap-1 rounded-sm bg-white/10 px-1 dark:bg-black/10">
                <Command className="h-2 w-3 text-white dark:text-[#929292]" />
                <CurvedArrow className="mt-1.5 h-5 w-3.5 fill-white dark:fill-[#929292]" />
              </div>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className="flex aspect-square h-8 items-center justify-center gap-1 overflow-hidden rounded-md border bg-white px-2 text-sm transition-all duration-300 ease-out hover:bg-gray-100 dark:border-none dark:bg-[#313131] dark:hover:bg-[#313131]/80 hover:dark:bg-[#411D23]/60"
            onClick={() => {
              if (mail.bulkSelected.length === 0) return;
              toast.promise(
                new Promise((resolve, reject) => {
                  mail.bulkSelected.map((id) =>
                    setBackgroundQueue({ type: 'add', threadId: `thread:${id}` }),
                  );
                  return bulkDeleteThread({ ids: mail.bulkSelected }).then(resolve).catch(reject);
                }).then(onMoveSuccess),
                {
                  success: 'All done! moved to bin',
                  error: 'Something went wrong!',
                },
              );
            }}
          >
            <div className="relative overflow-visible">
              <Trash className="fill-[#F43F5E]" />
            </div>
          </button>
        </TooltipTrigger>
        <TooltipContent>{t('common.mail.moveToBin')}</TooltipContent>
      </Tooltip>
    </div>
  );
}

export const Categories = () => {
  const t = useTranslations();
  const [category] = useQueryState('category', {
    defaultValue: 'Inbox',
  });
  return [
    {
      id: 'Inbox',
      name: 'Inbox',
      searchValue: 'NOT is:sent NOT is:draft',
      icon: (
        <Inbox
          className={cn('fill-[#006FFE] dark:fill-white', category === 'Inbox' && 'fill-white')}
        />
      ),
      colors:
        'border-0 bg-[#006FFE] text-white dark:bg-[#006FFE] dark:text-white dark:hover:bg-[#006FFE]/90',
    },
    {
      id: 'Sent',
      name: 'Sent',
      searchValue: 'is:sent',
      icon: (
        <SendIcon
          className={cn('fill-[#39ae4a] dark:fill-white', category === 'Sent' && 'fill-white')}
        />
      ),
      colors:
        'border-0 bg-[#39ae4a] text-white dark:bg-[#39ae4a] dark:text-white dark:hover:bg-[#39ae4a]/90',
    },
    {
      id: 'Drafts',
      name: 'Drafts',
      searchValue: 'is:draft',
      icon: (
        <DraftsIcon
          className={cn('fill-[#8B5CF6] dark:fill-white', category === 'Drafts' && 'fill-white')}
        />
      ),
      colors:
        'border-0 bg-[#8B5CF6] text-white dark:bg-[#8B5CF6] dark:text-white dark:hover:bg-[#8B5CF6]/90',
    },
    {
      id: 'Spam',
      name: 'Spam',
      searchValue: 'is:spam',
      icon: (
        <SpamIcon
          className={cn('fill-[#F43F5E] dark:fill-white', category === 'Spam' && 'fill-white')}
        />
      ),
      colors:
        'border-0 bg-[#F43F5E] text-white dark:bg-[#F43F5E] dark:text-white dark:hover:bg-[#F43F5E]/90',
    },
    {
      id: 'Trash',
      name: 'Trash',
      searchValue: 'is:trash',
      icon: (
        <TrashIcon
          className={cn('fill-[#F59E0D] dark:fill-white', category === 'Trash' && 'fill-white')}
        />
      ),
      colors:
        'border-0 bg-[#F59E0D] text-white dark:bg-[#F59E0D] dark:text-white dark:hover:bg-[#F59E0D]/90',
    },
    {
      id: 'Folders',
      name: 'Folders',
      searchValue: 'in:folders',
      icon: (
        <FolderOpen
          className={cn(
            'fill-[#8B5CF6] dark:fill-white',
            category === 'Folders' && 'fill-white',
          )}
        />
      ),
      colors:
        'border-0 bg-[#8B5CF6] text-white dark:bg-[#8B5CF6] dark:text-white dark:hover:bg-[#8B5CF6]/90',
    },
  ];
};

type CategoryType = ReturnType<typeof Categories>[0];

function getCategoryColor(categoryId: string): string {
  switch (categoryId.toLowerCase()) {
    case 'primary':
      return 'bg-[#006FFE]';
    case 'all mail':
      return 'bg-[#006FFE]';
    case 'important':
      return 'bg-[#F59E0D]';
    case 'promotions':
      return 'bg-[#F43F5E]';
    case 'personal':
      return 'bg-[#39ae4a]';
    case 'updates':
      return 'bg-[#8B5CF6]';
    case 'unread':
      return 'bg-[#FF4800]';
    default:
      return 'bg-base-primary-500';
  }
}

function CategorySelect({ isMultiSelectMode }: { isMultiSelectMode: boolean }) {
  const [mail, setMail] = useMail();
  const [searchValue, setSearchValue] = useSearchValue();
  const categories = Categories();
  const params = useParams<{ folder: string }>();
  const folder = params?.folder ?? 'inbox';
  const [category, setCategory] = useQueryState('category', {
    defaultValue: 'Inbox',
  });
  const containerRef = useRef<HTMLDivElement>(null);
  const activeTabElementRef = useRef<HTMLButtonElement>(null);

  // Only show category selection for inbox folder
  if (folder !== 'inbox') return <div className="h-8"></div>;

  // Primary category is always the first one
  const primaryCategory = categories[0];
  if (!primaryCategory) return null;

  const renderCategoryButton = (cat: CategoryType, isOverlay = false, idx: number) => {
    const isSelected = cat.id === (category || 'Primary');
    const bgColor = getCategoryColor(cat.id);

    return (
      <Tooltip key={cat.id}>
        <TooltipTrigger asChild>
          <Button
            ref={!isOverlay ? activeTabElementRef : null}
            onClick={() => {
              setCategory(cat.id);
              setSearchValue({
                value: `${cat.searchValue} ${cleanSearchValue(searchValue.value).trim().length ? `AND ${cleanSearchValue(searchValue.value)}` : ''}`,
                highlight: searchValue.highlight,
                folder: '',
              });
            }}
            className={cn(
              'flex h-8 items-center justify-center gap-1 overflow-hidden rounded-lg border transition-all duration-300 ease-out dark:border-none',
              isSelected
                ? cn('flex-[1_0_auto] border-none px-2 text-white', bgColor)
                : 'flex-1 bg-white hover:bg-gray-100 dark:bg-[#313131] dark:hover:bg-[#313131]/80',
            )}
            tabIndex={isOverlay ? -1 : undefined}
          >
            <div className="relative overflow-visible">{cat.icon}</div>
            {!isSelected && <span>{cat.name}</span>}
            {isSelected && (
              <div className="flex items-center justify-center gap-2.5 px-0.5">
                <div className="animate-in fade-in-0 slide-in-from-right-4 justify-start text-sm leading-none text-white duration-300">
                  {cat.name}
                </div>
              </div>
            )}
          </Button>
        </TooltipTrigger>
        {!isSelected && (
          <TooltipContent side="top" className={`${idx === 0 ? 'ml-4' : ''}`}>
            <span className="mr-2">{cat.name}</span>
            <kbd
              className={cn(
                'border-muted-foreground/10 bg-accent h-6 rounded-[6px] border px-1.5 font-mono text-xs leading-6',
                '-me-1 ms-auto inline-flex max-h-full items-center',
              )}
            >
              {idx + 1}
            </kbd>
          </TooltipContent>
        )}
      </Tooltip>
    );
  };

  // Update clip path when category changes
  useEffect(() => {
    const container = containerRef.current;
    const activeTabElement = activeTabElementRef.current;

    if (category && container && activeTabElement) {
      const { offsetLeft, offsetWidth } = activeTabElement;
      const clipLeft = Math.max(0, offsetLeft - 2);
      const clipRight = Math.min(container.offsetWidth, offsetLeft + offsetWidth + 2);
      const containerWidth = container.offsetWidth;

      if (containerWidth) {
        container.style.clipPath = `inset(0 ${Number(100 - (clipRight / containerWidth) * 100).toFixed(2)}% 0 ${Number((clipLeft / containerWidth) * 100).toFixed(2)}%)`;
      }
    }
  }, [category]);

  if (isMultiSelectMode) {
    return <BulkSelectActions />;
  }

  return (
    <div className="relative w-full">
      <div className="flex w-full items-start justify-start gap-2">
      {categories.map((cat, idx) => renderCategoryButton(cat, false, idx)).slice(0, 3)}
    </div>

    <div
      aria-hidden
      className="pointer-events-none absolute inset-0 z-10 overflow-hidden transition-[clip-path] duration-300 ease-in-out"
      ref={containerRef}
    >
      <div className="flex w-full items-start justify-start gap-2">
        {categories.map((cat, idx) => renderCategoryButton(cat, true, idx))}
      </div>
    </div>
  );
    </div>

    <div
      aria-hidden
      className="pointer-events-none absolute inset-0 z-10 overflow-hidden transition-[clip-path] duration-300 ease-in-out"
      ref={containerRef}
    >
      <div className="flex w-full items-start justify-start gap-2">
        {categories.map((cat, idx) => renderCategoryButton(cat, true, idx))}
      </div>
    </div>
  );
}

function SendIcon({ className = '' }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={className}
    >
      <path
        d="M13.6667 2.33334L3.66667 7.33334C3.26667 7.53334 2.86667 7.33334 2.66667 6.93334C2.46667 6.53334 2.66667 6.13334 3.06667 5.93334L13.0667 0.93334C13.4667 0.73334 13.8667 0.93334 14 1.33334C14.2 1.73334 14 2.13334 13.6667 2.33334ZM4.06667 7.53334L13.6667 2.33334V9.33334L4.06667 7.53334Z"
        fill="#9D9D9D"
      />
    </svg>
  );
}

function DraftsIcon({ className = '' }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={className}
    >
      <path
        d="M13.3333 2.66666H2.66667C1.93334 2.66666 1.33334 3.26666 1.33334 4V12C1.33334 12.7333 1.93334 13.3333 2.66667 13.3333H13.3333C14.0667 13.3333 14.6667 12.7333 14.6667 12V4C14.6667 3.26666 14.0667 2.66666 13.3333 2.66666ZM13.3333 4V12H2.66667V4H13.3333ZM5.33334 7.33334H10.6667C11.0333 7.33334 11.3333 7.63334 11.3333 8.00001C11.3333 8.36667 11.0333 8.66667 10.6667 8.66667H5.33334C4.96667 8.66667 4.66667 8.36667 4.66667 8.00001C4.66667 7.63334 4.96667 7.33334 5.33334 7.33334ZM4.66667 5.33334H11.3333V6H4.66667V5.33334Z"
        fill="#9D9D9D"
      />
    </svg>
  );
}

function SpamIcon({ className = '' }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={className}
    >
      <path
        d="M14.6667 3.33333L12.6667 1.33333C12.4889 1.15555 12.2444 1.05555 12 1.05555C11.7555 1.05555 11.5111 1.15555 11.3333 1.33333L4.66667 8.00001V13.3333C4.66667 13.7023 4.96134 14 5.33334 14H13.3333C13.7023 14 14 13.7023 14 13.3333V3.33333C14 2.96433 13.7023 2.66666 13.3333 2.66666H2L4.66667 0H10.6667L14.6667 3.33333ZM6.00001 10.6667H10C10.369 10.6667 10.6667 10.369 10.6667 10C10.6667 9.631 10.369 9.33333 10 9.33333C9.63101 9.33333 9.33334 9.631 9.33334 10C9.33334 10.369 9.63101 10.6667 10 10.6667H6.00001C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333"
        fill="#9D9D9D"
      />
    </svg>
  );
}

function SendIcon({ className = '' }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={className}
    >
      <path
        d="M13.6667 2.33334L3.66667 7.33334C3.26667 7.53334 2.86667 7.33334 2.66667 6.93334C2.46667 6.53334 2.66667 6.13334 3.06667 5.93334L13.0667 0.93334C13.4667 0.73334 13.8667 0.93334 14 1.33334C14.2 1.73334 14 2.13334 13.6667 2.33334ZM4.06667 7.53334L13.6667 2.33334V9.33334L4.06667 7.53334Z"
        fill="#9D9D9D"
      />
    </svg>
  );
}

function DraftsIcon({ className = '' }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={className}
    >
      <path
        d="M13.3333 2.66666H2.66667C1.93334 2.66666 1.33334 3.26666 1.33334 4V12C1.33334 12.7333 1.93334 13.3333 2.66667 13.3333H13.3333C14.0667 13.3333 14.6667 12.7333 14.6667 12V4C14.6667 3.26666 14.0667 2.66666 13.3333 2.66666H2L4.66667 0H10.6667L14.6667 3.33333Z"
        fill="#9D9D9D"
      />
    </svg>
  );
}

function SpamIcon({ className = '' }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={className}
    >
      <path
        d="M14.6667 3.33333L12.6667 1.33333C12.4889 1.15555 12.2444 1.05555 12 1.05555C11.7555 1.05555 11.5111 1.15555 11.3333 1.33333L4.66667 8.00001V13.3333C4.66667 13.7023 4.96134 14 5.33334 14H13.3333C13.7023 14 14 13.7023 14 13.3333V5.33333C14 4.96433 13.7023 4.66666 13.3333 4.66666H11.3333V2.66666C11.3333 2.29766 11.0357 2.00001 10.6667 2.00001H4.66667L2 4.66666H1.33334C0.96434 4.66666 0.66667 4.96433 0.66667 5.33333V13.3333C0.66667 13.7023 0.96434 14 1.33334 14H13.3333C13.7023 14 14 13.7023 14 13.3333V3.33333C14 2.96433 13.7023 2.66666 13.3333 2.66666H2L4.66667 0H10.6667L14.6667 3.33333ZM6.00001 10.6667H10C10.369 10.6667 10.6667 10.369 10.6667 10C10.6667 9.631 10.369 9.33333 10 9.33333C9.63101 9.33333 9.33334 9.631 9.33334 10C9.33334 10.369 9.63101 10.6667 10 10.6667H6.00001C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C6.00001 10.6667 5.63101 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333C6.36901 9.33333 6.66667 9.631 6.66667 10C6.66667 10.369 6.36901 10.6667 6.00001 10.6667C5.63101 10.6667 5.33334 10.369 5.33334 10C5.33334 9.631 5.63101 9.33333 6.00001 9.33333
