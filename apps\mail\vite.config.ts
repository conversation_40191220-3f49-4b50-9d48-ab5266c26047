import { cloudflare } from '@cloudflare/vite-plugin';
import { reactRouter } from '@react-router/dev/vite';
import tsconfigPaths from 'vite-tsconfig-paths';
import tailwindcss from 'tailwindcss';
import { defineConfig } from 'vite';
import dedent from 'dedent';

export default defineConfig({
  plugins: [
    cloudflare({ viteEnvironment: { name: 'ssr' } }),
    reactRouter(),
    tsconfigPaths(),
    {
      name: 'add-headers',
      applyToEnvironment: (env) => env.name === 'client',
      generateBundle() {
        this.emitFile({
          fileName: '_headers',
          type: 'asset',
          source: dedent`
            # Autogenerated

            /assets/*
              ! Cache-Control
                Cache-Control: public, immutable, max-age=31536000
          `,
        });
      },
    },
  ],
  server: {
    port: 3000,
    allowedHosts: ['.onemails.ai'],
    fs: {
      strict: true,
      // Allow serving files from two level up to the project root
      allow: ['../..'],
    },
  },
  preview: {
    port: 3000,
  },
  css: {
    postcss: {
      plugins: [tailwindcss()],
    },
  },
  build: {
    sourcemap: true,
  },
  resolve: {
    alias: {
      tslib: 'tslib/tslib.es6.js',
    },
  },
});
