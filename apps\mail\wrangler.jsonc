{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "zero",
  "compatibility_date": "2025-05-01",
  "compatibility_flags": ["nodejs_compat"],
  "main": "./worker.ts",
  "observability": {
    "enabled": true,
  },
  "env": {
    "local": {
      "vars": {
        "VITE_PUBLIC_BACKEND_URL": "http://localhost:8787",
        "VITE_PUBLIC_APP_URL": "http://localhost:3000",
      },
    },
    "staging": {
      "vars": {
        "VITE_PUBLIC_BACKEND_URL": "https://sapi.0.email",
        "VITE_PUBLIC_APP_URL": "https://staging.0.email",
      },
    },
    "production": {
      "vars": {
        "VITE_PUBLIC_BACKEND_URL": "https://tarotbest.com:8787",
        "VITE_PUBLIC_APP_URL": "https://tarotbest.com:3003",
      },
    },
  },
}
