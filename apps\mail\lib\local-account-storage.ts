/**
 * 本地缓存账号存储系统 - 使用 localStorage
 * 重点：QueryProvider 直接取本地的，不等待后端
 */

export interface LocalAccount {
  id: string;
  email: string;
  name?: string;
  provider?: string;
  isActive: boolean; //  当前选中状态
  lastUsed: Date;
  createdAt: Date;
}

//  账号存储结构 - 字典格式
export interface AccountStorage {
  accounts: Record<string, LocalAccount>; // 以邮箱为键
  activeEmail: string | null; // 当前选中的邮箱
  lastUpdated: Date;
}

class LocalAccountStorage {
  private storageKey = 'zero-local-accounts';

  /**
   * 获取存储数据
   */
  private getStorage(): AccountStorage {
    if (typeof window === 'undefined') {
      return { accounts: {}, activeEmail: null, lastUpdated: new Date() };
    }

    try {
      const data = localStorage.getItem(this.storageKey);
      if (data) {
        const parsed = JSON.parse(data) as AccountStorage;
        // 转换日期字符串为 Date 对象
        Object.values(parsed.accounts).forEach(account => {
          account.lastUsed = new Date(account.lastUsed);
          account.createdAt = new Date(account.createdAt);
        });
        parsed.lastUpdated = new Date(parsed.lastUpdated);
        return parsed;
      }
      return { accounts: {}, activeEmail: null, lastUpdated: new Date() };
    } catch (error) {
      console.error('获取账号存储失败:', error);
      return { accounts: {}, activeEmail: null, lastUpdated: new Date() };
    }
  }

  /**
   * 保存存储数据
   */
  private saveStorage(data: AccountStorage): void {
    if (typeof window === 'undefined') return;

    try {
      data.lastUpdated = new Date();
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      console.log(`[${new Date().toLocaleString('zh-CN')}] 💾 保存账号存储:`, Object.keys(data.accounts).length, '个账号');
    } catch (error) {
      console.error('保存账号存储失败:', error);
    }
  }

  /**
   *  1. 用户登录时 - 设置为选中
   */
  setActiveOnLogin(email: string): void {
    try {
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔑 开始设置登录账号:`, email);

      const storage = this.getStorage();
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 当前存储状态:`, {
        账号数量: Object.keys(storage.accounts).length,
        活跃邮箱: storage.activeEmail,
        目标邮箱: email
      });

      // 如果账号不存在，创建一个基本账号
      if (!storage.accounts[email]) {
        console.log(`[${new Date().toLocaleString('zh-CN')}] ➕ 账号不存在，创建新账号:`, email);
        storage.accounts[email] = {
          id: `login-${Date.now()}`,
          email,
          name: email,
          provider: 'unknown',
          isActive: true,
          lastUsed: new Date(),
          createdAt: new Date(),
        };
      } else {
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 账号已存在，更新状态:`, email);
        // 更新现有账号
        storage.accounts[email].isActive = true;
        storage.accounts[email].lastUsed = new Date();
      }

      // 取消其他账号的选中状态
      Object.values(storage.accounts).forEach(account => {
        if (account.email !== email) {
          account.isActive = false;
        }
      });

      storage.activeEmail = email;
      this.saveStorage(storage);

      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 用户登录设置活跃账号完成:`, email);
    } catch (error) {
      console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 设置登录账号失败:`, error);
    }
  }

  /**
   *  2. 获取后台账号列表时 - 更新本地账号
   */
  updateFromBackend(connections: any[], activeConnectionId?: string): void {
    if (!connections || connections.length === 0) return;

    try {
      const storage = this.getStorage();
      const now = new Date();

      // 更新账号信息，保留本地状态
      connections.forEach(conn => {
        const existing = storage.accounts[conn.email];
        const isActive = activeConnectionId ? conn.id === activeConnectionId : existing?.isActive || false;

        storage.accounts[conn.email] = {
          id: conn.id,
          email: conn.email,
          name: conn.name || conn.email,
          provider: conn.provider || 'unknown',
          isActive,
          lastUsed: existing?.lastUsed || now,
          createdAt: existing?.createdAt || now,
        };
      });

      // 设置活跃账号
      if (activeConnectionId) {
        const activeConn = connections.find(c => c.id === activeConnectionId);
        if (activeConn) {
          // 取消其他账号的选中状态
          Object.values(storage.accounts).forEach(account => {
            account.isActive = account.email === activeConn.email;
          });
          storage.activeEmail = activeConn.email;
          storage.accounts[activeConn.email].lastUsed = now;
        }
      }

      this.saveStorage(storage);
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 从后端更新账号:`, connections.length, '个账号');
    } catch (error) {
      console.error('更新后端账号失败:', error);
    }
  }

  /**
   *  3. 添加新账号时 - 本地添加
   */
  addAccount(account: Omit<LocalAccount, 'isActive' | 'lastUsed' | 'createdAt'>): boolean {
    try {
      const storage = this.getStorage();

      if (storage.accounts[account.email]) {
        console.warn(`账号已存在: ${account.email}`);
        return false;
      }

      const now = new Date();
      storage.accounts[account.email] = {
        ...account,
        isActive: false,
        lastUsed: now,
        createdAt: now,
      };

      this.saveStorage(storage);
      console.log(`[${new Date().toLocaleString('zh-CN')}] ➕ 添加新账号:`, account.email);
      return true;
    } catch (error) {
      console.error('添加账号失败:', error);
      return false;
    }
  }

  /**
   *  4. 切换账号时 - 本地更新
   */
  switchAccount(email: string): boolean {
    try {
      const storage = this.getStorage();

      if (!storage.accounts[email]) {
        console.error(`账号不存在: ${email}`);
        return false;
      }

      // 更新所有账号的选中状态
      Object.values(storage.accounts).forEach(account => {
        account.isActive = account.email === email;
        if (account.email === email) {
          account.lastUsed = new Date();
        }
      });

      storage.activeEmail = email;
      this.saveStorage(storage);

      // 🎯 触发自定义事件通知 QueryProvider
      window.dispatchEvent(new CustomEvent('activeEmailChanged', {
        detail: { email }
      }));

      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 切换账号:`, email);
      return true;
    } catch (error) {
      console.error('切换账号失败:', error);
      return false;
    }
  }

  /**
   *  5. QueryProvider 直接取本地的
   */
  getActiveEmail(): string | null {
    try {
      const storage = this.getStorage();
      
      if (storage.activeEmail && storage.accounts[storage.activeEmail]) {
        console.log(`[${new Date().toLocaleString('zh-CN')}] 📧 从本地缓存获取活跃邮箱:`, storage.activeEmail);
        return storage.activeEmail;
      }

      // 如果没有活跃邮箱，选择最近使用的
      const accounts = Object.values(storage.accounts);
      if (accounts.length > 0) {
        const mostRecent = accounts.sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime())[0];
        this.switchAccount(mostRecent.email);
        return mostRecent.email;
      }

      return null;
    } catch (error) {
      console.error('获取活跃邮箱失败:', error);
      return null;
    }
  }

  /**
   * 获取所有账号（字典格式）
   */
  getAccountsDict(): Record<string, LocalAccount> {
    const storage = this.getStorage();
    return storage.accounts;
  }

  /**
   * 获取所有账号（数组格式）
   */
  getAccountsList(): LocalAccount[] {
    const accounts = this.getAccountsDict();
    return Object.values(accounts).sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime());
  }

  /**
   * 删除账号
   */
  removeAccount(email: string): boolean {
    try {
      const storage = this.getStorage();

      if (!storage.accounts[email]) {
        console.error(`账号不存在: ${email}`);
        return false;
      }

      const wasActive = storage.accounts[email].isActive;
      delete storage.accounts[email];

      // 如果删除的是活跃账号，选择另一个
      if (wasActive) {
        const remaining = Object.values(storage.accounts);
        if (remaining.length > 0) {
          const nextActive = remaining.sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime())[0];
          this.switchAccount(nextActive.email);
        } else {
          storage.activeEmail = null;
        }
      }

      this.saveStorage(storage);
      console.log(`[${new Date().toLocaleString('zh-CN')}] ❌ 删除账号:`, email);
      return true;
    } catch (error) {
      console.error('删除账号失败:', error);
      return false;
    }
  }

  /**
   * 清除所有数据
   */
  clearAll(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(this.storageKey);
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 清除所有账号数据`);
    } catch (error) {
      console.error('清除数据失败:', error);
    }
  }

  /**
   * 检查是否有数据
   */
  hasData(): boolean {
    try {
      const storage = this.getStorage();
      return Object.keys(storage.accounts).length > 0;
    } catch (error) {
      return false;
    }
  }
}

// 单例实例
export const localAccountStorage = new LocalAccountStorage();

// 便捷函数
export const setActiveOnLogin = (email: string) => localAccountStorage.setActiveOnLogin(email);
export const updateFromBackend = (connections: any[], activeConnectionId?: string) => 
  localAccountStorage.updateFromBackend(connections, activeConnectionId);
export const addAccount = (account: Omit<LocalAccount, 'isActive' | 'lastUsed' | 'createdAt'>) => 
  localAccountStorage.addAccount(account);
export const switchAccount = (email: string) => localAccountStorage.switchAccount(email);
export const getActiveEmail = () => localAccountStorage.getActiveEmail();
export const getAccountsDict = () => localAccountStorage.getAccountsDict();
export const getAccountsList = () => localAccountStorage.getAccountsList();
