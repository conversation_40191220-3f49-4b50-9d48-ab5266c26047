/**
 * 账号存储管理 Hook - 使用 localStorage
 * 处理登录、后端更新、添加、切换等操作
 */

import { useEffect, useState, useRef } from 'react';
import { useActiveConnection, useConnections } from '@/hooks/use-connections';
import { useSession } from '@/lib/auth-client';
import {
  localAccountStorage,
  setActiveOnLogin,
  updateFromBackend,
  switchAccount,
  getAccountsList,
  type LocalAccount
} from '@/lib/local-account-storage';

export function useAccountStorage() {
  const { data: connections } = useConnections();
  const { data: activeConnection } = useActiveConnection();
  const { data: session } = useSession();
  const [localAccounts, setLocalAccounts] = useState<LocalAccount[]>([]);
  const [activeEmail, setActiveEmail] = useState<string | null>(null);
  const lastSessionRef = useRef<string | null>(null);

  // 🔍 调试日志 - 监控数据变化
  useEffect(() => {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 useAccountStorage数据状态:`, {
      session用户ID: session?.user?.id,
      session用户邮箱: session?.user?.email,
      活跃连接ID: activeConnection?.id,
      活跃连接邮箱: activeConnection?.email,
      连接数量: connections?.connections?.length || 0,
      上次会话ID: lastSessionRef.current
    });
  }, [session?.user?.id, session?.user?.email, activeConnection?.id, activeConnection?.email, connections?.connections?.length]);

  // 🎯 检测OAuth登录 - 当用户通过OAuth登录时自动初始化本地存储
  useEffect(() => {
    if (session?.user?.id && activeConnection?.email) {
      const currentSessionId = session.user.id;

      // 检查是否是新的登录会话
      if (lastSessionRef.current !== currentSessionId) {
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 检测到新的OAuth登录会话:`, {
          用户邮箱: session.user.email,
          活跃连接邮箱: activeConnection.email,
          会话ID: currentSessionId,
          上次会话ID: lastSessionRef.current
        });

        // 🎯 使用活跃连接的邮箱初始化本地账号存储
        try {
          setActiveOnLogin(activeConnection.email);
          console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ OAuth登录本地账号存储已初始化:`, activeConnection.email);
        } catch (error) {
          console.error('OAuth登录本地账号存储初始化失败:', error);
        }

        lastSessionRef.current = currentSessionId;
      }
    }
  }, [session?.user?.id, activeConnection?.email]);

  // 🎯 2. 获取后台账号列表时 - 更新本地账号
  useEffect(() => {
    if (connections?.connections && connections.connections.length > 0) {
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 后端账号列表更新，共 ${connections.connections.length} 个账号`);
      
      updateFromBackend(connections.connections, activeConnection?.id);
      refreshLocalState();
    }
  }, [connections, activeConnection]);

  // 刷新本地状态
  const refreshLocalState = () => {
    try {
      const accounts = getAccountsList();
      const email = localAccountStorage.getActiveEmail();

      setLocalAccounts(accounts);
      setActiveEmail(email);
    } catch (error) {
      console.error('刷新本地状态失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    refreshLocalState();
  }, []);

  //  1. 用户登录时 - 设置为选中
  const handleLogin = (email: string) => {
    try {
      setActiveOnLogin(email);
      refreshLocalState();
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔑 处理用户登录:`, email);
    } catch (error) {
      console.error('处理登录失败:', error);
    }
  };

  //  3. 添加新账号时 - 本地添加
  const handleAddAccount = (account: Omit<LocalAccount, 'isActive' | 'lastUsed' | 'createdAt'>) => {
    try {
      const success = localAccountStorage.addAccount(account);
      if (success) {
        refreshLocalState();
        console.log(`[${new Date().toLocaleString('zh-CN')}] ➕ 处理添加账号:`, account.email);
      }
      return success;
    } catch (error) {
      console.error('处理添加账号失败:', error);
      return false;
    }
  };

  //  4. 切换账号时 - 本地更新
  const handleSwitchAccount = (email: string) => {
    try {
      const success = switchAccount(email);
      if (success) {
        refreshLocalState();
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 处理账号切换:`, email);
        
        // 刷新页面以应用新的缓存配置
        window.location.reload();
      }
      return success;
    } catch (error) {
      console.error('处理账号切换失败:', error);
      return false;
    }
  };

  // 删除账号
  const handleRemoveAccount = (email: string) => {
    try {
      const success = localAccountStorage.removeAccount(email);
      if (success) {
        refreshLocalState();
        console.log(`[${new Date().toLocaleString('zh-CN')}] ❌ 处理删除账号:`, email);
        
        // 如果删除的是当前活跃账号，刷新页面
        if (email === activeEmail) {
          window.location.reload();
        }
      }
      return success;
    } catch (error) {
      console.error('处理删除账号失败:', error);
      return false;
    }
  };

  // 清除所有数据
  const handleClearAll = () => {
    try {
      localAccountStorage.clearAll();
      refreshLocalState();
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 处理清除所有数据`);
      window.location.reload();
    } catch (error) {
      console.error('处理清除数据失败:', error);
    }
  };

  return {
    // 状态
    localAccounts,
    activeEmail,
    hasData: localAccounts.length > 0,
    
    // 操作方法
    handleLogin,           // 1. 用户登录时
    handleAddAccount,      //  3. 添加新账号时
    handleSwitchAccount,   //  4. 切换账号时
    handleRemoveAccount,   // 删除账号
    handleClearAll,        // 清除所有数据
    refreshLocalState,     // 手动刷新状态
  };
}
