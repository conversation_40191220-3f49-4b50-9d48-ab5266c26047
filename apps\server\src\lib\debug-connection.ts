import SMTPTransport from 'nodemailer/lib/smtp-transport';
import * as nodemailer from 'nodemailer';
import { ImapFlow } from 'imapflow';

// 连接锁，防止并发连接到同一服务器
const connectionLocks = new Map<string, Promise<any>>();

export async function testImapConnection(
  host: string,
  port: number,
  secure: boolean,
  user: string,
  pass: string,
  useTLS?: boolean,
): Promise<{ success: boolean; error?: string }> {
  const lockKey = `${user}@${host}:${port}`;

  // 检查是否有正在进行的连接
  if (connectionLocks.has(lockKey)) {
    console.log(`Waiting for existing connection to complete for ${lockKey}...`);
    try {
      await connectionLocks.get(lockKey);
    } catch (error) {
      // 忽略之前连接的错误
    }
  }

  // 创建新的连接Promise
  const connectionPromise = performImapConnection(host, port, secure, user, pass);
  connectionLocks.set(lockKey, connectionPromise);

  try {
    const result = await connectionPromise;
    return result;
  } finally {
    connectionLocks.delete(lockKey);
  }
}

async function performImapConnection(
  host: string,
  port: number,
  secure: boolean,
  user: string,
  pass: string,
): Promise<{ success: boolean; error?: string }> {
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(
        `Testing IMAP connection to ${host}:${port} (secure: ${secure}) for user ${user} (attempt ${attempt}/${maxRetries})`,
      );

      // 使用主机映射（如果需要）
      const mappedHost =  host;
      console.log(`Host mapping: ${host} -> ${mappedHost}`);

      // 创建IMAP客户端，添加更多配置选项
      const client = new ImapFlow({
        host: mappedHost,
        port,
        secure,
        auth: {
          user,
          pass,
        },
        logger: false, // 禁用详细日志
        disableAutoEnable: true, // 禁用自动启用扩展
      });

      // 尝试连接
      console.log('Attempting IMAP connection...');
      await client.connect();
      console.log('IMAP connection successful!');

      // 获取服务器信息
      console.log('Server info:', client.serverInfo);

      // 测试基本操作
      const mailboxes = await client.list();
      console.log(`Found ${mailboxes.length} mailboxes`);

      // 关闭连接
      await client.logout();
      console.log('IMAP connection test completed successfully!');

      return { success: true };

    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      console.error(`IMAP connection test failed (attempt ${attempt}/${maxRetries}):`, lastError.message);
      console.error('Full error details:', error);

      // 如果是认证失败，不需要重试
      if (lastError.message.includes('Authentication Failed') ||
          lastError.message.includes('AUTHENTICATIONFAILED') ||
          lastError.message.includes('Command failed')) {
        console.error('Authentication or command failed - not retrying');
        break;
      }

      // 等待一段时间再重试
      if (attempt < maxRetries) {
        console.log(`Waiting 2 seconds before retry...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }

  return {
    success: false,
    error: lastError?.message || 'Unknown error',
  };
}

export async function testSmtpConnection(
  host: string,
  port: number,
  secure: boolean,
  user: string,
  pass: string,
  useTLS?: boolean,
): Promise<{ success: boolean; error?: string }> {
  // 直接使用域名，不使用IP映射
  const actualHost =  host;
  console.log(
    `Testing SMTP connection to ${actualHost}:${port} (original: ${host}, secure: ${secure}, TLS: ${useTLS}) for user ${user}`,
  );
  let transport: nodemailer.Transporter | null = null;

  try {
    // Configure transport with proper STARTTLS support
    const transportConfig: SMTPTransport.Options = {
      host: actualHost,
      port,
      secure, // true for SSL (port 465), false for STARTTLS (port 587)
      auth: {
        user,
        pass,
      },
      // 增加超时时间，给不同邮箱服务商更多时间
      connectionTimeout: host.includes('163.com') ? 30000 : 15000, // 163邮箱需要更长时间
      greetingTimeout: host.includes('163.com') ? 30000 : 15000,
      socketTimeout: host.includes('163.com') ? 30000 : 15000,
    };

    // For services using port 587, enable STARTTLS
    if (port === 587 && !secure) {
      const isEthereal = host === '**************' || host.includes('ethereal.email');

      transportConfig.requireTLS = true; // 启用STARTTLS
      transportConfig.tls = {
        minVersion: 'TLSv1.2',
        ...(isEthereal && {
          servername: 'smtp.ethereal.email', // 为IP连接提供正确的SNI
        }),
      };
    }

    // QQ邮箱配置 - QQ邮箱工作正常，保持原有配置
    if (host.includes('qq.com')) {
      if (port === 465 && secure) {
        transportConfig.tls = {
          minVersion: 'TLSv1.2',
          rejectUnauthorized: false,
        };
      }
    }

    // 163邮箱配置 - 单独处理，支持多个端口
    if (host.includes('163.com')) {
      // 配置TLS设置
      if (port === 465 && secure) {
        // 465端口SSL连接
        transportConfig.tls = {
          minVersion: 'TLSv1.2',
          rejectUnauthorized: false,
        };
      } else if (port === 587 && !secure && useTLS) {
        // 587端口STARTTLS连接
        transportConfig.tls = {
          minVersion: 'TLSv1.2',
          rejectUnauthorized: false,
        };
      } else if (port === 994 && secure) {
        // 994端口SSL连接 - 163邮箱的备选端口
        transportConfig.tls = {
          minVersion: 'TLSv1.2',
          rejectUnauthorized: false,
        };
      }

      // 163邮箱需要更长的超时时间
      transportConfig.connectionTimeout = 60000;
      transportConfig.greetingTimeout = 60000;
      transportConfig.socketTimeout = 60000;
    }

    // Create transport
    transport = nodemailer.createTransport(transportConfig);

    // Verify connection with timeout - 163邮箱需要更长时间
    const timeoutMs = host.includes('163.com') ? 60000 : 15000; // 163邮箱60秒超时
    await Promise.race([
      transport.verify(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('SMTP connection timeout')), timeoutMs),
      ),
    ]);

    console.log('SMTP connection verified successfully!');
    return { success: true };
  } catch (error) {
    console.error('SMTP connection test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  } finally {
    if (transport) {
      transport.close();
      transport = null;
    }
  }
}
