import { createRateLimiterMiddleware, privateProcedure, router, publicProcedure } from '../trpc';
import { testImapConnection, testSmtpConnection } from '../../lib/debug-connection';
import { account, connection, user as user_ } from '@zero/db/schema';
import { getActiveConnection } from '../../lib/server-utils';
import { Ratelimit } from '@upstash/ratelimit';
import { TRPCError } from '@trpc/server';
import { and, eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';
import { imapSmtpConfig } from '../../lib/driver/config-imap-smtp';
import { encrypt } from '../../lib/hash-utils';
import { logConnectionError } from '../../lib/error-logger';

// 根据邮箱域名自动配置IMAP/SMTP设置
function getAutoConfigForDomain(domain: string) {
  const configs: Record<
    string,
    {
      imapHost: string;
      imapPort: number;
      imapSecure: boolean;
      smtpHost: string;
      smtpPort: number;
      smtpSecure: boolean;
    }
  > = imapSmtpConfig;

  return (
    configs[domain] || {
      imapHost: '',
      imapPort: 993,
      imapSecure: true,
      smtpHost: '',
      smtpPort: 465,
      smtpSecure: true,
    }
  );
}

const imapSmtpConnectionSchema = z.object({
  provider: z.literal('credential'),
  auth: z.object({
    email: z.string().email(),
    refreshToken: z.string(),
    host: z.string().optional(), // 允许空字符串，服务器端自动配置
    port: z
      .string()
      .optional()
      .transform((val) => (val ? parseInt(val, 10) : undefined)),
    secure: z.boolean().optional(),
    tls: z.boolean().optional(),
    smtpHost: z.string().optional(), // 允许空字符串，服务器端自动配置
    smtpPort: z
      .string()
      .optional()
      .transform((val) => (val ? parseInt(val, 10) : undefined)),
    smtpSecure: z.boolean().optional(),
    smtpTLS: z.boolean().optional(),
  }),
  isAddConnectionMode: z.boolean(),
});

const testConnectionSchema = z.object({
  email: z.string().email(),
  password: z.string(),
  imapHost: z.string(),
  imapPort: z.string().transform((val) => parseInt(val, 10)),
  imapSecure: z.boolean(),
  imapTLS: z.boolean().optional(),
  smtpHost: z.string(),
  smtpPort: z.string().transform((val) => parseInt(val, 10)),
  smtpSecure: z.boolean(),
  smtpTLS: z.boolean().optional(),
});
export const connectionsRouter = router({
  testConnection: privateProcedure.input(testConnectionSchema).mutation(async ({ input }) => {
    const [imapResult, smtpResult] = await Promise.all([
      testImapConnection(
        input.imapHost,
        input.imapPort,
        input.imapSecure,
        input.email,
        input.password,
        input.imapTLS,
      ),
      testSmtpConnection(
        input.smtpHost,
        input.smtpPort,
        input.smtpSecure,
        input.email,
        input.password,
        input.smtpTLS,
      ),
    ]);

    return {
      imapTest: imapResult,
      smtpTest: smtpResult,
      success: imapResult.success && smtpResult.success,
    };
  }),

  // 公开的连接测试端点，用于登录前验证
  testConnectionPublic: publicProcedure.input(testConnectionSchema).mutation(async ({ input }) => {
    console.log(`开始验证IMAP连接: ${input.email}`);

    try {
      // 使用现有的测试函数进行验证
      console.log('测试IMAP连接...');
      const imapResult = await testImapConnection(
        input.imapHost,
        input.imapPort,
        input.imapSecure,
        input.email,
        input.password,
        false, // useTLS = false
      );

      if (!imapResult.success) {
        //监听错误
        console.log('IMAP连接测试失败:', imapResult.error);

        // 🎯 记录IMAP连接错误日志
        await logConnectionError(
          input.email,
          imapResult.error || 'Unknown IMAP error',
          'IMAP',
          {
            host: input.imapHost,
            port: input.imapPort,
            secure: input.imapSecure
          }
        );

        return {
          imapTest: imapResult,
          smtpTest: { success: false, error: 'IMAP连接失败，跳过SMTP测试' },
          success: false,
        };
      }

      console.log('✅ IMAP连接测试成功');

      // IMAP成功后测试SMTP连接
      console.log('测试SMTP连接...');
      let smtpResult = await testSmtpConnection(
        input.smtpHost,
        input.smtpPort,
        input.smtpSecure,
        input.email,
        input.password,
        false, // useTLS = false
      );

      // 如果是163邮箱且465端口失败，尝试其他端口
      if (!smtpResult.success && input.email.includes('@163.com') && input.smtpPort === 465) {
        console.log('465端口失败，尝试994端口SSL...');
        smtpResult = await testSmtpConnection(
          input.smtpHost,
          994, // 使用994端口
          true, // SSL连接
          input.email,
          input.password,
          false, // useTLS = false for SSL
        );

        // 如果994端口也失败，尝试587端口STARTTLS
        if (!smtpResult.success) {
          console.log('994端口失败，尝试587端口STARTTLS...');
          smtpResult = await testSmtpConnection(
            input.smtpHost,
            587, // 使用587端口
            false, // STARTTLS，不是SSL
            input.email,
            input.password,
            true, // useTLS = true for STARTTLS
          );
        }
      }

      if (!smtpResult.success) {
        console.log('SMTP连接测试失败:', smtpResult.error);

        // 🎯 记录SMTP连接错误日志
        await logConnectionError(
          input.email,
          smtpResult.error || 'Unknown SMTP error',
          'SMTP',
          {
            host: input.smtpHost,
            port: input.smtpPort,
            secure: input.smtpSecure
          }
        );
      } else {
        console.log('✅ SMTP连接测试成功');
      }

        return {
          imapTest: imapResult,
          smtpTest: smtpResult,
          success: imapResult.success && smtpResult.success,
        };

      } catch (error) {
        console.error('连接测试过程中发生错误:', error);
        return {
          imapTest: { success: false, error: error instanceof Error ? error.message : '未知错误' },
          smtpTest: { success: false, error: '由于IMAP测试失败，跳过SMTP测试' },
          success: false,
        };
      }
    }),

  // 🎯 新增：检查邮箱是否已存在于系统中
  checkEmailExists: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .query(async ({ input, ctx }) => {
      const { db } = ctx;

      console.log(`🔍 检查邮箱是否已存在: ${input.email}`);

      // 查找 connection 表中是否有该邮箱
      const existingConnection = await db
        .select()
        .from(connection)
        .where(eq(connection.email, input.email))
        .limit(1);

      if (existingConnection.length > 0) {
        const conn = existingConnection[0]!;
        console.log(`✅ 找到已存在的连接，用户ID: ${conn.userId}`);

        // 查找对应的用户信息
        const user = await db
          .select()
          .from(user_)
          .where(eq(user_.id, conn.userId))
          .limit(1);

        return {
          exists: true,
          userId: conn.userId,
          userEmail: user[0]?.email,
          userName: user[0]?.name,
        };
      }

      console.log(`ℹ️ 邮箱不存在于系统中: ${input.email}`);
      return {
        exists: false,
      };
    }),

  // 🎯 新增：获取已存在用户的 credential account 密码
  getExistingUserCredentials: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .query(async ({ input, ctx }) => {
      const { db } = ctx;

      console.log(`🔑 获取已存在用户的 credential 信息: ${input.email}`);

      // 查找该邮箱的 credential account
      const credentialAccount = await db
        .select()
        .from(account)
        .where(and(
          eq(account.providerId, 'credential'),
          eq(account.accountId, input.email)
        ))
        .limit(1);

      if (credentialAccount.length === 0) {
        console.log(`❌ 未找到 credential account: ${input.email}`);
        return {
          exists: false,
        };
      }

      console.log(`✅ 找到 credential account: ${input.email}`);

      return {
        exists: true,
        userId: credentialAccount[0]!.userId,
        // 不返回实际密码，只返回存在状态
      };
    }),

  list: privateProcedure
    .use(
      createRateLimiterMiddleware({
        limiter: Ratelimit.slidingWindow(60, '1m'),
        generatePrefix: ({ session }) => `ratelimit:get-connections-${session?.user.id}`,
      }),
    )
    .query(async ({ ctx }) => {
      const { db, session } = ctx;

      // 获取用户的默认连接ID
      const userData = await db.query.user.findFirst({
        where: eq(user_.id, session.user.id),
      });
      const activeConnectionId = userData?.defaultConnectionId;

      const connections = await db
        .select({
          id: connection.id,
          email: connection.email,
          name: connection.name,
          picture: connection.picture,
          createdAt: connection.createdAt,
          providerId: connection.providerId,
          accessToken: connection.accessToken,
          refreshToken: connection.refreshToken,
        })
        .from(connection)
        .where(eq(connection.userId, session.user.id));

      const disconnectedIds = connections
        .filter((c) => {
          // 🎯 修正连接断开检测逻辑
          if (c.providerId === 'credential') {
            // IMAP 连接：只需要 refreshToken（存储密码），accessToken 可以为空
            return !c.refreshToken;
          } else {
            // OAuth 连接（google, microsoft）：需要两个 token
            return !c.accessToken || !c.refreshToken;
          }
        })
        .map((c) => c.id);

      // 对连接进行排序：当前活跃连接排在第一位
      const sortedConnections = connections.sort((a, b) => {
        if (a.id === activeConnectionId) return -1;
        if (b.id === activeConnectionId) return 1;
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

      return {
        connections: sortedConnections.map((connection) => {
          return {
            id: connection.id,
            email: connection.email,
            name: connection.name,
            picture: connection.picture,
            createdAt: connection.createdAt,
            providerId: connection.providerId,
            isActive: connection.id === activeConnectionId, // 添加活跃状态标识
          };
        }),
        disconnectedIds,
        activeConnectionId,
      };
    }),
  setDefault: privateProcedure
    .input(z.object({ connectionId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const { connectionId } = input;
      const { db } = ctx;
      const user = ctx.session.user;

      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 后端收到设置默认连接请求: ${connectionId}, 用户: ${user.id}`);

      const foundConnection = await db.query.connection.findFirst({
        where: and(eq(connection.id, connectionId), eq(connection.userId, user.id)),
      });

      if (!foundConnection) {
        console.log(`[${new Date().toLocaleString('zh-CN')}] ❌ 连接未找到: ${connectionId}`);
        throw new TRPCError({ code: 'NOT_FOUND' });
      }

      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 找到连接: ${foundConnection.email} (${foundConnection.providerId})`);

      await db
        .update(user_)
        .set({ defaultConnectionId: connectionId })
        .where(eq(user_.id, user.id));

      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 后端默认连接已更新: ${connectionId} -> ${foundConnection.email}`);
    }),
  delete: privateProcedure
    .input(z.object({ connectionId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const { connectionId } = input;
      const { db } = ctx;
      const user = ctx.session.user;

      // 获取要删除的连接信息
      const targetConnection = await db.query.connection.findFirst({
        where: and(eq(connection.id, connectionId), eq(connection.userId, user.id)),
      });

      if (!targetConnection) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '连接不存在' });
      }

      console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 准备删除连接: ${targetConnection.email} (${targetConnection.providerId})`);

      // 🔍 检查用户总共有多少个连接
      const userConnections = await db
        .select({ id: connection.id, providerId: connection.providerId, email: connection.email, })
        .from(connection)
        .where(eq(connection.userId, user.id));

      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 用户 ${user.id} 当前有 ${userConnections.length} 个连接`);

      // 如果这是最后一个连接，清除整个组的数据并登出用户
      if (userConnections.length <= 1) {
        // 当前要删除的是最后一个连接
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🚪 删除最后一个连接，清除整个组的数据并登出用户`);

        // 🗑️ 清除该组所有的 connection 数据
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 清除用户 ${user.id} 的所有 connection 数据`);
        await db
          .delete(connection)
          .where(eq(connection.userId, user.id));

        // 🗑️ 清除该组所有的 account 数据
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 清除用户 ${user.id} 的所有 account 数据`);
        await db
          .delete(account)
          .where(eq(account.userId, user.id));

        // 清除该组的user数据
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 清除用户 ${user.id} 的所有 user 数据`);
        await db
        .delete(user_)
        .where(eq(user_.id, user.id));

        // 登出用户
        await ctx.c.var.auth.api.signOut({ headers: ctx.c.req.raw.headers });

        return { isLastConnection: true };
      }

      // 当前用户有两个或两个以上连接

      //  根据连接类型进行不同的删除处理
      if (targetConnection.providerId === 'google' || targetConnection.providerId === 'microsoft') {
        // 📧 Gmail/Outlook: 删除连接和对应的 account
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 删除 ${targetConnection.providerId} 连接和 account`);

        // 删除连接
        await db
          .delete(connection)
          .where(and(eq(connection.id, connectionId), eq(connection.userId, user.id)));

        // 删除对应的 account
        // 可能删除多个
        // await db
        //   .delete(account)
        //   .where(and(
        //     eq(account.userId, user.id),
        //     eq(account.providerId, targetConnection.providerId)
        //   ));
        if (targetConnection.accessToken) {
          await db
          .delete(account)
          .where(and(
            eq(account.userId, user.id),
            eq(account.providerId, targetConnection.providerId),
            eq(account.accessToken, targetConnection.accessToken)
          ));
        } else if (targetConnection.refreshToken) {
          await db
          .delete(account)
          .where(and(
            eq(account.userId, user.id),
            eq(account.providerId, targetConnection.providerId),
            eq(account.refreshToken, targetConnection.refreshToken)
          ));
        }
        

      } else if (targetConnection.providerId === 'credential') {
        // IMAP/SMTP: 检查是否是最后一个 credential 连接
        const credentialConnections = userConnections.filter(conn => conn.providerId === 'credential');

        console.log(`[${new Date().toLocaleString('zh-CN')}] 🔍 用户有 ${credentialConnections.length} 个 credential 连接`);

        if (credentialConnections.length <= 1) {
          // 这是最后一个 credential 连接，删除连接和 credential account
          console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 删除最后一个 credential 连接和 account`);

          // 删除连接
          await db
            .delete(connection)
            .where(and(eq(connection.id, connectionId), eq(connection.userId, user.id)));

          // 删除 credential account
          await db
            .delete(account)
            .where(and(
              eq(account.userId, user.id),
              eq(account.providerId, 'credential')
            ));
        } else {
          // 还有其他 credential 连接，只删除当前连接
          // 如果account中account_id为删除的email,则修改
          console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 删除 credential 连接（保留 account）`);

          // 找到当前用户id的credentialAccount
          const [targetAccount] = await db.select()
          .from(account)
          .where(and(
              eq(account.userId, user.id),
              eq(account.providerId, 'credential')
            ))
          .limit(1);

          if (targetAccount && targetConnection.email === targetAccount.accountId) {
            // 如果credentialAccount的邮箱是要删除的邮箱，则替换成其他的credential邮箱
            const otherConnection = credentialConnections.find((c)=>c.email !== targetConnection.email);
            if (otherConnection) {
              await db.update(account)
              .set({accountId: otherConnection.email})
              .where(eq(account.id, targetAccount.id));

              // 如果user表中的邮箱是要删除的邮箱，则同样替换成其他的credential邮箱
              const [targetUser] = await db.select()
              .from(user_)
              .where(eq(user_.id, user.id))
              .limit(1);

              if (targetUser && targetUser.email === targetConnection.email) {
                await db.update(user_)
                .set({email: otherConnection.email})
                .where(eq(user_.id, user.id));
              }
            }
          }

          await db
            .delete(connection)
            .where(and(eq(connection.id, connectionId), eq(connection.userId, user.id)));
          
        }
      }

      // 🔄 如果删除的是当前活跃连接，清除默认连接
      const activeConnection = await getActiveConnection();
      if (connectionId === activeConnection.id) {
        await db.update(user_).set({ defaultConnectionId: null });
      }

      return { isLastConnection: false };
    }),
  getDefault: privateProcedure.query(async () => {
    const connection = await getActiveConnection();
    return connection;
  }),

  addImapSmtpConnection: privateProcedure
    .input(imapSmtpConnectionSchema)
    .mutation(async ({ input, ctx }) => {
      // console.log("debug addImapSmtpConnection called");
      const { db } = ctx;
      const user = ctx.session.user;

      const { provider, auth: connectionAuth, isAddConnectionMode } = input;
      const { email, refreshToken } = connectionAuth;
      const hashedPassword = encrypt(refreshToken);

      // 根据邮箱域名自动配置IMAP/SMTP设置
      const domain = email.split('@')[1] || '';
      const autoConfig = getAutoConfigForDomain(domain);

      // 使用提供的配置或自动配置
      const finalConfig = {
        email,
        host: connectionAuth.host || autoConfig.imapHost,
        port: connectionAuth.port || autoConfig.imapPort,
        secure: connectionAuth.secure !== undefined ? connectionAuth.secure : autoConfig.imapSecure,
        smtpHost: connectionAuth.smtpHost || autoConfig.smtpHost,
        smtpPort: connectionAuth.smtpPort || autoConfig.smtpPort,
        smtpSecure:
          connectionAuth.smtpSecure !== undefined
            ? connectionAuth.smtpSecure
            : autoConfig.smtpSecure,
      };

      console.log(`自动配置邮箱 ${email} (${domain}):`, finalConfig);

      // 暂时跳过连接验证，直接创建连接
      // TODO: 在实际使用时进行连接验证
      console.log(`跳过IMAP连接验证，直接创建连接: ${email}`);
      console.log(`配置信息: ${JSON.stringify(finalConfig, null, 2)}`);

      const connectionId = uuidv4();
      const imapSmtpConfig = {
        provider,
        auth: finalConfig,
      };

      // 仅通过邮箱查找有没有connection
      // const existEmailConnection = await db.query.connection.findFirst({
      //   where: eq(connection.email, email)
      // });

      // if (existEmailConnection) {
      //   const existAccount = await db.query.account.findFirst({
      //     where: eq(account.userId, existEmailConnection.userId)
      //   });
      //   if (existAccount) {
      //     // 删除existAccount
      //     await db.delete(account).where(eq(account.id, existAccount.id));
      //   }

      //   const existUser = await db.query.user.findFirst({
      //     where: eq(user_.id, existEmailConnection.userId)
      //   });
      //   if (existUser) {
      //     // 删除User
      //     await db.delete(user_).where(eq(user_.id, existUser.id));
      //   }

      //   // 删除existEmailConnection
      //   await db.delete(connection).where(eq(connection.id, existEmailConnection.id));
      // }

      console.log(`为用户 ${user.id} 创建邮箱 ${email} 的连接`);

      // 🎯 检查账户合并逻辑（添加连接时）
      console.log(`🔄 检查添加连接时的账户合并: ${email}`);

      // 首先检查当前用户是否已经有 credential account（说明是 IMAP 用户）
      const currentUserCredentialAccount = await db
        .select()
        .from(account)
        .where(and(
          eq(account.userId, user.id),
          eq(account.providerId, 'credential')
        ))
        .limit(1);

      if (currentUserCredentialAccount.length > 0) {
        // 当前用户已经是 IMAP 用户，直接添加连接即可
        console.log(`ℹ️ 当前用户已是 IMAP 用户，直接添加连接: ${email}`);
      } else {
        // 当前用户不是 IMAP 用户，检查要添加的邮箱是否已存在于其他用户
        console.log(`🔍 当前用户不是 IMAP 用户，检查邮箱 ${email} 是否已存在`);

        const existingCredentialAccount = await db
          .select()
          .from(account)
          .where(and(
            eq(account.providerId, 'credential'),
            eq(account.accountId, email)
          ))
          .limit(1);

        if (existingCredentialAccount.length > 0) {
          const targetUserId = existingCredentialAccount[0]?.userId;
          if (targetUserId && targetUserId !== user.id) {
            // 当前用户不是IMAP，添加的用户在account表中存在 fix:把下面的合并方向反过来 targetUserId -> user.id
            console.log(`🎯 找到已存在的 IMAP 用户，合并账户: ${targetUserId} → ${user.id}`);

            // 更新account，修改userId
            await db
              .update(account)
              .set({ userId: user.id })
              .where(eq(account.userId, targetUserId));

            // 更新connection,修改userId
            await db
              .update(connection)
              .set({ userId: user.id })
              .where(eq(connection.userId, targetUserId));

            // 删除之前已存在的账号，合并后不需要了
            await db
              .delete(user_)
              .where(eq(user_.id, targetUserId));

            // console.log(`🎯 找到已存在的 IMAP 用户，合并账户: ${user.id} → ${targetUserId}`);

            // // 合并当前用户的所有数据到目标用户
            // await db
            //   .update(account)
            //   .set({ userId: targetUserId })
            //   .where(eq(account.userId, user.id));

            // await db
            //   .update(connection)
            //   .set({ userId: targetUserId })
            //   .where(eq(connection.userId, user.id));

            // // 删除当前用户记录
            // await db
            //   .delete(user_)
            //   .where(eq(user_.id, user.id));

            // console.log(`✅ 账户合并完成: ${user.id} → ${targetUserId}`);

            // 更新当前用户ID为目标用户ID，以便后续操作（不需要）
            // 当前user.id继续可以
            // user.id = targetUserId;
          }
        } else {
          console.log(`ℹ️ 邮箱 ${email} 不存在，将创建新的 IMAP 连接`);
          // mt 添加一个account
          const authCtx = await ctx.c.var.auth.$context;
          const passwordHash = await authCtx.password.hash('smtp-imap-user');
          const newAccountId = uuidv4();
          await db.insert(account).values({
            id: newAccountId,
            userId: user.id,
            providerId: 'credential',
            accountId: email,
            password: passwordHash,
            createdAt: new Date(),
            updatedAt: new Date(),
          })
        }

        // 当前用户不是 IMAP 用户
        // 更新user表中的email，account中一定有当前邮件的记录
        const [currentCredentialAccount] = await db
          .select()
          .from(account)
          .where(and(
            eq(account.providerId, 'credential'),
            eq(account.accountId, email)
          ))
          .limit(1);
        if (!currentCredentialAccount) {
          console.error("connections.addImapSmtpConnection错误：没有找到account,请添加记录");
        }
        const currentUserId = currentCredentialAccount!.userId;
        await db.update(user_)
        .set({email})
        .where(eq(user_.id, currentUserId));
      }

      // 🎯 现在 Better Auth 会自动处理账户链接，不需要手动创建 account 记录

      // Check if a connection already exists for this user and email
      const existingConnection = await db.query.connection.findFirst({
        where: and(eq(connection.userId, user.id), eq(connection.email, email)),
      });

      if (existingConnection) {
        // Update existing connection to IMAP/SMTP type
        console.log(
          `Updating existing connection (${existingConnection.providerId}) to credential for ${email}`,
        );

        // 🔧 如果原有的 name 为空，使用邮箱 @ 前面的部分作为 name
        const fallbackName = email.split('@')[0] || 'Unknown';
        const finalName = existingConnection.name || fallbackName;

        await db
          .update(connection)
          .set({
            providerId: 'credential',
            accessToken: '',
            refreshToken: hashedPassword,
            scope: JSON.stringify(imapSmtpConfig),
            expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(),
            // 🔧 保留原有的 name 和 picture，如果 name 为空则使用邮箱前缀
            name: finalName,
            picture: existingConnection.picture,
          })
          .where(eq(connection.id, existingConnection.id));

        console.log(`Successfully updated connection ${existingConnection.id} to credential`);

        return {
          success: true,
          connectionId: existingConnection.id,
        };
      } else {
        // Create new connection
        // 🔧 使用邮箱 @ 前面的部分作为 name
        const fallbackName = email.split('@')[0] || 'Unknown';

        await db.insert(connection).values({
          id: connectionId,
          userId: user.id,
          email,
          name: fallbackName,
          providerId: 'credential',
          accessToken: '',
          refreshToken: hashedPassword,
          scope: JSON.stringify(imapSmtpConfig),
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
          createdAt: new Date(),
          updatedAt: new Date(),
        } as typeof connection.$inferInsert);
      }

      // 对于IMAP/SMTP连接，总是设为默认连接
      // 因为用户明确选择了这种登录方式
      console.log(`Setting IMAP/SMTP connection ${connectionId} as default for user ${user.id}`);
      await db
        .update(user_)
        .set({ defaultConnectionId: connectionId })
        .where(eq(user_.id, user.id));

      return {
        success: true,
        connectionId,
      };
    }),

  // 🎯 更新账户令牌信息（用于 SMTP-IMAP 登录后设置令牌）
  updateAccountTokens: publicProcedure
    .input(
      z.object({
        email: z.string(),
        password: z.string(),
        serverInfo: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { email, password, serverInfo } = input;
      const hashedPassword = encrypt(password);

      console.log(`🔄 更新账户令牌: ${email}`);

      try {
        // 查找用户
        const [user] = await ctx.db.select().from(user_).where(eq(user_.email, email)).limit(1);

        if (!user) {
          console.error(`❌ 用户不存在: ${email}`);
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '用户不存在',
          });
        }

        // 查找该用户的 credential 账户
        const [credentialAccount] = await ctx.db
          .select()
          .from(account)
          .where(and(eq(account.userId, user.id), eq(account.providerId, 'credential')))
          .limit(1);

        if (!credentialAccount) {
          console.error(`❌ 找不到 credential 账户: ${email}`);
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '找不到 credential 账户',
          });
        }

        // 更新账户的令牌字段
        await ctx.db
          .update(account)
          .set({
            accessToken: hashedPassword, // 设为哈希处理的登录密码
            refreshToken: hashedPassword, // 设为哈希处理的登录密码
            idToken: hashedPassword, // 设为哈希处理的登录密码
            scope: serverInfo, // 设为服务器信息
            updatedAt: new Date(),
          })
          .where(eq(account.id, credentialAccount.id));

        console.log(`✅ 账户令牌更新成功: ${email}`);

        return {
          success: true,
          message: '账户令牌更新成功',
        };
      } catch (error: any) {
        console.error(`❌ 更新账户令牌失败: ${email}`, error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '更新账户令牌失败: ' + error.message,
        });
      }
    }),
});
