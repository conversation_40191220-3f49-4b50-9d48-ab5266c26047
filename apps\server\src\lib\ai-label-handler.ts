import { aiLabelEmail, connection, emailAiLabelJob } from '@zero/db/schema';
import { eq, and, count, desc, sql } from 'drizzle-orm';
import { getContext } from 'hono/context-storage';
import type { ParsedMessage } from '../types';
import type { HonoContext } from '../ctx';
import { redis } from './services';

function sleep(ms: number | undefined) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

const cache = redis();

const urlAiCompletions = 'https://in.aihubs.cn:18881/v1/chat/completions';
const aiRequestToken = 'sk-xqDLtFyKdFrQ8mBb7bB091F12e364e5a8f3bC3952f215222';
const aiModel = 'gpt-4o';

export async function handleEmailByAiLabel(
  userEmail: string,
  folder: string,
  emailUid: string,
  threadMessages: ParsedMessage[],
) {
  await sleep(10);
  console.log('debug ai-label-handler message number: ', threadMessages.length);
  console.log(
    `debug ai-label-handler detail: userEmail:${userEmail} folder:${folder} emailUid:${emailUid}`,
  );
  const c = getContext<HonoContext>();
  const [queryJobState] = await c.var.db
    .select()
    .from(emailAiLabelJob)
    .where(eq(emailAiLabelJob.guid, `${userEmail}:${folder}:${emailUid}`));
  if (queryJobState && (queryJobState.state === 'pending' || queryJobState.state === 'finished')) {
    return;
  }

  const [emailAiLabelJobRow] = await c.var.db
    .insert(emailAiLabelJob)
    .values({
      guid: `${userEmail}:${folder}:${emailUid}`,
      uid: emailUid,
      email: userEmail,
      folder,
      state: 'pending',
    })
    .returning();
  if (!emailAiLabelJobRow) {
    return;
  }

  const [connectionResult] = await c.var.db
    .select()
    .from(connection)
    .where(eq(connection.email, userEmail));
  if (!connectionResult) {
    await c.var.db
      .update(emailAiLabelJob)
      .set({
        state: 'finished',
      })
      .where(eq(emailAiLabelJob.id, emailAiLabelJobRow.id));
    return;
  }
  const connectionId = connectionResult.id;
  const state = await cache.get<string>(`subscribed_accounts:${connectionId}`);
  if (!state) return;
  const labels: { name: string; usecase: string }[] = await cache.get<any>(
    `connection_labels:${connectionId}`,
  );
  // console.log('debug ai-label-handler labels:', labels);
  if (Object.prototype.toString.call(labels) === '[object Array]') {
    let mailContentStr = '';
    for (const threadMessage of threadMessages) {
      //console.log('debug ai-label-handler threadMessage:', threadMessage);
      mailContentStr = mailContentStr + threadMessage.body
    }

    for (const label of labels) {
      // TODO 根据label使用AI测试邮件
      const currentUsecase = label.usecase;
      const currentLabelName = label.name;

      // 检查邮件是否已经打标签
      // const [existAiLabelEmail] = await c.var.db
      //   .select()
      //   .from(aiLabelEmail)
      //   .where(eq(aiLabelEmail.guid, `${userEmail}:${folder}:${emailUid}`));
      // if (existAiLabelEmail) {
      // }

      let passAiCheck = false;

      const chatContent = "Answers with only 'yes' or 'no'. Here's an email:\n" + '---\n'+
      mailContentStr + '---\n'+
      'Check whether this email complies with the following rules:\n' + currentUsecase;

      // console.log('debug ai-label-handler chatContent:' + chatContent);

      // try {
      //   const aiResponse = await fetch(urlAiCompletions, {
      //     method: 'POST',
      //     headers: {
      //       Authorization: `Bearer ${aiRequestToken}`,
      //       'Content-Type': 'application/json',
      //     },
      //     body: JSON.stringify({
      //       model: aiModel,
      //       messages: [
      //         {
      //           role: 'user',
      //           content: chatContent,
      //         },
      //       ],
      //     }),
      //   });
      //   if (!aiResponse.ok) {
      //     // console.error(`AI label无法调用AI:${urlAiCompletions}`);
      //     throw new Error('failed to fetch ' + aiResponse.status);
      //   }

      //   const aiResult: any = await aiResponse.json();
      //   const modelResultMd: string = aiResult.choices[0].message.content;

      //   passAiCheck = !modelResultMd.includes('no');
      // } catch (error) {
      //   console.error(`AI label无法调用AI:${urlAiCompletions} 错误：${error}`);
      // }

      if (passAiCheck) {
        // 把邮件id保存到对应的AI label
        let threadMessagesReceivedOn;
        let maxReceivedOnTimestap = 0;
        for (const threadMessage of threadMessages) {
          if (new Date(threadMessage.receivedOn).getTime() > maxReceivedOnTimestap) {
            maxReceivedOnTimestap = new Date(threadMessage.receivedOn).getTime();
            threadMessagesReceivedOn = new Date(threadMessage.receivedOn);
          }
        }
        // console.log('debug ai-label-handler maxReceivedOnTimestap:', maxReceivedOnTimestap);
        await c.var.db.insert(aiLabelEmail).values({
          guid: `${userEmail}:${folder}:${emailUid}`,
          uid: emailUid,
          email: userEmail,
          folder: folder,
          labelName: currentLabelName,
          labelUsecase: currentUsecase,
          receivedOn: threadMessagesReceivedOn ? new Date(threadMessagesReceivedOn) : new Date(),
        });

        // 第一个匹配的label，处理完直接结束后续label的检查
        await c.var.db
          .update(emailAiLabelJob)
          .set({
            state: 'finished',
          })
          .where(eq(emailAiLabelJob.id, emailAiLabelJobRow.id));
        return;
      }
    }

    // 没有任何label符合
    await c.var.db
      .update(emailAiLabelJob)
      .set({
        state: 'finished',
      })
      .where(eq(emailAiLabelJob.id, emailAiLabelJobRow.id));
  }
}
