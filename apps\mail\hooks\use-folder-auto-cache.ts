/**
 * 邮箱文件夹自动缓存 Hook
 * 
 * 功能：
 * - 管理自动缓存状态
 * - 提供缓存控制方法
 * - 集成 TRPC 查询
 * - 智能用户活跃度检测
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { FolderCacheManager, type CacheProgress, type CacheStatus, type CacheOptions } from '@/lib/folder-cache-manager';
import { useTRPCClient } from '@/providers/query-provider';
import { toast } from 'sonner';

interface UseFolderAutoCacheOptions extends Partial<CacheOptions> {
  enabled?: boolean; // 是否启用自动缓存
  pauseOnUserActivity?: boolean; // 用户活跃时是否暂停
  activityTimeout?: number; // 用户活跃超时时间（毫秒）
}

interface UseFolderAutoCacheReturn {
  // 状态
  status: CacheStatus;
  progress: CacheProgress;
  isEnabled: boolean;
  
  // 控制方法
  start: () => void;
  pause: () => void;
  resume: () => void;
  stop: () => void;
  toggle: () => void;
  
  // 配置方法
  setEnabled: (enabled: boolean) => void;
  updateOptions: (options: Partial<UseFolderAutoCacheOptions>) => void;
}

export const useFolderAutoCache = (
  options: UseFolderAutoCacheOptions = {}
): UseFolderAutoCacheReturn => {
  const {
    enabled = true,
    pauseOnUserActivity = true,
    activityTimeout = 5 * 60 * 1000, // 5分钟
    ...cacheOptions
  } = options;

  // 状态管理
  const [status, setStatus] = useState<CacheStatus>('idle');
  const [progress, setProgress] = useState<CacheProgress>({
    current: 0,
    total: 0,
    currentFolder: null,
    completedFolders: [],
    failedFolders: [],
  });
  const [isEnabled, setIsEnabled] = useState(enabled);

  // 引用管理
  const managerRef = useRef<FolderCacheManager | null>(null);
  const activityTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  // TRPC 客户端
  const trpcClient = useTRPCClient();

  // 模拟文件夹数据，实际应该从 API 获取
  const folders = ['inbox', 'sent', 'drafts', 'archive', 'spam', 'bin'];

  /**
   * 缓存文件夹函数
   */
  const cacheFolder = useCallback(async (folder: string): Promise<void> => {
    console.log(`[${new Date().toLocaleString('zh-CN')}] 📥 开始预缓存文件夹: ${folder}`);

    try {
      // 使用 TRPC 客户端来预取文件夹数据
      await trpcClient.mail.listThreads.query({
        folder,
        max: 50,  // 每次缓存50封邮件
        q: '',    // 空查询
        cursor: '' // 从头开始
      });

      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 文件夹预缓存成功: ${folder}`);

    } catch (error) {
      console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 文件夹预缓存失败: ${folder}`, error);
      throw error;
    }
  }, [trpcClient]);

  /**
   * 初始化缓存管理器
   */
  const initializeManager = useCallback(() => {
    if (!folders || folders.length === 0) return;

    console.log(`[${new Date().toLocaleString('zh-CN')}] 🏗️ 初始化文件夹缓存管理器`);

    // 销毁旧的管理器
    if (managerRef.current) {
      managerRef.current.destroy();
    }

    // 创建新的管理器
    managerRef.current = new FolderCacheManager(
      cacheOptions,
      // 进度回调
      (newProgress) => {
        setProgress(newProgress);
        console.log(`[${new Date().toLocaleString('zh-CN')}] 📊 缓存进度: ${newProgress.current}/${newProgress.total}`);
      },
      // 状态回调
      (newStatus) => {
        setStatus(newStatus);
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 缓存状态: ${newStatus}`);

        // 状态变化时的通知
        switch (newStatus) {
          case 'completed':
            toast.success('所有文件夹缓存完成！');
            break;
          case 'error':
            toast.error('文件夹缓存出现错误');
            break;
        }
      },
      // 错误回调
      (error, folder) => {
        console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 文件夹缓存错误: ${folder}`, error);
        toast.error(`文件夹 ${folder} 缓存失败`);
      },
      // 缓存函数
      cacheFolder
    );

    // 初始化队列（排除 inbox，因为首页已经加载了）
    const foldersToCache = folders.filter((f: string) => f !== 'inbox');
    managerRef.current.initializeQueue(foldersToCache);

  }, [folders]);

  /**
   * 用户活跃度检测
   */
  const handleUserActivity = useCallback(() => {
    lastActivityRef.current = Date.now();

    if (pauseOnUserActivity && status === 'running') {
      console.log(`[${new Date().toLocaleString('zh-CN')}] 👤 检测到用户活跃，暂停缓存`);
      managerRef.current?.pause();
    }

    // 清除之前的定时器
    if (activityTimerRef.current) {
      clearTimeout(activityTimerRef.current);
    }

    // 设置新的定时器，在用户不活跃后恢复缓存
    if (pauseOnUserActivity) {
      activityTimerRef.current = setTimeout(() => {
        if (status === 'paused') {
          console.log(`[${new Date().toLocaleString('zh-CN')}] 😴 用户不活跃，恢复缓存`);
          managerRef.current?.resume();
        }
      }, activityTimeout);
    }
  }, [pauseOnUserActivity, status, activityTimeout]);

  /**
   * 控制方法
   */
  const start = useCallback(() => {
    if (!managerRef.current) return;
    managerRef.current.start();
  }, []);

  const pause = useCallback(() => {
    if (!managerRef.current) return;
    managerRef.current.pause();
  }, []);

  const resume = useCallback(() => {
    if (!managerRef.current) return;
    managerRef.current.resume();
  }, []);

  const stop = useCallback(() => {
    if (!managerRef.current) return;
    managerRef.current.stop();
  }, []);

  const toggle = useCallback(() => {
    if (!managerRef.current) return;
    
    switch (status) {
      case 'idle':
      case 'paused':
        start();
        break;
      case 'running':
        pause();
        break;
    }
  }, [status, start, pause]);

  const updateOptions = useCallback((newOptions: Partial<UseFolderAutoCacheOptions>) => {
    // 简单的选项更新，不重新初始化管理器
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🔧 更新缓存选项:`, newOptions);
  }, []);

  /**
   * 初始化效果
   */
  useEffect(() => {
    if (isEnabled && folders) {
      initializeManager();
    }

    return () => {
      if (managerRef.current) {
        managerRef.current.destroy();
      }
    };
  }, [isEnabled, initializeManager]);

  /**
   * 用户活跃度监听
   */
  useEffect(() => {
    if (!pauseOnUserActivity) return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
      
      if (activityTimerRef.current) {
        clearTimeout(activityTimerRef.current);
      }
    };
  }, [handleUserActivity, pauseOnUserActivity]);

  /**
   * 自动启动缓存（在用户登录后2分钟）
   */
  useEffect(() => {
    if (!isEnabled || !managerRef.current || status !== 'idle') return;

    const autoStartTimer = setTimeout(() => {
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🚀 自动启动文件夹缓存`);
      start();
    }, 2 * 60 * 1000); // 2分钟后自动启动

    return () => clearTimeout(autoStartTimer);
  }, [isEnabled, status, start]);

  return {
    // 状态
    status,
    progress,
    isEnabled,
    
    // 控制方法
    start,
    pause,
    resume,
    stop,
    toggle,
    
    // 配置方法
    setEnabled: setIsEnabled,
    updateOptions,
  };
};
