#!/bin/bash

# 创建 xmind 文件结构
cat <<EOF > mail-project.xmind
{
  "title": "Mail Project",
  "children": [
    {
      "title": "apps/",
      "children": [
        {
          "title": "mail/",
          "children": [
            {
              "title": ".gitignore",
              "note": "Git 忽略配置"
            },
            {
              "title": "components.json",
              "note": "组件配置"
            },
            {
              "title": "eslint.config.mjs",
              "note": "ESLint 配置"
            },
            {
              "title": "instrument.ts",
              "note": "性能监控工具"
            },
            {
              "title": "middleware.ts",
              "note": "中间件逻辑"
            },
            {
              "title": "package.json",
              "note": "项目依赖管理"
            },
            {
              "title": "react-router.config.ts",
              "note": "路由配置"
            },
            {
              "title": "tailwind.config.ts",
              "note": "Tailwind CSS 配置"
            },
            {
              "title": "tsconfig.json",
              "note": "TypeScript 配置"
            },
            {
              "title": "vite.config.ts",
              "note": "Vite 构建配置"
            },
            {
              "title": "worker.ts",
              "note": "Cloudflare Workers 脚本"
            },
            {
              "title": "wrangler.jsonc",
              "note": "Cloudflare Wrangler 配置"
            },
            {
              "title": "app/",
              "children": [
                {
                  "title": "entry.client.tsx",
                  "note": "客户端入口文件"
                },
                {
                  "title": "entry.server.tsx",
                  "note": "服务器端入口文件"
                },
                {
                  "title": "globals.css",
                  "note": "全局样式表"
                },
                {
                  "title": "mailto-handler.ts",
                  "note": "邮件链接处理程序"
                },
                {
                  "title": "page.tsx",
                  "note": "根页面组件"
                },
                {
                  "title": "root.tsx",
                  "note": "React Router 根布局"
                },
                {
                  "title": "routes.ts",
                  "note": "路由定义"
                },
                {
                  "title": "(auth)/",
                  "note": "认证相关路由",
                  "children": [
                    {
                      "title": "login/",
                      "children": [
                        {
                          "title": "page.tsx",
                          "note": "登录页面组件"
                        },
                        {
                          "title": "login-client.tsx",
                          "note": "登录客户端逻辑"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
EOF

echo "XMind 文件结构已生成：mail-project.xmind"
