{"name": "@zero/db", "version": "0.0.0", "private": true, "dependencies": {"drizzle-orm": "0.43.1", "postgres": "3.4.5", "zod": "3.24.4"}, "scripts": {"db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@types/node": "22.13.8", "@zero/tsconfig": "workspace:*", "dotenv": "16.4.7", "drizzle-kit": "0.31.4", "typescript": "5.8.2"}, "exports": {".": "./src/index.ts", "./schema": "./src/schema.ts", "./user_settings_default": "./src/user_settings_default.ts"}}