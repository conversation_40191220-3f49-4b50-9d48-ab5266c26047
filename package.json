{"name": "zero", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.12.4", "workspaces": {"packages": ["packages/*"], "catalog": {"typescript": "^5.8.3"}}, "scripts": {"prepare": "husky", "nizzy": "tsx ./packages/cli/src/cli.ts", "postinstall": "pnpm nizzy sync", "dev": "dotenv -- turbo run dev", "bun:dev": "dotenv -- turbo run bun:dev", "bun:print-env": "bun --env-file=.env --print process.env", "build": "dotenv -- turbo run build", "build:frontend": "pnpm run --filter=@zero/mail build", "deploy:frontend": "pnpm run --filter=@zero/mail deploy", "deploy:backend": "pnpm run --filter=@zero/server deploy", "start": "dotenv -- turbo run start", "lint": "dotenv -- turbo run lint", "format": "prettier --write apps/**/*.{ts,tsx} --log-level silent", "check": "pnpm run check:format && pnpm run lint", "check:format": "prettier . --check", "lint-staged": "prettier --write --ignore-unknown", "docker:db:up": "docker-compose -f docker-compose.db.yaml up -d", "docker:db:down": "docker-compose -f docker-compose.db.yaml down", "docker:db:clean": "docker-compose -f docker-compose.db.yaml down -v", "db:generate": "dotenv -- turbo run db:generate", "db:migrate": "dotenv -- turbo run db:migrate", "db:push": "dotenv -- turbo run db:push", "db:studio": "dotenv -- turbo run db:studio", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org zero-7y --project nextjs ./apps/mail/.next && sentry-cli sourcemaps upload --org zero-7y --project nextjs ./apps/mail/.next", "scripts": "dotenv -- pnpx tsx ./scripts/run.ts"}, "devDependencies": {"@types/node": "22.13.8", "@zero/tsconfig": "workspace:*", "dotenv-cli": "^8.0.0", "husky": "9.1.7", "prettier": "3.5.3", "prettier-plugin-sort-imports": "1.8.6", "prettier-plugin-tailwindcss": "0.6.11", "tsx": "4.19.4", "turbo": "^2.5.5", "typescript": "catalog:"}, "dependencies": {"@types/mailparser": "3.4.6", "@types/nodemailer": "6.4.17", "imapflow": "1.0.189", "mailparser": "3.7.4", "nodemailer": "7.0.4", "uuid": "11.1.0"}}