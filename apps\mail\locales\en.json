{"common": {"actions": {"logout": "Logout", "back": "Back", "create": "Create Email", "saveChanges": "Save changes", "saving": "Saving...", "resetToDefaults": "Reset to Defaults", "close": "Close", "signingOut": "Signing out...", "signedOutSuccess": "Signed out successfully!", "signOutError": "Error signing out", "refresh": "Refresh", "loading": "Loading...", "featureNotImplemented": "This feature is not implemented yet", "moving": "Moving...", "moved": "Moved", "movedToInbox": "Moved to inbox", "movingToInbox": "Moving to inbox...", "movedToSpam": "Moved to spam", "movingToSpam": "Moving to spam...", "deletingMail": "Deleting mail...", "failedToDeleteMail": "Failed to delete mail", "deletedMail": "Mail deleted", "archiving": "Archiving...", "archived": "Archived", "failedToMove": "Failed to move message", "addingToFavorites": "Adding to favorites...", "removingFromFavorites": "Removing from favorites...", "addedToFavorites": "Added to favorites", "addedToImportant": "Added to important", "removedFromFavorites": "Removed from favorites", "removedFromImportant": "Removed from important", "failedToAddToFavorites": "Failed to add to favorites", "failedToRemoveFromFavorites": "Failed to remove from favorites", "failedToModifyFavorites": "Failed to modify favorites", "movingToBin": "Moving to bin...", "movedToBin": "Moved to bin", "Bin": "Bin", "failedToMoveToBin": "Failed to move to bin", "markingAsRead": "Marking as read...", "markingAsUnread": "Marking as unread...", "hiddenImagesWarning": "Images are hidden by default for security reasons.", "showImages": "Show Images", "disableImages": "Hide Images", "trustSender": "Trust Sender", "cancel": "Cancel", "save": "Save", "remove": "Remove", "settings": "Settings", "exitSelectionModeEsc": "Click or press ESC to exit selection mode"}, "themes": {"dark": "Dark", "light": "Light", "system": "System", "amber-minimal-light": "Amber Minimal Light", "amethyst-haze-light": "Amethyst Haze Light", "bold-tech-light": "Bold Tech Light", "bubblegum-light": "Bubblegum Light", "caffeine-light": "Caffeine Light", "candyland-light": "Candyland Light", "catppuccin-light": "Catppuccin Light", "claude-light": "<PERSON>", "claymorphism-light": "Claymorphism Light", "clean-slate-light": "Clean Slate Light", "cosmic-night-light": "Cosmic Night Light", "cyberpunk-light": "Cyberpunk Light", "doom-64-light": "Doom 64 Light", "elegant-luxury-light": "Elegant Luxury Light", "graphite-light": "Graphite Light", "kodama-grove-light": "Kodama Grove Light", "midnight-bloom-light": "Midnight Bloom Light", "mocha-mousse-light": "<PERSON><PERSON>", "modern-minimal-light": "Modern Minimal Light", "mono-light": "Mono Light", "nature-light": "Nature Light", "neo-brutalism-light": "Neo Brutalism Light", "northern-lights-light": "Northern Lights Light", "notebook-light": "Notebook Light", "ocean-breeze-light": "Ocean Breeze Light", "pastel-dreams-light": "Pastel Dreams Light", "perpetuity-light": "Perpetuity Light", "quantum-rose-light": "Quantum Rose Light", "retro-arcade-light": "Retro Arcade Light", "solar-dusk-light": "Solar Dusk Light", "starry-night-light": "Starry Night Light", "sunset-horizon-light": "Sunset Horizon Light", "supabase-light": "Supabase Light", "t3-chat-light": "T3 Chat Light", "tangerine-light": "Tangerine Light", "twitter-light": "Twitter Light", "vercel-light": "Vercel Light", "vintage-paper-light": "Vintage Paper Light"}, "commandPalette": {"title": "Command Palette", "description": "Quick navigation and actions for Mail-0", "placeholder": "Type a command or search...", "noResults": "No results found", "groups": {"mail": "Mail", "settings": "Settings", "actions": "Actions", "help": "Help", "navigation": "Navigation"}, "commands": {"goToInbox": "Go to Inbox", "goToDrafts": "Go to Drafts", "goToSent": "Go to Sent", "goToSpam": "Go to Spam", "goToArchive": "Go to Archive", "goToBin": "Go to Bin", "goToSettings": "Go to Settings", "newEmail": "New Email", "composeMessage": "New Email", "searchEmails": "Search Emails", "toggleTheme": "Toggle Theme", "backToMail": "Back to Mail", "goToDocs": "Go to docs", "helpWithShortcuts": "Help with shortcuts", "commandPalette": "Command Palette"}}, "searchBar": {"pickDateRange": "Pick a date or a range", "search": "Search", "clearSearch": "Clear search", "advancedSearch": "Advanced search", "quickFilters": "Quick filters", "searchIn": "Search in", "recipient": "Recipient", "sender": "Sender", "subject": "Subject", "from": "From", "dateRange": "Date range", "category": "Category", "folder": "Folder", "allMail": "All Mail", "unread": "Unread", "hasAttachment": "Has Attachment", "starred": "Starred", "applyFilters": "Apply filters", "reset": "Reset", "searching": "Searching...", "aiSuggestions": "AI Suggestions", "aiSearching": "AI is searching...", "aiSearchError": "AI search failed. Please try again.", "aiNoResults": "No AI suggestions found", "aiEnhancedQuery": "Enhanced search query"}, "navUser": {"customerSupport": "Community", "documentation": "Documentation", "appTheme": "App Theme", "accounts": "Accounts", "signIn": "Sign in", "otherAccounts": "Other Accounts"}, "mailCategories": {"primary": "Primary", "allMail": "All Mail", "important": "Important", "personal": "Personal", "updates": "Updates", "promotions": "Promotions", "social": "Social", "unread": "Unread"}, "replyCompose": {"replyTo": "Reply to", "thisEmail": "this email", "dropFiles": "Drop files to attach", "attachments": "Attachments", "attachmentCount": "{count, plural, =0 {attachments} one {attachment} other {attachments}}", "fileCount": "{count, plural, =0 {files} one {file} other {files}}", "saveDraft": "Save draft", "send": "Send", "forward": "Forward"}, "mailDisplay": {"details": "Details", "from": "From", "to": "To", "cc": "Cc", "bcc": "Bcc", "date": "Date", "mailedBy": "Mailed-By", "signedBy": "Signed-By", "security": "Security", "standardEncryption": "Standard encryption (TLS)", "loadingMailContent": "Loading mail content...", "unsubscribe": "Unsubscribe", "unsubscribed": "Unsubscribed", "unsubscribeDescription": "Are you sure you want to unsubscribe from this mailing list?", "unsubscribeOpenSiteDescription": "To stop getting messages from this mailing list, go to their website to unsubscribe.", "cancel": "Cancel", "goToWebsite": "Go to website", "failedToUnsubscribe": "Failed to unsubscribe from mailing list"}, "threadDisplay": {"exitFullscreen": "Exit fullscreen", "enterFullscreen": "Enter fullscreen", "archive": "Archive", "reply": "Reply", "moreOptions": "More options", "moveToSpam": "Move to Spam", "replyAll": "Reply all", "forward": "Forward", "markAsUnread": "<PERSON> as Unread", "markAsRead": "<PERSON> <PERSON>", "addLabel": "Add label", "muteThread": "Mute thread", "favourites": "Favourites", "disableImages": "Hide Images", "enableImages": "Show Images", "star": "Star", "unstar": "Unstar"}, "notes": {"title": "Notes", "empty": "No notes for this email", "emptyDescription": "Add notes to keep track of important information or follow-ups.", "addNote": "Add a note", "addYourNote": "Add your note here...", "editNote": "Edit note", "deleteNote": "Delete note", "deleteConfirm": "Are you sure you want to delete this note?", "deleteConfirmDescription": "This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "save": "Save note", "toSave": "to save", "label": "Label:", "search": "Search notes...", "noteCount": "{count, plural, =0 {Add notes} one {# note} other {# notes}}", "notePinned": "Note pinned", "noteUnpinned": "Note unpinned", "colorChanged": "Note color updated", "noteUpdated": "Note updated", "noteDeleted": "Note deleted", "noteCopied": "Copied to clipboard", "noteAdded": "Note added", "notesReordered": "Notes reordered", "noMatchingNotes": "No notes matching \"{query}\"", "clearSearch": "Clear search", "pinnedNotes": "Pinned notes", "otherNotes": "Other notes", "created": "Created", "updated": "Updated", "errors": {"failedToLoadNotes": "Failed to load notes", "failedToLoadThreadNotes": "Failed to load thread notes", "failedToAddNote": "Failed to add note", "failedToUpdateNote": "Failed to update note", "failedToDeleteNote": "Failed to delete note", "failedToUpdateNoteColor": "Failed to update note color", "noValidNotesToReorder": "No valid notes to reorder", "failedToReorderNotes": "Failed to reorder notes"}, "colors": {"default": "<PERSON><PERSON><PERSON>", "red": "Red", "orange": "Orange", "yellow": "Yellow", "green": "Green", "blue": "Blue", "purple": "Purple", "pink": "Pink"}, "actions": {"pin": "Pin note", "unpin": "Unpin note", "edit": "Edit note", "delete": "Delete note", "copy": "Copy note", "changeColor": "Change color"}}, "settings": {"notFound": "Settings not found", "saved": "Setting<PERSON> saved", "failedToSave": "Failed to save settings", "languageChanged": "Language changed to {locale}"}, "mail": {"replies": "{count, plural, =0 {replies} one {# reply} other {# replies}}", "deselectAll": "Deselected all emails", "selectedEmails": "Selected {count} emails", "noEmailsToSelect": "No emails to select", "markedAsRead": "Marked as read", "markedAsUnread": "Marked as unread", "failedToMarkAsRead": "Failed to mark as read", "failedToMarkAsUnread": "Failed to mark as unread", "selected": "{count} selected", "clearSelection": "Clear Selection", "moveToSpam": "Move to Spam", "moveToInbox": "Move to Inbox", "deleteFromBin": "Delete from Bin", "unarchive": "Unarchive", "archive": "Archive", "moveToBin": "Move to Bin", "restoreFromBin": "<PERSON>ore from Bin", "markAsUnread": "<PERSON> as Unread", "markAsRead": "<PERSON> <PERSON>", "addFavorite": "Favorite", "removeFavorite": "Unfavorite", "muteThread": "<PERSON><PERSON>", "moving": "Moving...", "moved": "Moved", "errorMoving": "Error moving", "reply": "Reply", "replyAll": "Reply All", "forward": "Forward", "labels": "Labels", "createNewLabel": "Create New Label", "noLabelsAvailable": "No labels available", "loadMore": "Load more", "imagesHidden": "Images are hidden for security reasons", "showImages": "Show Images", "noEmails": "No emails available", "noSearchResults": "No search results found", "clearSearch": "Clear search", "markAsImportant": "<PERSON> as Important", "removeFromImportant": "Remove from Important", "markedAsImportant": "Marked as Important", "markedAsUnimportant": "Unmarked as Unimportant", "toggleImportant": "Toggle important", "unSubscribeFromAll": "Unsubscribe from all", "starAll": "Star all", "mute": "Mute", "unmute": "Unmute", "muted": "Muted", "failedToMute": "Failed to mute", "failedToUnmute": "Failed to unmute", "archived": "Archived", "failedToArchive": "Failed to archive"}, "units": {"mb": "{amount} MB"}, "labels": {"deleteLabel": "Delete Label", "deleteLabelConfirm": "Are you sure you want to delete this label?", "deleteLabelConfirmDescription": "This action cannot be undone.", "deleteLabelConfirmCancel": "Cancel", "deleteLabelConfirmDelete": "Delete", "deleteLabelSuccess": "Label deleted successfully"}}, "navigation": {"sidebar": {"inbox": "Inbox", "drafts": "Drafts", "sent": "<PERSON><PERSON>", "spam": "Spam", "archive": "Archive", "bin": "Trash", "feedback": "<PERSON><PERSON><PERSON>", "settings": "Settings"}, "settings": {"general": "General", "connections": "Connections", "cache": "<PERSON><PERSON>", "security": "Security", "appearance": "Appearance", "signatures": "Signatures", "shortcuts": "Shortcuts", "labels": "Labels", "dangerZone": "Danger Zone", "deleteAccount": "Delete Account", "privacy": "Privacy"}}, "pages": {"error": {"notFound": {"title": "Page Not Found", "description": "Oops! The page you're looking for doesn't exist or has been moved.", "goBack": "Go Back"}, "settingsNotFound": "404 - <PERSON><PERSON><PERSON> page not found"}, "settings": {"general": {"title": "General", "description": "Manage settings for your language and email display preferences.", "language": "Language", "selectLanguage": "Select a language", "timezone": "Timezone", "selectTimezone": "Select a timezone", "dynamicContent": "Dynamic Content", "dynamicContentDescription": "Allow emails to display dynamic content.", "languageChangedTo": "Language changed to {locale}", "customPrompt": "Custom AI Prompt", "customPromptPlaceholder": "Enter your custom prompt for the AI...", "customPromptDescription": "Customize how the AI writes your email replies. This will be added to the base prompt.", "noResultsFound": "No results found"}, "connections": {"title": "Email Connections", "description": "Connect your email accounts to Zero.", "disconnectTitle": "Disconnect <PERSON><PERSON> Account", "disconnectDescription": "Are you sure you want to disconnect this email?", "cancel": "Cancel", "remove": "Remove", "disconnectSuccess": "Account disconnected successfully", "disconnectError": "Failed to disconnect account", "addEmail": "Add Connection", "connectEmail": "Connect Email", "connectEmailDescription": "Select an email provider to connect", "moreComingSoon": "More coming soon", "reconnect": "Reconnect", "disconnected": "Disconnected"}, "security": {"title": "Security", "description": "Manage your security preferences and account protection.", "twoFactorAuth": "Two-Factor Authentication", "twoFactorAuthDescription": "Add an extra layer of security to your account", "loginNotifications": "Login Notifications", "loginNotificationsDescription": "Get notified about new login attempts", "deleteAccount": "Delete Account", "loadImagesDefault": "Load Images by <PERSON><PERSON><PERSON>", "loadImagesDefaultDescription": "Images are not loaded by default for security reasons."}, "appearance": {"title": "Appearance", "description": "Customize colors, fonts and view options.", "theme": "Theme", "inboxType": "Inbox Type"}, "signatures": {"title": "Email Signatures", "description": "Create and manage your email signature that appears at the bottom of your messages.", "enableSignature": "Enable signature", "enableSignatureDescription": "Add your signature to the end of all outgoing messages.", "includeByDefault": "Include by default", "includeByDefaultDescription": "Automatically add your signature when composing new emails.", "signatureContent": "Signature Content", "signatureContentPlaceholder": "Create your signature with formatting, links, and images.", "signaturePreview": "Signature Preview", "signatureSaved": "Signature saved successfully", "signaturePreviewDescription": "This is how your signature will appear in emails.", "editorType": "Editor Type", "editorTypeDescription": "Choose between plain text/HTML input or a rich text editor for your signature.", "plainText": "Plain Text / HTML", "richText": "Rich Text Editor", "richTextDescription": "Use the rich text editor to format your signature.", "richTextPlaceholder": "Format your signature with the rich text editor...", "signatureContentHelp": "You can use HTML to add formatting, links, and images to your signature."}, "shortcuts": {"title": "Keyboard Shortcuts", "description": "View and customize keyboard shortcuts for quick actions.", "actions": {"newEmail": "New Email", "sendEmail": "Send Email", "reply": "Reply", "replyAll": "Reply All", "forward": "Forward", "drafts": "Go to drafts", "inbox": "Go to inbox", "sentMail": "Go to sent mail", "delete": "Delete", "search": "Search", "markAsUnread": "<PERSON> as unread", "muteThread": "Mute thread", "printEmail": "Print Email", "archiveEmail": "Archive Thread", "markAsSpam": "<PERSON> as <PERSON>m", "moveToFolder": "Move to Folder", "undoLastAction": "Undo Last Action", "viewEmailDetails": "View Email Details", "goToDrafts": "Go to Drafts", "expandEmailView": "Expand Email View", "helpWithShortcuts": "Help with shortcuts", "recordHotkey": "Record Hotkey", "pressKeys": "Press keys", "releaseKeys": "Release keys", "selectAll": "Select All", "commandPalette": "Command Palette", "markAsRead": "<PERSON> <PERSON>", "exitSelectionMode": "Exit Selection Mode", "closeCompose": "Close compose", "goToSpam": "Go to Spam", "goToArchive": "Go to Archive", "goToBin": "Go to Bin", "scrollDown": "Scroll down in mail list", "scrollUp": "Scroll up in mail list", "goToSettings": "Go to Settings", "showImportant": "Show important", "showAllMail": "Show all mail", "showPersonal": "Show personal", "showUpdates": "Show updates", "showPromotions": "Show promotions", "showUnread": "Show unread", "selectUnderCursor": "Select under cursor"}}, "labels": {"title": "Labels", "description": "Manage your labels for better organization of your emails."}, "dangerZone": {"title": "Danger Zone", "description": "Permanently delete your account and all associated data.", "warning": "This action cannot be undone.", "confirmation": "Please type DELETE to confirm account deletion", "deleteAccount": "Delete Account", "deleting": "Deleting...", "deleted": "Account deleted successfully", "error": "Failed to delete account"}, "privacy": {"title": "Privacy", "description": "Manage your privacy and security preferences for email content.", "externalImages": "Display External Images", "externalImagesDescription": "Allow emails to display images from external sources.", "trustedSenders": "Trusted Senders", "trustedSendersDescription": "Always display images for these senders."}}, "createEmail": {"invalidEmail": "Invalid email address", "body": "Body", "example": "<EMAIL>", "attachments": "Attachments ({count})", "addMore": "Add more", "dropFilesToAttach": "Drop files to attach", "writeYourMessageHere": "Write your message here...", "emailSent": "Email sent", "emailSentSuccessfully": "<PERSON>ail sent successfully", "failedToSendEmail": "Failed to send email. Please try again.", "signature": {"title": "Signature", "include": "Include signature", "remove": "Remove signature", "enable": "Enable signature", "disable": "Disable signature", "add": "Add signature"}, "addLink": "Add Link", "addUrlToCreateALink": "Add URL to create a link. The link will open in a new tab.", "editor": {"menuBar": {"heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "bold": "Bold", "italic": "Italic", "strikethrough": "Strikethrough", "underline": "Underline", "link": "Link", "bulletList": "Bullet List", "orderedList": "Ordered List", "viewAttachments": "View Attachments", "attachFiles": "Attach Files"}}}}, "errorMessages": {"required_scopes_missing": "We’re missing the permissions needed to craft your full experience. Please sign in again and allow the requested access.", "early_access_required": "Early access is required to log in", "unauthorized": "Zero could not load your data from the 3rd party provider. Please try again."}, "auth": {"smtpImapLogin": {"title": "SMTP/IMAP Login", "titleAddConnection": "Add SMTP/IMAP Connection", "description": "Enter your email credentials, the system will automatically configure the mail server", "descriptionAddConnection": "Add a new email connection to your account, the system will automatically configure the mail server", "backToLogin": "Back to Login", "backToConnections": "Back to Connection Settings", "emailAddress": "Email Address", "emailPlaceholder": "<EMAIL>", "password": "Password/Authorization Code", "passwordPlaceholder": "••••••••", "supportedProviders": "Auto-configure for major email providers", "qqEmailTip": "💡 QQ email: Use authorization code", "netease163EmailTip": "💡 163 email: Use authorization code", "icloudEmailTip": "💡 iCloud: Use app-specific password", "gmailEmailTip": "💡 Gmail: Use app password", "loginButton": "<PERSON><PERSON>", "addConnectionButton": "Add Connection", "loggingIn": "Logging in...", "addingConnection": "Adding connection...", "testingConnection": "Testing connection...", "connectionSuccess": "Email connection verification successful!", "connectionFailed": "Email connection verification failed: {error}", "loginSuccess": "Login successful!", "loginFailed": "<PERSON><PERSON> failed: {error}", "registrationSuccess": "Registration successful, now trying to login", "registrationFailed": "Registration failed: {error}", "connectionAdded": "Email connection added successfully!", "connectionAddFailed": "Failed to add email connection: {error}", "invalidEmailFormat": "Please enter a valid email address", "passwordRequired": "Password cannot be empty", "autoConfiguring": "Automatically configuring mail server...", "secureConnection": "🔒 Secure encrypted connection", "supportedEmailProviders": "Supported Email Providers", "providersList": "Gmail, Outlook, Yahoo, 163, QQ, iCloud and more"}}}