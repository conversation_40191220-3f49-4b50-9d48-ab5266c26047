import {
  HelpCircle,
  LogIn,
  LogOut,
  MoonIcon,
  Settings,
  Plus,
  BrainIcon,
  CopyCheckIcon,
  BadgeCheck,
  BanknoteIcon,
  Loader2,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useActiveConnection, useConnections } from '@/hooks/use-connections';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useLocation, useRevalidator, useSearchParams } from 'react-router';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { CircleCheck, Danger, ThreeDots } from '../icons/icons';
import { signOut, useSession } from '@/lib/auth-client';
import { AddConnectionDialog } from '../connection/add';
import { useTRPC } from '@/providers/query-provider';
import { useSidebar } from '@/components/ui/sidebar';
import { useBrainState } from '@/hooks/use-summary';
import { useThreads } from '@/hooks/use-threads';
// import { useBilling } from '@/hooks/use-billing';
import { PricingDialog } from './pricing-dialog';
import { SunIcon } from '../icons/animated/sun';
import { useLabels } from '@/hooks/use-labels';
import { clear as idbClear } from 'idb-keyval';
import { localAccountStorage, switchAccount } from '@/lib/local-account-storage';
import { cacheManifestManager } from '@/lib/cache-manifest-manager';
import { Gauge } from '@/components/ui/gauge';
import { useStats } from '@/hooks/use-stats';
import { useNavigate } from 'react-router';
import { useTranslations } from 'use-intl';
import { type IConnection } from '@/types';
import { useTheme } from 'next-themes';
import { Progress } from './progress';
import { useQueryState } from 'nuqs';
import { Button } from './button';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export function NavUser() {
  const { data: session, refetch: refetchSession } = useSession();
  const { data } = useConnections();
  const [isRendered, setIsRendered] = useState(false);
  const { theme, setTheme } = useTheme();

  // 🎯 账号切换加载状态
  const [switchingConnectionId, setSwitchingConnectionId] = useState<string | null>(null);
  const t = useTranslations();
  const { state } = useSidebar();
  const trpc = useTRPC();
  const navigate = useNavigate();
  const [, setThreadId] = useQueryState('threadId');
  const { mutateAsync: setDefaultConnection } = useMutation(
    trpc.connections.setDefault.mutationOptions(),
  );
  // const { openBillingPortal, customer: billingCustomer, isPro } = useBilling();
  // remove autumn
  const { data: activeConnection, refetch: refetchActiveConnection } = useActiveConnection();
  const activeAccount = useMemo(() => {
    if (!activeConnection || !data) return null;
    return data.connections?.find((connection) => connection.id === activeConnection.id);
  }, [activeConnection, data]);
  // console.log("debug nav-user activeAccount: ",activeAccount);
  // console.log("debug nav-user session: ",session);
  const { data: subscribeData } = useQuery<any>({
    queryKey: ['subscribe_status'],
    queryFn: async () => {
      // if (!(activeAccount?.email || session?.user.email)) {
      //   throw new Error('no user email');
      // }
      const response = await fetch(
        import.meta.env.VITE_PUBLIC_BACKEND_URL + '/api/pay/subscribe/statusInfo',
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          // body: JSON.stringify({
          //   appUsername: activeAccount?.email || session?.user.email,
          // }),
        },
      );
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    },
  });
  const isPro = useMemo(() => {
    return subscribeData?.data;
  }, [subscribeData]);
  // const isPro = false;
  const openBillingPortal = async () => {
    if (!(activeAccount?.email || session?.user.email)) {
      toast.error('Please login');
      return;
    }
    try {
      const res = await fetch(
        import.meta.env.VITE_PUBLIC_BACKEND_URL + '/api/pay/customer_portal',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          // body: JSON.stringify({
          //   appUsername: activeAccount?.email || session?.user.email,
          // }),
          credentials: 'include',
        },
      );
      if (!res.ok) {
        const error: any = new Error('An error occurred while fetching the data.');
        error.status = res.status;
        throw error;
      }
      const jsonData: any = await res.json();
      window.open(jsonData.data, '_self', 'noopener,noreferrer');
    } catch (error: any) {
      toast.error('failed to get customer portal url:', error);
    }
  };
  // const billingCustomer = {
  //   stripe_id: null,
  // };
  const showBillingPortal = true;

  const pathname = useLocation().pathname;
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();

  const { revalidate } = useRevalidator();
  const [, setPricingDialog] = useQueryState('pricingDialog');

  const getSettingsHref = useCallback(() => {
    const category = searchParams.get('category');
    const currentPath = category
      ? `${pathname}?category=${encodeURIComponent(category)}`
      : pathname;
    return `/settings/general?from=${encodeURIComponent(currentPath)}`;
  }, [pathname, searchParams]);

  const handleClearCache = useCallback(async () => {
    try {
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🧹 开始清理所有缓存数据`);

      // 1. 清理当前账号的内存缓存
      queryClient.clear();
      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 已清理 React Query 内存缓存`);

      // 2. 清理所有账号的IndexedDB缓存（React Query 持久化缓存）
      await idbClear();
      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 已清理 IndexedDB React Query 缓存`);

      // 3. 清理所有账号的缓存清单数据
      const allManifests = await cacheManifestManager.getAllManifests();
      console.log(`[${new Date().toLocaleString('zh-CN')}] 📋 找到 ${allManifests.length} 个缓存清单`);

      for (const manifest of allManifests) {
        await cacheManifestManager.clearManifest(manifest.accountId);
        console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 已清理缓存清单: ${manifest.accountEmail}`);
      }

      console.log(`[${new Date().toLocaleString('zh-CN')}] 🎉 所有缓存数据清理完成`);
      toast.success('All cache cleared successfully (including cache manifests)');
    } catch (error) {
      console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 清理缓存时出错:`, error);
      toast.error('Failed to clear some cache data');
    }
  }, [queryClient]);

  const handleCopyConnectionId = useCallback(async () => {
    await navigator.clipboard.writeText(activeConnection?.id || '');
    toast.success('Connection ID copied to clipboard');
  }, [activeConnection]);

  // const activeAccount = useMemo(() => {
  //   if (!activeConnection || !data) return null;
  //   return data.connections?.find((connection) => connection.id === activeConnection.id);
  // }, [activeConnection, data]);

  useEffect(() => setIsRendered(true), []);

  const handleAccountSwitch = (connectionId: string, email:string) => async () => {
    if (connectionId === activeConnection?.id) return;

//const newConnection = data?.connections?.find(conn => conn.id === connectionId);

    // 🎯 设置加载状态
    setSwitchingConnectionId(connectionId);

    try {
      // 清除当前线程
      setThreadId(null);

      // 设置新的默认连接
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🔧 调用setDefaultConnection API...`);
      await setDefaultConnection({ connectionId });

    // 🎯 刷新活跃连接状态（移除 refetchConnections 避免重复调用）
    console.log(`[${new Date().toLocaleString('zh-CN')}] 🔄 刷新活跃连接状态...`);
    await refetchActiveConnection();
    // 🎯 等待一小段时间确保状态完全更新
    //await new Promise(resolve => setTimeout(resolve, 200))
    // 
    if (email) {
      // 🎯 直接通知 QueryProvider 切换缓存
      window.dispatchEvent(new CustomEvent('switchToEmail', {
        detail: { email: email }
      }));
      // 同时更新本地存储
      switchAccount(email);
    }

      console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 账号切换完成，跳转到 inbox 页面...`);
      navigate('/mail/inbox', { replace: true });
    } catch (error) {
      console.error(`[${new Date().toLocaleString('zh-CN')}] ❌ 账号切换失败:`, error);
      toast.error('账号切换失败，请重试');
    } finally {
      // 🎯 清除加载状态
      setSwitchingConnectionId(null);
    }
  };

  const handleLogout = async () => {
    toast.promise(signOut(), {
      loading: 'Signing out...',
      success: () => 'Signed out successfully!',
      error: 'Error signing out',
      async finally() {
        // 🗑️ 清理本地账号存储，避免下次登录时使用旧账号的缓存
        try {
          localAccountStorage.clearAll();
          console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 退出登录时已清理本地账号存储`);
        } catch (error) {
          console.error('清理本地账号存储失败:', error);
        }

        window.location.href = '/login';
      },
    });
  };

  const { data: brainState, refetch: refetchBrainState } = useBrainState();

  const otherConnections = useMemo(() => {
    if (!data || !activeAccount) return [];
    return data.connections.filter((connection) => connection.id !== activeAccount?.id);
  }, [data, activeAccount]);

  const handleThemeToggle = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  if (!isRendered) return null;
  if (!session) return null;

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center gap-3">
        {state === 'collapsed' ? (
          activeAccount && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex cursor-pointer items-center">
                  <div className="relative">
                    <Avatar className="size-8 rounded-[5px]">
                      <AvatarImage
                        className="rounded-[5px]"
                        src={activeAccount?.picture || undefined}
                        alt={activeAccount?.name || activeAccount?.email}
                      />

                      <AvatarFallback className="rounded-[5px] text-[10px]">
                        {(activeAccount?.name || activeAccount?.email)
                          .split(' ')
                          .map((n) => n[0])
                          .join('')
                          .toUpperCase()
                          .slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="ml-3 w-[--radix-dropdown-menu-trigger-width] min-w-56 bg-white font-medium dark:bg-[#131313]"
                align="end"
                side={'bottom'}
                sideOffset={8}
              >
                {session && activeAccount && (
                  <>
                    <div className="flex flex-col items-center p-3 text-center">
                      <Avatar className="border-border/50 mb-2 size-14 rounded-xl border">
                        <AvatarImage
                          className="rounded-xl"
                          src={
                            (activeAccount.picture ?? undefined) ||
                            (session.user.image ?? undefined)
                          }
                          alt={activeAccount.name || session.user.name || 'User'}
                        />
                        <AvatarFallback className="rounded-xl">
                          <span>
                            {(activeAccount.name || session.user.name || 'User')
                              .split(' ')
                              .map((n) => n[0])
                              .join('')
                              .toUpperCase()
                              .slice(0, 2)}
                          </span>
                        </AvatarFallback>
                      </Avatar>
                      <div className="w-full">
                        <div className="flex items-center justify-center gap-0.5 text-sm font-medium">
                          {activeAccount.name || session.user.name || 'User'}
                          {isPro && (
                            <BadgeCheck
                              className="h-4 w-4 text-white dark:text-[#141414]"
                              fill="#1D9BF0"
                            />
                          )}
                        </div>
                        <div className="text-muted-foreground text-xs">{activeAccount.email}</div>
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                  </>
                )}
                <div className="space-y-1">
                  <>
                    <p className="text-muted-foreground px-2 py-1 text-[11px] font-medium">
                      {t('common.navUser.accounts')}
                    </p>

                    {data?.connections
                      ?.filter((connection) => connection.id !== activeConnection?.id)
                      .map((connection) => (
                        <DropdownMenuItem
                          key={connection.id}
                          onClick={handleAccountSwitch(connection.id, connection.email)}
                          className="flex cursor-pointer items-center gap-3 py-1"
                        >
                          <Avatar className="size-7 rounded-lg">
                            <AvatarImage
                              className="rounded-lg"
                              src={connection.picture || undefined}
                              alt={connection.name || connection.email}
                            />
                            <AvatarFallback className="rounded-lg text-[10px]">
                              {(connection.name || connection.email)
                                .split(' ')
                                .map((n) => n[0])
                                .join('')
                                .toUpperCase()
                                .slice(0, 2)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="-space-y-0.5">
                            <p className="text-[12px]">{connection.name || connection.email}</p>
                            {connection.name && (
                              <p className="text-muted-foreground text-[11px]">
                                {connection.email.length > 25
                                  ? `${connection.email.slice(0, 25)}...`
                                  : connection.email}
                              </p>
                            )}
                          </div>
                        </DropdownMenuItem>
                      ))}
                    <AddConnectionDialog />

                    <DropdownMenuSeparator className="my-1" />

                    <DropdownMenuItem onClick={handleThemeToggle} className="cursor-pointer">
                      <div className="flex w-full items-center gap-2">
                        {theme === 'dark' ? (
                          <MoonIcon className="size-4 opacity-60" />
                        ) : (
                          <SunIcon className="size-4 opacity-60" />
                        )}
                        <p className="text-[13px] opacity-60">{t('common.navUser.appTheme')}</p>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <a href={getSettingsHref()} className="cursor-pointer">
                        <div className="flex items-center gap-2">
                          <Settings size={16} className="opacity-60" />
                          <p className="text-[13px] opacity-60">{t('common.actions.settings')}</p>
                        </div>
                      </a>
                    </DropdownMenuItem>
                    {/* <DropdownMenuItem>
                      <a href="https://discord.gg/0email" target="_blank" className="w-full">
                        <div className="flex items-center gap-2">
                          <HelpCircle size={16} className="opacity-60" />
                          <p className="text-[13px] opacity-60">
                            {t('common.navUser.customerSupport')}
                          </p>
                        </div>
                      </a>
                    </DropdownMenuItem> */}
                    <DropdownMenuItem className="cursor-pointer" onClick={handleLogout}>
                      <div className="flex items-center gap-2">
                        <LogOut size={16} className="opacity-60" />
                        <p className="text-[13px] opacity-60">{t('common.actions.logout')}</p>
                      </div>
                    </DropdownMenuItem>
                  </>
                </div>
                <>
                  <DropdownMenuSeparator className="mt-1" />
                  <div className="text-muted-foreground/60 flex items-center justify-center gap-1 px-2 pb-2 pt-1 text-[10px]">
                    <a href="/privacy" className="hover:underline">
                      Privacy
                    </a>
                    <span>·</span>
                    <a href="/terms" className="hover:underline">
                      Terms
                    </a>
                  </div>
                </>
              </DropdownMenuContent>
            </DropdownMenu>
          )
        ) : (
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-2">
              {data && activeAccount ? (
                <div
                  key={activeAccount.id}
                  onClick={handleAccountSwitch(activeAccount.id, activeAccount.email)}
                  className={`flex cursor-pointer items-center ${
                    activeAccount.id === activeConnection?.id && data.connections.length > 1
                      ? 'outline-mainBlue rounded-full outline outline-2'
                      : ''
                  }`}
                >
                  <div className="relative">
                    <Avatar className="size-7 rounded-full">
                      <AvatarImage
                        className="rounded-full"
                        src={activeAccount.picture || undefined}
                        alt={activeAccount.name || activeAccount.email}
                      />
                      <AvatarFallback className="rounded-full text-[10px]">
                        {(activeAccount.name || activeAccount.email)
                          .split(' ')
                          .map((n) => n[0])
                          .join('')
                          .toUpperCase()
                          .slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    {/* 🎯 加载动画 - 当正在切换到此账号时显示 */}
                    {switchingConnectionId === activeAccount.id ? (
                      <Loader2 className="text-mainBlue absolute -bottom-2 left-1/2 size-4 -translate-x-1/2 animate-spin rounded-full bg-white dark:bg-[#141414]" />
                    ) : (
                      activeAccount.id === activeConnection?.id && data.connections.length > 1 && (
                        <CircleCheck className="fill-mainBlue absolute -bottom-2 left-1/2 size-4 -translate-x-1/2 rounded-full bg-white dark:bg-[#141414]" />
                      )
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex cursor-pointer items-center">
                  <div className="relative">
                    <div className="bg-muted size-7 animate-pulse rounded-[5px]" />
                  </div>
                </div>
              )}
              {otherConnections.slice(0, data && data.connections.length > 3 ? 2 : otherConnections.length).map((connection) => (
                <Tooltip key={connection.id}>
                  <TooltipTrigger asChild>
                    <div
                      onClick={handleAccountSwitch(connection.id , connection.email)}
                      className={`flex cursor-pointer items-center ${
                        connection.id === activeConnection?.id && otherConnections.length > 1
                          ? 'outline-mainBlue rounded-[5px] outline outline-2'
                          : ''
                      }`}
                    >
                      <div className="relative">
                        <Avatar className="size-7 rounded-full">
                          <AvatarImage
                            className="rounded-full"
                            src={connection.picture || undefined}
                            alt={connection.name || connection.email}
                          />
                          <AvatarFallback className="rounded-full text-[10px]">
                            {(connection.name || connection.email)
                              .split(' ')
                              .map((n) => n[0])
                              .join('')
                              .toUpperCase()
                              .slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                        {/* 🎯 加载动画 - 当正在切换到此账号时显示 */}
                        {switchingConnectionId === connection.id ? (
                          <Loader2 className="text-mainBlue absolute -bottom-2 -right-2 size-4 animate-spin rounded-full bg-white dark:bg-black" />
                        ) : (
                          connection.id === activeConnection?.id && otherConnections.length > 1 && (
                            <CircleCheck className="fill-mainBlue absolute -bottom-2 -right-2 size-4 rounded-full bg-white dark:bg-black" />
                          )
                        )}
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent className="text-muted-foreground text-xs">
                    {connection.email}
                  </TooltipContent>
                </Tooltip>
              ))}

              {data && data.connections.length > 3 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="hover:bg-muted flex h-7 w-7 cursor-pointer items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700">
                      <span className="text-[10px] text-gray-600 dark:text-gray-300">+{otherConnections.length - 2}</span>
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="ml-3 min-w-56 bg-white font-medium dark:bg-[#131313]"
                    align="end"
                    side={'bottom'}
                    sideOffset={8}
                  >
                    {otherConnections.slice(2).map((connection) => (
                      <DropdownMenuItem
                        key={connection.id}
                        onClick={handleAccountSwitch(connection.id, connection.email)}
                        className="flex cursor-pointer items-center gap-3 py-1"
                      >
                        <Avatar className="size-7 rounded-lg">
                          <AvatarImage
                            className="rounded-lg"
                            src={connection.picture || undefined}
                            alt={connection.name || connection.email}
                          />
                          <AvatarFallback className="rounded-lg text-[10px]">
                            {(connection.name || connection.email)
                              .split(' ')
                              .map((n) => n[0])
                              .join('')
                              .toUpperCase()
                              .slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="-space-y-0.5">
                          <p className="text-[12px]">{connection.name || connection.email}</p>
                          {connection.name && (
                            <p className="text-muted-foreground text-[11px]">
                              {connection.email.length > 25
                                ? `${connection.email.slice(0, 25)}...`
                                : connection.email}
                            </p>
                          )}
                        </div>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {true ? (
                <AddConnectionDialog>
                  <button className="flex h-7 w-7 cursor-pointer items-center justify-center rounded-full border border-dashed dark:bg-[#262626] dark:text-[#929292]">
                    <Plus className="size-4" />
                  </button>
                </AddConnectionDialog>
              ) : (
                <>
                  <Button
                    onClick={() => setPricingDialog('true')}
                    className="hover:bg-offsetLight/80 flex h-7 w-7 cursor-pointer items-center justify-center rounded-full border border-dashed bg-transparent px-0 text-black dark:bg-[#262626] dark:text-[#929292]"
                  >
                    <Plus className="size-4" />
                  </Button>
                </>
              )}
            </div>

            <div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className={cn('md:h-fit md:px-2')}>
                    <ThreeDots className="fill-iconLight dark:fill-iconDark" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="ml-3 min-w-56 bg-white font-medium dark:bg-[#131313]"
                  align="end"
                  side={'bottom'}
                  sideOffset={8}
                >
                  <div className="space-y-1">
                    {showBillingPortal ? (
                      <DropdownMenuItem onClick={() => openBillingPortal()}>
                        <div className="flex items-center gap-2">
                          <BanknoteIcon size={16} className="opacity-60" />
                          <p className="text-[13px] opacity-60">Billing</p>
                        </div>
                      </DropdownMenuItem>
                    ) : null}
                    <DropdownMenuItem onClick={handleThemeToggle} className="cursor-pointer">
                      <div className="flex w-full items-center gap-2">
                        {theme === 'dark' ? (
                          <MoonIcon className="size-4 opacity-60" />
                        ) : (
                          <SunIcon className="size-4 opacity-60" />
                        )}
                        <p className="text-[13px] opacity-60">{t('common.navUser.appTheme')}</p>
                      </div>
                    </DropdownMenuItem>
                    {/* <DropdownMenuItem>
                      <a href="https://discord.gg/0email" target="_blank" className="w-full">
                        <div className="flex items-center gap-2">
                          <HelpCircle size={16} className="opacity-60" />
                          <p className="text-[13px] opacity-60">
                            {t('common.navUser.customerSupport')}
                          </p>
                        </div>
                      </a>
                    </DropdownMenuItem> */}
                    <DropdownMenuItem className="cursor-pointer" onClick={handleLogout}>
                      <div className="flex items-center gap-2">
                        <LogOut size={16} className="opacity-60" />
                        <p className="text-[13px] opacity-60">{t('common.actions.logout')}</p>
                      </div>
                    </DropdownMenuItem>
                  </div>

                  <DropdownMenuSeparator className="mt-1" />
                  <div className="text-muted-foreground/60 flex items-center justify-center gap-1 px-2 pb-2 pt-1 text-[10px]">
                    <a href="/privacy" className="hover:underline">
                      Privacy
                    </a>
                    <span>·</span>
                    <a href="/terms" className="hover:underline">
                      Terms
                    </a>
                  </div>
                  <DropdownMenuSeparator className="mt-1" />
                  <p className="text-muted-foreground px-2 py-1 text-[11px] font-medium">Debug</p>
                  {/* <DropdownMenuItem onClick={handleCopyConnectionId}>
                    <div className="flex items-center gap-2">
                      <CopyCheckIcon size={16} className="opacity-60" />
                      <p className="text-[13px] opacity-60">Copy Connection ID</p>
                    </div>
                  </DropdownMenuItem> */}
                  <DropdownMenuItem onClick={handleClearCache}>
                    <div className="flex items-center gap-2">
                      <HelpCircle size={16} className="opacity-60" />
                      <p className="text-[13px] opacity-60">Clear Local Cache</p>
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}
      </div>

      {state !== 'collapsed' && (
        <div className="flex items-center justify-between gap-2">
          <div className="my-2 flex flex-col items-start gap-1 space-y-1">
            <div className="flex hidden items-center gap-1 text-[13px] leading-none text-black dark:text-white">
              <p className="max-w-[16.5ch] truncate text-[13px]">
                {activeAccount?.name || session.user.name || 'User'}
              </p>
              {isPro ? (
                <BadgeCheck className="h-4 w-4 text-white dark:text-[#141414]" fill="#1D9BF0" />
              ) : (
                <button
                  onClick={() => setPricingDialog('true')}
                  className="flex hidden h-5 items-center gap-1 rounded-full border px-1 pr-1.5 hover:bg-transparent"
                >
                  <BadgeCheck className="h-4 w-4 text-white dark:text-[#141414]" fill="#1D9BF0" />
                  <span className="text-muted-foreground text-[10px] uppercase">Get verified</span>
                </button>
              )}
            </div>
            <div className="h-5 max-w-[200px] overflow-hidden truncate text-[13px] font-normal leading-none text-black dark:text-white">
              {activeAccount?.email || session.user.email}
            </div>
          </div>

          <div className="ml-2">{/* Gauge component removed */}</div>
        </div>
      )}

      <div className="space-y-1">
        {/* <div>
          <div className="text-muted-foreground flex justify-between text-[10px] uppercase tracking-widest">
            <span>AI Chats</span>
            {chatMessages.unlimited ? (
              <span>Unlimited</span>
            ) : (
              <span>
                {chatMessages.remaining}/{chatMessages.total}
              </span>
            )}
          </div>
          <Progress className="h-1" value={(chatMessages.remaining! / chatMessages.total) * 100} />
        </div> */}
        {/* <div>
          <div className="text-muted-foreground flex justify-between text-[10px] uppercase tracking-widest">
            <span>AI Labels</span>
            {brainActivity.unlimited ? (
              <span>Unlimited</span>
            ) : (
              <span>
                {brainActivity.remaining}/{brainActivity.total}
              </span>
            )}
          </div>
          <Progress
            className="h-1"
            value={(brainActivity.remaining! / brainActivity.total) * 100}
          />
        </div> */}
      </div>
    </div>
  );
}
