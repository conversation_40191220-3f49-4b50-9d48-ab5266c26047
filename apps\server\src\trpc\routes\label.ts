import { activeDriverProcedure, createRateLimiterMiddleware, router } from '../trpc';
import { Ratelimit } from '@upstash/ratelimit';
import { z } from 'zod';

export const labelsRouter = router({
  list: activeDriverProcedure
    .use(
      createRateLimiterMiddleware({
        generatePrefix: ({ session }) => `ratelimit:get-labels-${session?.user.id}`,
        limiter: Ratelimit.slidingWindow(60, '1m'),
      }),
    )
    .query(async ({ ctx }) => {
      const { driver } = ctx;
      return (await driver.getUserLabels()).filter((label) => label.type === 'user');
    }),
  create: activeDriverProcedure
    .use(
      createRateLimiterMiddleware({
        generatePrefix: ({ session }) => `ratelimit:labels-post-${session?.user.id}`,
        limiter: Ratelimit.slidingWindow(60, '1m'),
      }),
    )
    .input(
      z.object({
        name: z.string(),
        color: z
          .object({
            backgroundColor: z.string(),
            textColor: z.string(),
          })
          .default({
            backgroundColor: '',
            textColor: '',
          }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { driver } = ctx;
      const label = {
        ...input,
        type: 'user',
      };
      return await driver.createLabel(label);
    }),
  update: activeDriverProcedure
    .use(
      createRateLimiterMiddleware({
        generatePrefix: ({ session }) => `ratelimit:labels-patch-${session?.user.id}`,
        limiter: Ratelimit.slidingWindow(60, '1m'),
      }),
    )
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        type: z.string().optional(),
        color: z
          .object({
            backgroundColor: z.string(),
            textColor: z.string(),
          })
          .optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { driver } = ctx;
      const { id, ...label } = input;
      return await driver.updateLabel(id, label);
    }),
  delete: activeDriverProcedure
    .use(
      createRateLimiterMiddleware({
        generatePrefix: ({ session }) => `ratelimit:labels-delete-${session?.user.id}`,
        limiter: Ratelimit.slidingWindow(60, '1m'),
      }),
    )
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { driver } = ctx;
      return await driver.deleteLabel(input.id);
    }),
});
