import { disableBrainFunction, enableBrainFunction, getPrompts } from '../../lib/brain';
import { redis } from '../../lib/services';
import { activeConnectionProcedure, router } from '../trpc';

import { z } from 'zod';

/**
 * Gets the current connection limit for a given connection ID
 * @param connectionId The connection ID to check
 * @returns Promise<number> The current limit
 */
export const getConnectionLimit = async (connectionId: string): Promise<number> => {
  try {
    return 9999;
  } catch (error) {
    console.error(`[GET_CONNECTION_LIMIT] Error getting limit for ${connectionId}:`, error);
    throw error;
  }
};

const labelSchema = z.object({
  name: z.string(),
  usecase: z.string(),
});

const labelsSchema = z.array(labelSchema);

const cache = redis();

export const brainRouter = router({
  enableBrain: activeConnectionProcedure
    .input(
      z.object({
        connection: z
          .object({
            id: z.string(),
            providerId: z.string(),
          })
          .optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      let { connection } = input;
      if (!connection) connection = ctx.activeConnection;
      return await enableBrainFunction(connection);
    }),
  disableBrain: activeConnectionProcedure
    .input(
      z.object({
        connection: z
          .object({
            id: z.string(),
            providerId: z.string(),
          })
          .optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      let { connection } = input;
      if (!connection) connection = ctx.activeConnection;
      return await disableBrainFunction(connection);
    }),

  generateSummary: activeConnectionProcedure
    .input(
      z.object({
        threadId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      
      return  {
        data: {
          short: "",
        },
      };
      
    }),
  getState: activeConnectionProcedure.query(async ({ ctx }) => {
    const connection = ctx.activeConnection;
    const state = await cache.get<string>(`subscribed_accounts:${connection.id}`);
    if (!state) return { enabled: false };
    const limit = await getConnectionLimit(connection.id);
    return { limit, enabled: true };
    // const state = await env.subscribed_accounts.get(connection.id);
    // if (!state || state === 'pending') return { enabled: false };
    // const limit = await getConnectionLimit(connection.id);
    // return { limit: 9999, enabled: true };
  }),
  getLabels: activeConnectionProcedure
    .output(
      z.array(
        z.object({
          name: z.string(),
          usecase: z.string(),
        }),
      ),
    )
    .query(async ({ ctx }) => {
      const connection = ctx.activeConnection;
      // const labels = "";
      const labels = await cache.get<any>(`connection_labels:${connection.id}`);
      console.log("debug brain labels: ", labels, Object.prototype.toString.call(labels));
      try {
        if (Object.prototype.toString.call(labels)==='[object Array]') {
          return (labels as z.infer<typeof labelsSchema>);
        }else if (Object.prototype.toString.call(labels)==='[object String]') {
          return (JSON.parse(labels) as z.infer<typeof labelsSchema>);
        }else {
          return [];
        }
        // return labels ? (JSON.parse(labels) as z.infer<typeof labelsSchema>) : [];
      } catch (error) {
        console.error(`[GET_LABELS] Error parsing labels for ${connection.id}:`, error);
        return [];
      }
    }),
  getPrompts: activeConnectionProcedure.query(async ({ ctx }) => {
    const connection = ctx.activeConnection;
    return await getPrompts({ connectionId: connection.id });
  }),
  updateLabels: activeConnectionProcedure
    .input(
      z.object({
        labels: labelsSchema,
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const connection = ctx.activeConnection;
      console.log(input.labels);

      const labels = labelsSchema.parse(input.labels);
      console.log(labels);
      // 过滤掉重复的
      const filteredLabels = labels.filter((obj1, i, arr) => 
        arr.findIndex(obj2 => (obj2.name === obj1.name)) === i
      )
      console.log('debug updateLabels after filter, filteredLabels:',filteredLabels);

      //await env.connection_labels.put(connection.id, JSON.stringify(labels));
      // const preLabels = await cache.get<any>(`connection_labels:${connection.id}`);
      // if (Object.prototype.toString.call(labels)==='[object Array]') {
      //   let preLabelNames = []
      //   for (const prelabel of preLabels as z.infer<typeof labelsSchema>) {
      //     preLabelNames.push(prelabel.name);
      //   }
      // }
      await cache.set<string>(`connection_labels:${connection.id}`, JSON.stringify(filteredLabels));
      return { success: true };
    }),
});
