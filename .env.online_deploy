HOSTNAME=0.0.0.0
VITE_PUBLIC_APP_URL=https://onemails.ai
GOOGLE_CLIENT_ID=439216317135-l82ffr472gu0m3nf7ie51v2ncr1nq7iv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
MICROSOFT_CLIENT_ID=c37907c1-ac02-4eda-ade8-ef7e1976c81d
MICROSOFT_CLIENT_SECRET=****************************************
MICROSOFT_REDIRECT_URI=https://onemails.ai/api/auth/callback/microsoft
ICLOUD_CLIENT_ID=5e0c7c0c-c7c0-4c7c-8c7c-7c7c7c7c7c7c
ICLOUD_CLIENT_SECRET=5e0c7c0c-c7c0-4c7c-8c7c-7c7c7c7c7c7c
ICLOUD_REDIRECT_URI=http://localhost:3000/api/auth/microsoft/callback
VITE_PUBLIC_BACKEND_URL=https://onemails.ai
DATABASE_URL=postgresql://postgres:aihubs2025@127.0.0.1:5432/zerodotemail
BETTER_AUTH_SECRET=6T4p6kS9Yg8w7qH5l2xVzQJbXr3FmKvN/LjP0DdCnA==
BETTER_AUTH_URL=https://onemails.ai
COOKIE_DOMAIN=onemails.ai

# cli
#REDIS_URL=http://host.docker.internal:8079

# docker
REDIS_URL=http://127.0.0.1:8079

REDIS_TOKEN=upstash-local-token-0618
RESEND_API_KEY=
OPENAI_API_KEY=sk-xqDLtFyKdFrQ8mBb7bB091F12e364e5a8f3bC3952f215222
OPENAI_BASE_URL=https://in.aihubs.cn:18881/v1
AI_SYSTEM_PROMPT=
NODE_ENV=development
# NODE_ENV=production
AUTUMN_SECRET_KEY=am_sk_test_a4JJh9DgbRqgfLdxFHcjtj0VYsWuXzAUFmMDSNPAYT
#NODE_TLS_REJECT_UNAUTHORIZED=0
# NEXT_PUBLIC_BACKEND_URL=http://host.docker.internal:8787