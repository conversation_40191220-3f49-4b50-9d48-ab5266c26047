import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Link, useNavigate, useSearchParams } from 'react-router';
import { z } from 'zod';
import { useState } from 'react';
import { useTRPC } from '@/providers/query-provider';
import { useMutation } from '@tanstack/react-query';
import { Loader2, ArrowLeft, Mail, Shield } from 'lucide-react';
import { signIn, signUp } from '@/lib/auth-client';
import { useTranslations } from 'use-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { imapSmtpConfig } from '@/lib/constants';
import { setActiveOnLogin } from '@/lib/local-account-storage';

export default function SmtpImapLoginPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const trpc = useTRPC();
  const t = useTranslations();

  // 🎯 检查是否为添加连接模式
  const isAddConnectionMode = searchParams.get('mode') === 'add-connection';

  // 🎯 获取邮箱提供商类型
  const provider = searchParams.get('provider') || 'other';

  // 🎯 根据提供商获取标题和描述
  const getProviderInfo = (provider: string) => {
    switch (provider) {
      case 'icloud':
        return {
          title: 'iCloud 邮箱登录',
          description: '使用您的 iCloud 邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      case 'onemails':
        return {
          title: 'OneMails 邮箱登录',
          description: '使用您的 OneMails 邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      case '163':
        return {
          title: '163 邮箱登录',
          description: '使用您的 163 邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      case 'qq':
        return {
          title: 'QQ 邮箱登录',
          description: '使用您的 QQ 邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      case 'aliyun':
        return {
          title: '阿里邮箱',
          description: '使用您的阿里邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      default:
        return {
          title: '其它邮箱登录',
          description: '使用您的邮箱账号登录',
          placeholder: '<EMAIL>'
        };
    }
  };

  const providerInfo = getProviderInfo(provider);

  const credentialsSchema = z.object({
    email: z.string().email(t('auth.smtpImapLogin.invalidEmailFormat')),
    password: z.string().min(1, t('auth.smtpImapLogin.passwordRequired')),
  });

  type CredentialsValues = z.infer<typeof credentialsSchema>;

  const { mutateAsync: testConnection } = useMutation(
    trpc.connections.testConnectionPublic.mutationOptions(),
  );

  const { mutateAsync: addImapSmtpConnection } = useMutation(
    trpc.connections.addImapSmtpConnection.mutationOptions(),
  );

  // 注意：checkEmailExists 是 query，不是 mutation

  const { mutateAsync: setDefaultConnection } = useMutation(
    trpc.connections.setDefault.mutationOptions(),
  );

  const credentialsForm = useForm<CredentialsValues>({
    resolver: zodResolver(credentialsSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onCredentialsSubmit = async (data: CredentialsValues) => {
    setIsSubmitting(true);
    try {
      console.log(t('auth.smtpImapLogin.autoConfiguring'));

      // 简化的登录流程：直接验证IMAP连接并添加连接
      // 不依赖Better Auth的用户系统

      console.log(t('auth.smtpImapLogin.testingConnection'));

      // 根据邮箱域名获取自动配置
      const domain = data.email.split('@')[1];
      const getAutoConfig = (domain: string) => {
        const configs: Record<string, any> = imapSmtpConfig;
        return configs[domain] || {
          imapHost: `imap.${domain}`,
          imapPort: 993,
          imapSecure: true,
          smtpHost: `smtp.${domain}`,
          smtpPort: 465,
          smtpSecure: true,
        };
      };

      const config = getAutoConfig(domain);

      // 验证IMAP连接
      try {
        const connectionResult = await testConnection({
          email: data.email,
          password: data.password,
          imapHost: config.imapHost,
          imapPort: config.imapPort.toString(),
          imapSecure: config.imapSecure,
          smtpHost: config.smtpHost,
          smtpPort: config.smtpPort.toString(),
          smtpSecure: config.smtpSecure,
        });

        console.log('连接测试结果:', connectionResult);

        // 检查连接测试结果
        if (!connectionResult.success) {
          console.error('连接验证失败:', connectionResult);
          const errorMsg = connectionResult.imapTest?.error || connectionResult.smtpTest?.error || '连接验证失败';
          toast.error(t('auth.smtpImapLogin.connectionFailed', { error: errorMsg }));
          return;
        }

        console.log('✅ IMAP连接验证成功');
        toast.success(t('auth.smtpImapLogin.connectionSuccess'));

      } catch (imapError: any) {
        console.error('IMAP验证过程中发生错误:', imapError);
        toast.error(t('auth.smtpImapLogin.connectionFailed', { error: imapError.message || 'Unknown error' }));
        return;
      }

      // 🎯 根据模式决定是否需要用户登录
      let loginSuccess = false;

      if (isAddConnectionMode) {
        // 添加连接模式：用户已登录，直接添加连接
        console.log('添加连接模式：用户已登录，直接添加IMAP连接');
        loginSuccess = true;
      } else {
        // 初始登录模式：智能登录逻辑
        console.log('初始登录模式：检查邮箱是否已存在于系统中');

        try {
          // 🎯 首先检查邮箱是否已存在于系统中
          console.log('检查邮箱是否已存在:', data.email);

          let data_result: { exists: boolean; userId?: string; userEmail?: string; userName?: string } | null = null;

          try {
            // 🎯 修复 TRPC query 参数格式
            const queryParams = new URLSearchParams({
              input: JSON.stringify({
                "0": {
                  email: data.email
                }
              })
            });

            const response = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_URL || 'http://localhost:8787'}/api/trpc/connections.checkEmailExists?${queryParams}`, {
              method: 'GET',
              headers: { 'Content-Type': 'application/json' },
            });

            if (response.ok) {
              const emailCheckResult = await response.json() as { result?: { data?: { exists: boolean; userId?: string; userEmail?: string; userName?: string } } };
              data_result = emailCheckResult.result?.data || null;
              console.log('邮箱检查结果:', data_result);
            } else {
              console.error('检查邮箱API调用失败:', response.status, response.statusText);
              const errorText = await response.text();
              console.error('错误详情:', errorText);
            }
          } catch (apiError) {
            console.error('检查邮箱API调用异常:', apiError);
          }

          if (data_result?.exists) {
            console.log(`✅ 邮箱已存在于系统中，用户ID: ${data_result.userId}`);
            console.log('邮箱已存在，跳过 Better Auth 登录，直接进入连接添加流程');

            // 邮箱已存在，说明用户已经在系统中
            // 不需要通过 Better Auth 登录，直接标记登录成功
            // 后续的连接添加会通过 hooks 自动合并账户
            loginSuccess = true;
            console.log('✅ 已存在用户，跳过登录验证，直接进入连接添加流程');
          } else {
            console.log('ℹ️ 邮箱不存在于系统中或API调用失败，尝试登录，如果失败则注册');

            // 先尝试登录（可能用户已存在但API调用失败）
            const loginResult = await signIn.email({
              email: data.email,
              password: 'smtp-imap-user',
            });

            if (loginResult.error) {
              console.log('登录失败，尝试注册新用户:', loginResult.error);

              // 登录失败，尝试注册新用户
              const signUpResult = await signUp.email({
                email: data.email,
                password: 'smtp-imap-user',
                name: data.email.split('@')[0] || 'SMTP User',
              });

              if (signUpResult.error) {
                console.error('注册失败:', signUpResult.error);
                toast.error(t('auth.smtpImapLogin.registrationFailed', { error: signUpResult.error.message || 'Unknown error' }));
                return;
              }

              console.log(t('auth.smtpImapLogin.registrationSuccess'));

              // 注册成功后登录
              const retryLoginResult = await signIn.email({
                email: data.email,
                password: 'smtp-imap-user',
              });

              if (retryLoginResult.error) {
                console.error('注册后登录失败:', retryLoginResult.error);
                toast.error(t('auth.smtpImapLogin.loginFailed', { error: retryLoginResult.error.message || 'Unknown error' }));
                return;
              }

              loginSuccess = true;
              console.log('✅ 新用户注册并登录成功');
              toast.success(t('auth.smtpImapLogin.loginSuccess'));
            } else {
              loginSuccess = true;
              console.log('✅ 已存在用户登录成功');
            }
          }

          // 🎯 登录成功后，更新账户的令牌字段
          try {
            console.log('更新账户令牌信息...');

            // 构建 IMAP/SMTP 服务器信息作为 scope
            const domain = data.email.split('@')[1];
            const serverInfo = {
              imap: {
                host: imapSmtpConfig[domain] ? imapSmtpConfig[domain].imapHost : `imap.${domain}` ,
                port: imapSmtpConfig[domain] ? imapSmtpConfig[domain].imapPort : 993,
                secure: imapSmtpConfig[domain] ? imapSmtpConfig[domain].imapSecure : true
              },
              smtp: {
                host: imapSmtpConfig[domain] ? imapSmtpConfig[domain].smtpHost : `smtp.${domain}`,
                port: imapSmtpConfig[domain] ? imapSmtpConfig[domain].smtpPort : 465,
                secure: imapSmtpConfig[domain] ? imapSmtpConfig[domain].smtpSecure : true
              }
            };

            // 调用后端 API 更新账户令牌
            const updateResponse = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_URL || 'http://localhost:8787'}/api/trpc/connections.updateAccountTokens`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              credentials: 'include',
              body: JSON.stringify({
                "0": {
                  input: {
                    email: data.email,
                    password: data.password,
                    serverInfo: JSON.stringify(serverInfo)
                  }
                }
              }),
            });

            if (updateResponse.ok) {
              console.log('✅ 账户令牌更新成功');
            } else {
              console.warn('⚠️ 账户令牌更新失败，但不影响登录');
            }
          } catch (updateError: any) {
            console.warn('⚠️ 更新账户令牌时出错，但不影响登录:', updateError.message);
          }

        } catch (authError: any) {
          console.error('认证过程出错:', authError);
          toast.error(t('auth.smtpImapLogin.loginFailed', { error: authError.message || 'Unknown error' }));
          return;
        }
      }

      // 检查登录是否成功
      if (!loginSuccess) {
        toast.error(t('auth.smtpImapLogin.loginFailed', { error: 'Authentication failed' }));
        return;
      }

      console.log('用户认证成功，开始添加IMAP连接');

      // 登录成功后，添加SMTP/IMAP连接（服务器端会自动配置）
      try {
        const connectionResult = await addImapSmtpConnection({
          provider: 'credential' as any,
          auth: {
            email: data.email,
            refreshToken: data.password, // 存储实际的邮箱密码/授权码
            // 不传递 host 等参数，让服务器端自动配置
          },
          isAddConnectionMode,
        });

        console.log('连接添加成功，连接ID:', connectionResult.connectionId);

        // 强制设置为默认连接
        try {
          await setDefaultConnection({ connectionId: connectionResult.connectionId });
          console.log('已设置为默认连接');
        } catch (setDefaultError: any) {
          console.log('设置默认连接失败，但连接已添加:', setDefaultError.message);
        }

        // 🎯 初始化本地账号存储
        try {
          console.log(`[${new Date().toLocaleString('zh-CN')}] 🎯 开始初始化本地账号存储:`, data.email);
          setActiveOnLogin(data.email);
          console.log(`[${new Date().toLocaleString('zh-CN')}] ✅ 本地账号存储已初始化:`, data.email);
        } catch (storageError: any) {
          console.warn('⚠️ 本地账号存储初始化失败:', storageError.message);
        }

        toast.success(t('auth.smtpImapLogin.connectionAdded'));
        // 🎯 根据模式决定跳转目标
        if (isAddConnectionMode) {
          navigate('/settings/connections'); // 添加连接模式：返回连接设置页面
        } else {
          navigate('/mail'); // 初始登录模式：进入邮件界面
        }
      } catch (connectionError: any) {
        console.error('添加连接失败:', connectionError);

        // 如果是重复连接错误，尝试找到现有连接并设为默认
        if (connectionError.message?.includes('duplicate key') ||
            connectionError.message?.includes('already exists')) {
          console.log('连接已存在，尝试设为默认连接');

          try {
            // 获取连接列表，找到对应的连接
            const connectionsResponse = await addImapSmtpConnection({
              provider: 'credential' as any,
              auth: {
                email: data.email,
                refreshToken: data.password,
              },
              isAddConnectionMode,
            });

            console.log('找到现有连接:', connectionsResponse.connectionId);

            if (connectionsResponse.connectionId) {
              await setDefaultConnection({ connectionId: connectionsResponse.connectionId });
              console.log('已将现有连接设为默认');
            }
          } catch (findError: any) {
            console.log('查找现有连接失败:', findError.message);
          }

          // 🎯 初始化本地账号存储（重复连接情况）
          try {
            setActiveOnLogin(data.email);
            console.log('✅ 本地账号存储已初始化（重复连接）:', data.email);
          } catch (storageError: any) {
            console.warn('⚠️ 本地账号存储初始化失败（重复连接）:', storageError.message);
          }

          toast.success(t('auth.smtpImapLogin.connectionAdded'));
          // 🎯 根据模式决定跳转目标
          if (isAddConnectionMode) {
            navigate('/settings/connections'); // 添加连接模式：返回连接设置页面
          } else {
            navigate('/mail'); // 初始登录模式：进入邮件界面
          }
        } else {
          toast.error(t('auth.smtpImapLogin.connectionAddFailed', { error: connectionError.message || 'Unknown error' }));
        }
      }
    } catch (error) {
      console.error('SMTP/IMAP登录失败:', error);
      toast.error(error instanceof Error
        ? t('auth.smtpImapLogin.loginFailed', { error: error.message })
        : t('auth.smtpImapLogin.loginFailed', { error: 'Unknown error' })
      );
    } finally {
      setIsSubmitting(false);
    }
  };



  // 获取邮箱域名对应的提示信息
  const getEmailTip = (email: string) => {
    if (email.includes('@163.com')) {
      return t('auth.smtpImapLogin.netease163EmailTip');
    }
    if (email.includes('@qq.com')) {
      return t('auth.smtpImapLogin.qqEmailTip');
    }
    if (email.includes('@icloud.com')) {
      return t('auth.smtpImapLogin.icloudEmailTip');
    }
    if (email.includes('@gmail.com')) {
      return t('auth.smtpImapLogin.gmailEmailTip');
    }
    return null;
  };

  return (
    <div className="w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="w-96 space-y-4">
        {/* 返回按钮 */}
        <div className="text-left">
          <Link
            to={isAddConnectionMode ? "/settings/connections" : "/login"}
            className="inline-flex items-center text-sm text-slate-400 hover:text-white transition-colors duration-200"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {isAddConnectionMode ? t('auth.smtpImapLogin.backToConnections') : t('auth.smtpImapLogin.backToLogin')}
          </Link>
        </div>

        {/* 主卡片 */}
        <Card className="border-slate-700 bg-slate-800/50 backdrop-blur-sm shadow-2xl">
          <CardHeader className="text-center space-y-2 pb-3">
            <div className="mx-auto w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <Mail className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-bold text-white">
                {isAddConnectionMode ? `添加 ${providerInfo.title.replace(' 邮箱登录', '')} 连接` : providerInfo.title}
              </CardTitle>
              <CardDescription className="text-slate-300 text-xs">
                {isAddConnectionMode
                  ? `添加新的 ${providerInfo.title.replace(' 邮箱登录', '')} 邮箱连接`
                  : providerInfo.description
                }
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="space-y-3 pt-0">
            <Form {...credentialsForm}>
              <form onSubmit={credentialsForm.handleSubmit(onCredentialsSubmit)} className="space-y-3">
                <FormField
                  control={credentialsForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 font-medium">
                        {t('auth.smtpImapLogin.emailAddress')}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            placeholder={providerInfo.placeholder}
                            {...field}
                            className="pl-10 bg-slate-700 border-slate-500 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={credentialsForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 font-medium">
                        {t('auth.smtpImapLogin.password')}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            type="password"
                            placeholder={t('auth.smtpImapLogin.passwordPlaceholder')}
                            {...field}
                            className="pl-10 bg-slate-700 border-slate-500 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-400" />
                      {getEmailTip(credentialsForm.watch('email') || '') && (
                        <div className="mt-1 p-2 bg-amber-500/10 border border-amber-500/20 rounded text-xs text-amber-400">
                          {getEmailTip(credentialsForm.watch('email') || '')}
                        </div>
                      )}
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-3 transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={isSubmitting}
                >
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isSubmitting
                    ? (isAddConnectionMode ? t('auth.smtpImapLogin.addingConnection') : t('auth.smtpImapLogin.loggingIn'))
                    : (isAddConnectionMode ? t('auth.smtpImapLogin.addConnectionButton') : t('auth.smtpImapLogin.loginButton'))
                  }
                </Button>
              </form>
            </Form>



          </CardContent>
        </Card>
      </div>
    </div>
  );
}
