import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Link, useNavigate, useSearchParams } from 'react-router';
import { z } from 'zod';
import { useState, useEffect } from 'react';
import { useTRPC } from '@/providers/query-provider';
import { useMutation } from '@tanstack/react-query';
import { Loader2, ArrowLeft, Mail } from 'lucide-react';
import { signIn, signUp } from '@/lib/auth-client';
import { useTranslations } from 'use-intl';
import { imapSmtpConfig } from '@/lib/constants';



export default function SmtpImapLoginPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [autoConfig, setAutoConfig] = useState(true);
  const [showManualConfig, setShowManualConfig] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const trpc = useTRPC();
  const t = useTranslations();

  // 🎯 随机选择背景图片
  const [backgroundImage] = useState(() => {
    const bgImages = ['bg0001.png', 'bg0002.png', 'bg0003.png', 'bg0004.png', 'bg0005.png', 'bg0006.png'];
    const randomIndex = Math.floor(Math.random() * bgImages.length);
    return `/bg-pics/${bgImages[randomIndex]}`;
  });

  // 🎯 检查是否为添加连接模式
  const isAddConnectionMode = searchParams.get('mode') === 'add-connection';

  // 🎯 获取邮箱提供商类型
  const provider = searchParams.get('provider') || 'other';

  // 🎯 根据提供商获取标题和描述
  const getProviderInfo = (provider: string) => {
    switch (provider) {
      case 'icloud':
        return {
          title: 'iCloud 邮箱登录',
          description: '使用您的 iCloud 邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      case 'onemails':
        return {
          title: 'OneMails 邮箱登录',
          description: '使用您的 OneMails 邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      case '163':
        return {
          title: '163 邮箱登录',
          description: '使用您的 163 邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      case 'qq':
        return {
          title: 'QQ 邮箱登录',
          description: '使用您的 QQ 邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      case 'aliyun':
        return {
          title: '阿里邮箱',
          description: '使用您的阿里邮箱账号登录',
          placeholder: '<EMAIL>'
        };
      default:
        return {
          title: '其它邮箱登录',
          description: '使用您的邮箱账号登录',
          placeholder: '<EMAIL>'
        };
    }
  };

  const providerInfo = getProviderInfo(provider);

  const credentialsSchema = z.object({
    email: z.string().email(t('auth.smtpImapLogin.invalidEmailFormat')),
    password: z.string().min(1, t('auth.smtpImapLogin.passwordRequired')),
  });

  const manualConfigSchema = z.object({
    imapHost: z.string().min(1, 'IMAP服务器地址不能为空'),
    imapPort: z.number().min(1).max(65535, '端口号必须在1-65535之间'),
    imapSecure: z.boolean(),
    smtpHost: z.string().min(1, 'SMTP服务器地址不能为空'),
    smtpPort: z.number().min(1).max(65535, '端口号必须在1-65535之间'),
    smtpSecure: z.boolean(),
  });

  type CredentialsValues = z.infer<typeof credentialsSchema>;
  type ManualConfigValues = z.infer<typeof manualConfigSchema>;

  const { mutateAsync: testConnection } = useMutation(
    trpc.connections.testConnectionPublic.mutationOptions(),
  );

  const { mutateAsync: addImapSmtpConnection } = useMutation(
    trpc.connections.addImapSmtpConnection.mutationOptions(),
  );



  // 注意：checkEmailExists 是 query，不是 mutation

  const { mutateAsync: setDefaultConnection } = useMutation(
    trpc.connections.setDefault.mutationOptions(),
  );

  const credentialsForm = useForm<CredentialsValues>({
    resolver: zodResolver(credentialsSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const manualConfigForm = useForm<ManualConfigValues>({
    resolver: zodResolver(manualConfigSchema),
    defaultValues: {
      imapHost: '',
      imapPort: 993,
      imapSecure: true,
      smtpHost: '',
      smtpPort: 465,
      smtpSecure: true,
    },
  });

  // 监听邮箱输入变化，自动预填充配置
  useEffect(() => {
    const subscription = credentialsForm.watch((value, { name }) => {
      if (name === 'email' && value.email && value.email.includes('@')) {
        if (autoConfig) {
          // 自动配置模式：使用内置配置（不使用本地配置）
          const builtinConfig = getBuiltinEmailConfig(value.email);
          if (builtinConfig) {
            console.log('自动预填充内置配置:', builtinConfig);
            manualConfigForm.setValue('imapHost', builtinConfig.imapHost || '');
            manualConfigForm.setValue('imapPort', builtinConfig.imapPort || 993);
            manualConfigForm.setValue('imapSecure', builtinConfig.imapSecure !== false);
            manualConfigForm.setValue('smtpHost', builtinConfig.smtpHost || '');
            manualConfigForm.setValue('smtpPort', builtinConfig.smtpPort || 465);
            manualConfigForm.setValue('smtpSecure', builtinConfig.smtpSecure !== false);
          }
        } else {
          // 关闭自动配置时：使用本地配置预填充
          const localConfig = getConfigFromLocal(value.email);
          if (localConfig) {
            console.log('自动预填充本地配置:', localConfig);
            manualConfigForm.setValue('imapHost', localConfig.imapHost || '');
            manualConfigForm.setValue('imapPort', localConfig.imapPort || 993);
            manualConfigForm.setValue('imapSecure', localConfig.imapSecure !== false);
            manualConfigForm.setValue('smtpHost', localConfig.smtpHost || '');
            manualConfigForm.setValue('smtpPort', localConfig.smtpPort || 465);
            manualConfigForm.setValue('smtpSecure', localConfig.smtpSecure !== false);
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [credentialsForm, manualConfigForm, autoConfig]);

  // 监听自动配置开关变化
  useEffect(() => {
    if (!autoConfig) {
      // 关闭自动配置时，清空手动配置表单的残留地址
      console.log('关闭自动配置，清空手动配置表单');
      manualConfigForm.reset({
        imapHost: '',
        imapPort: 993,
        imapSecure: true,
        smtpHost: '',
        smtpPort: 465,
        smtpSecure: true,
      });
    }
  }, [autoConfig, manualConfigForm]);

  const onCredentialsSubmit = async (data: CredentialsValues) => {
    setIsSubmitting(true);

    try {
      let config = null;

      if (autoConfig) {
        // 自动配置模式：使用原有流程（内置配置 → 网络）
        console.log('自动配置模式：使用原有配置流程');

        const domain = data.email.split('@')[1];

        // 1. 先从内置配置获取
        const configs: Record<string, any> = imapSmtpConfig;
        config = configs[domain];
        console.log('从内置配置获取:', config);

        // 2. 如果内置没有配置，从服务器获取
        if (!config) {
          console.log('内置配置未找到，尝试从服务器获取配置');
          try {
            const response = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_URL}/api/email-config/${domain}?email=${encodeURIComponent(data.email)}`);
            const serverResult = await response.json();

            if ((serverResult as any).success) {
              config = (serverResult as any).config;
              console.log(`从服务器获取配置成功 (来源: ${(serverResult as any).source}):`, config);
            } else {
              console.log('服务器也未找到配置:', (serverResult as any).message);
              toast.error('未找到邮箱配置，请检查邮箱域名或使用手动配置');
              return;
            }
          } catch (error) {
            console.error('从服务器获取配置失败:', error);
            toast.error('获取邮箱配置失败，请稍后重试或使用手动配置');
            return;
          }
        }
      } else {
        // 手动配置模式：先取本地存储，没有再找配置和网络
        console.log('手动配置模式：先本地，后配置和网络');

        // 1. 先从本地获取
        config = getConfigFromLocal(data.email);

        // 2. 如果本地没有，尝试内置配置和网络
        if (!config) {
          console.log('本地没有配置，尝试内置配置和网络');
          config = getBuiltinEmailConfig(data.email);

          // 如果内置也没有，从服务器获取
          if (!config) {
            try {
              const domain = data.email.split('@')[1];
              const response = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_URL}/api/email-config/${domain}?email=${encodeURIComponent(data.email)}`);
              const serverResult = await response.json();

              if ((serverResult as any).success) {
                config = (serverResult as any).config;
                console.log('从服务器获取配置成功:', config);
              }
            } catch (error) {
              console.error('从服务器获取配置失败:', error);
            }
          }
        }

        if (!config) {
          toast.error('未找到邮箱配置，请先进行手动配置');
          return;
        }

        console.log('使用配置:', config);
      }

      console.log('最终使用配置:', config);
      // 步骤2: 测试IMAP/SMTP连接
      console.log(t('auth.smtpImapLogin.testingConnection'));
      const connectionResult = await testConnection({
        email: data.email,
        password: data.password,
        imapHost: config.imapHost,
        imapPort: config.imapPort.toString(),
        imapSecure: config.imapSecure,
        smtpHost: config.smtpHost,
        smtpPort: config.smtpPort.toString(),
        smtpSecure: config.smtpSecure,
      });

      if (!connectionResult.success) {
        console.error('连接验证失败:', connectionResult);
        const errorMsg = connectionResult.imapTest?.error || connectionResult.smtpTest?.error || '连接验证失败';

        toast.error(t('auth.smtpImapLogin.connectionFailed', { error: errorMsg }));
        return;
      }
      console.log('✅ IMAP连接验证成功');
      toast.success(t('auth.smtpImapLogin.connectionSuccess'));

      // 保存配置到数据库
      try {
        const domain = data.email.split('@')[1];
        console.log(`保存 ${domain} 的配置到数据库`);

        const saveResponse = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_URL}/api/email-config`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // 包含认证信息
          body: JSON.stringify({
            domain,
            imapHost: config.imapHost,
            imapPort: config.imapPort,
            imapSecure: config.imapSecure,
            smtpHost: config.smtpHost,
            smtpPort: config.smtpPort,
            smtpSecure: config.smtpSecure,
          }),
        });

        const saveResult = await saveResponse.json();
        if ((saveResult as any).success) {
          console.log(`✅ ${domain} 配置已保存到数据库`);
        } else {
          console.log(`⚠️ 保存配置失败:`, (saveResult as any).error);
        }
      } catch (saveError) {
        console.error('保存配置到数据库失败:', saveError);
        // 不阻断登录流程，只记录错误
      }

      // 🎯 根据模式决定是否需要用户登录
      let loginSuccess = false;

      if (isAddConnectionMode) {
        // 添加连接模式：用户已登录，直接添加连接
        console.log('添加连接模式：用户已登录，直接添加IMAP连接');
        loginSuccess = true;
      } else {
        try {
          // 🎯 首先检查邮箱是否已存在于系统中
          console.log('检查邮箱是否已存在:', data.email);

          // 先尝试登录（可能用户已存在但API调用失败）
          const loginResult = await signIn.email({
            email: data.email,
            password: 'smtp-imap-user',
          });

          if (loginResult.error) {
            console.log('登录失败，尝试注册新用户:', loginResult.error);
            // 登录失败，尝试注册新用户
            await signUp.email({
              email: data.email,
              password: 'smtp-imap-user',
              name: data.email.split('@')[0] || 'SMTP User',
            });
          }

          loginSuccess = true;
        } catch (authError: any) {
          console.error('用户认证失败:', authError);
          loginSuccess = false;
        }
      }

      // 检查登录是否成功
      if (!loginSuccess) {
        toast.error(t('auth.smtpImapLogin.loginFailed', { error: 'Authentication failed' }));
        return;
      }

      console.log('用户认证成功，开始添加IMAP连接');

      // 登录成功后，添加SMTP/IMAP连接（服务器端会自动配置）
      try {
        const connectionResult = await addImapSmtpConnection({
          provider: 'credential' as any,
          auth: {
            email: data.email,
            refreshToken: data.password, // 存储实际的邮箱密码/授权码
            // 不传递 host 等参数，让服务器端自动配置
          },
          isAddConnectionMode,
        });

        console.log('连接添加成功，连接ID:', connectionResult.connectionId);

        // 强制设置为默认连接
        try {
          await setDefaultConnection({ connectionId: connectionResult.connectionId });
          console.log('已设置为默认连接');
        } catch (setDefaultError: any) {
          console.log('设置默认连接失败，但连接已添加:', setDefaultError.message);
        }

        // 如果使用的是本地手动配置，保存到数据库
        if (!autoConfig && config) {
          try {
            const domain = data.email.split('@')[1];
            console.log('保存手动配置到数据库:', domain);

            const saveResponse = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_URL}/api/email-config`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include',
              body: JSON.stringify({
                domain,
                imapHost: config.imapHost,
                imapPort: config.imapPort,
                imapSecure: config.imapSecure,
                smtpHost: config.smtpHost,
                smtpPort: config.smtpPort,
                smtpSecure: config.smtpSecure,
              }),
            });

            const saveResult = await saveResponse.json();
            if ((saveResult as any).success) {
              console.log('✅ 手动配置已保存到数据库');
            } else {
              console.log('⚠️ 保存手动配置到数据库失败:', (saveResult as any).error);
            }
          } catch (saveError) {
            console.error('保存手动配置到数据库失败:', saveError);
          }
        }

        toast.success(t('auth.smtpImapLogin.connectionAdded'));
        // 🎯 根据模式决定跳转目标
        if (isAddConnectionMode) {
          navigate('/settings/connections'); // 添加连接模式：返回连接设置页面
        } else {
          navigate('/mail'); // 初始登录模式：进入邮件界面
        }
      } catch (connectionError: any) {
        console.error('添加连接失败:', connectionError);

        // 如果是重复连接错误，尝试找到现有连接并设为默认
        if (connectionError.message?.includes('duplicate key') ||
            connectionError.message?.includes('already exists')) {
          console.log('连接已存在，尝试设为默认连接');

          try {
            // 获取连接列表，找到对应的连接
            const connectionsResponse = await addImapSmtpConnection({
              provider: 'credential' as any,
              auth: {
                email: data.email,
                refreshToken: data.password,
              },
              isAddConnectionMode,
            });

            console.log('找到现有连接:', connectionsResponse.connectionId);

            if (connectionsResponse.connectionId) {
              await setDefaultConnection({ connectionId: connectionsResponse.connectionId });
              console.log('已将现有连接设为默认');
            }
          } catch (findError: any) {
            console.log('查找现有连接失败:', findError.message);
          }

          toast.success(t('auth.smtpImapLogin.connectionAdded'));
          // 🎯 根据模式决定跳转目标
          if (isAddConnectionMode) {
            navigate('/settings/connections'); // 添加连接模式：返回连接设置页面
          } else {
            navigate('/mail'); // 初始登录模式：进入邮件界面
          }
        } else {
          toast.error(t('auth.smtpImapLogin.connectionAddFailed', { error: connectionError.message || 'Unknown error' }));
        }
      }
    } catch (error) {
      console.error('SMTP/IMAP登录失败:', error);
      toast.error(error instanceof Error
        ? t('auth.smtpImapLogin.loginFailed', { error: error.message })
        : t('auth.smtpImapLogin.loginFailed', { error: 'Unknown error' })
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // localStorage 操作工具函数
  const saveConfigToLocal = (email: string, config: ManualConfigValues) => {
    try {
      const domain = email.split('@')[1];
      const localConfigs = JSON.parse(localStorage.getItem('emailConfigs') || '{}');
      localConfigs[domain] = config;
      localStorage.setItem('emailConfigs', JSON.stringify(localConfigs));
      console.log('配置已保存到本地:', domain, config);
    } catch (error) {
      console.error('保存配置到本地失败:', error);
    }
  };

  const getConfigFromLocal = (email: string) => {
    try {
      const domain = email.split('@')[1];
      const localConfigs = JSON.parse(localStorage.getItem('emailConfigs') || '{}');
      return localConfigs[domain] || null;
    } catch (error) {
      console.error('从本地获取配置失败:', error);
      return null;
    }
  };

  // 获取内置邮件服务器配置
  const getBuiltinEmailConfig = (email: string) => {
    const domain = email.split('@')[1];
    const configs: Record<string, any> = imapSmtpConfig;
    const builtinConfig = configs[domain];
    if (builtinConfig) {
      console.log('从内置配置获取:', domain, builtinConfig);
      return builtinConfig;
    }
    return null;
  };



  // 手动配置按钮点击处理
  const handleManualConfigClick = async (e: React.MouseEvent) => {
    e.preventDefault(); // 阻止表单提交
    e.stopPropagation(); // 阻止事件冒泡

    const email = credentialsForm.getValues('email');

    if (!email || !email.includes('@')) {
      toast.error('请先输入邮箱地址');
      return;
    }

    // 尝试获取邮箱配置并填充到表单
    try {
      console.log('获取邮箱配置:', email);

      let config = null;

      if (autoConfig) {
        // 自动配置模式：内置配置 → 网络
        console.log('自动配置模式：使用内置配置');
        config = getBuiltinEmailConfig(email);

        // 如果内置没有配置，从服务器获取
        if (!config) {
          const domain = email.split('@')[1];
          const response = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_URL}/api/email-config/${domain}?email=${encodeURIComponent(email)}`);
          const serverResult = await response.json();

          if ((serverResult as any).success) {
            config = (serverResult as any).config;
            console.log('从服务器获取配置成功:', config);
          }
        }
      } else {
        // 手动配置模式：本地 → 网络
        console.log('手动配置模式：先本地后网络');
        config = getConfigFromLocal(email);

        // 如果本地没有配置，从网络获取
        if (!config) {
          const domain = email.split('@')[1];
          const response = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_URL}/api/email-config/${domain}?email=${encodeURIComponent(email)}`);
          const serverResult = await response.json();

          if ((serverResult as any).success) {
            config = (serverResult as any).config;
            console.log('从服务器获取配置成功:', config);
          }
        }
      }

      // 如果找到配置，填充到表单
      if (config) {
        manualConfigForm.setValue('imapHost', config.imapHost || '');
        manualConfigForm.setValue('imapPort', config.imapPort || 993);
        manualConfigForm.setValue('imapSecure', config.imapSecure !== false);
        manualConfigForm.setValue('smtpHost', config.smtpHost || '');
        manualConfigForm.setValue('smtpPort', config.smtpPort || 465);
        manualConfigForm.setValue('smtpSecure', config.smtpSecure !== false);

        const isLocal = !autoConfig && getConfigFromLocal(email);
        const source = isLocal ? '本地配置' : '系统配置';
        toast.success(`已自动填充${source}信息`);
      } else {
        toast.info('未找到该邮箱的配置信息，请手动输入');
      }
    } catch (error) {
      console.error('获取邮箱配置失败:', error);
      toast.info('获取配置失败，请手动输入配置信息');
    }

    setShowManualConfig(true);
  };

  // 手动配置提交函数
  const onManualConfigSubmit = async (data: ManualConfigValues) => {
    try {
      console.log('保存手动配置到本地...');

      const email = credentialsForm.getValues('email');

      // 保存配置到本地
      saveConfigToLocal(email, data);

      toast.success('配置已保存到本地！');
      setShowManualConfig(false);
      manualConfigForm.reset();
    } catch (error: any) {
      console.error('保存配置时出错:', error);
      toast.error(error.message || '保存失败');
    }
  };

  // 手动配置表单提交处理
  const handleManualConfigFormSubmit = (e: React.FormEvent) => {
    e.preventDefault(); // 阻止表单提交到外层表单
    e.stopPropagation(); // 阻止事件冒泡
    manualConfigForm.handleSubmit(onManualConfigSubmit)(e);
  };


  // 获取邮箱域名对应的提示信息
  const getEmailTip = (email: string) => {
    if (email.includes('@163.com')) {
      return t('auth.smtpImapLogin.netease163EmailTip');
    }
    if (email.includes('@qq.com')) {
      return t('auth.smtpImapLogin.qqEmailTip');
    }
    if (email.includes('@icloud.com')) {
      return t('auth.smtpImapLogin.icloudEmailTip');
    }
    if (email.includes('@gmail.com')) {
      return t('auth.smtpImapLogin.gmailEmailTip');
    }
    return null;
  };

  return (
    <div className="w-full h-screen bg-gray-100 flex items-center justify-center p-4 md:p-8">
      {/* 居中的圆角长方形框 - 缩小10% */}
      <div className="w-full max-w-6xl h-full max-h-[540px] bg-white rounded-3xl shadow-2xl overflow-hidden flex transform scale-90">
        {/* 左侧区域 - 背景图片，占1/3宽度，手机端隐藏 */}
        <div className="w-1/3 relative overflow-hidden hidden md:block">
          {/* 背景图片 */}
          <img
            src={backgroundImage}
            alt="Background"
            className="absolute inset-0 w-full h-full object-cover"
          />
          {/* 背景遮罩 */}
          <div className="absolute inset-0 bg-black/30"></div>

          {/* 返回按钮 - 圆形按钮在左上角 */}
          <div className="absolute top-6 left-6 z-20">
            <Link
              to={isAddConnectionMode ? "/settings/connections" : "/login"}
              className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors duration-200 backdrop-blur-sm"
            >
              <ArrowLeft className="h-5 w-5 text-white" />
            </Link>
          </div>

          {/* Logo */}
          <div className="absolute top-8 left-8 z-10 mt-12">
            <div className="flex items-center space-x-2 text-white">
              <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                <Mail className="h-5 w-5" />
              </div>
              <span className="text-xl font-bold">OneMails</span>
            </div>
          </div>

          {/* 引言内容 */}
          <div className="absolute bottom-0 left-0 right-0 p-8 z-10">
            {/* 引言 */}
            <div className="text-white">
              <blockquote className="text-lg font-light mb-4 leading-relaxed">
                {/* "Simply all the tools that<br />
                my team and I need." */}
              </blockquote>

              <div className="text-xs opacity-80">
                {/* <div className="font-medium">Karen Yue</div> */}
                {/* <div>Director of Digital Marketing Technology</div> */}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧区域 - 登录表单，占2/3宽度，手机端占全宽 */}
        <div className="w-full md:w-2/3 bg-white flex items-center justify-center p-6 relative">
          {/* 手机端返回按钮 */}
          <div className="absolute top-6 left-6 z-20 md:hidden">
            <Link
              to={isAddConnectionMode ? "/settings/connections" : "/login"}
              className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </Link>
          </div>

          <div className="w-full max-w-sm space-y-5 transform scale-90">

          {/* 标题 */}
          <div className="text-center space-y-2">
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back to OneMails
            </h1>
            <p className="text-sm text-gray-600">
              Build your design system effortlessly with our<br />
              powerful component library.
            </p>
          </div>

          {/* 登录表单 */}
          <div className="space-y-3">
            <Form {...credentialsForm}>
              <form onSubmit={credentialsForm.handleSubmit(onCredentialsSubmit)} className="space-y-3">
                <FormField
                  control={credentialsForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <Input
                            placeholder={providerInfo.placeholder}
                            {...field}
                            className="h-14 bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder:text-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 rounded-2xl text-sm px-4 pt-6 pb-2 transition-all duration-200"
                          />
                          <label className="absolute left-4 top-2 text-xs text-gray-500 font-medium">
                            Email
                          </label>
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={credentialsForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="password"
                            placeholder="••••••••••"
                            {...field}
                            className="h-14 bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder:text-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 rounded-2xl text-sm px-4 pt-6 pb-2 transition-all duration-200"
                          />
                          <label className="absolute left-4 top-2 text-xs text-gray-500 font-medium">
                            Password
                          </label>
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500" />
                      {getEmailTip(credentialsForm.watch('email') || '') && (
                        <div className="mt-1 p-2 bg-amber-50 border border-amber-200 rounded-lg text-xs text-amber-700">
                          {getEmailTip(credentialsForm.watch('email') || '')}
                        </div>
                      )}
                    </FormItem>
                  )}
                />

                {/* 自动配置和手动设置 */}
                <div className="flex items-center justify-between py-2">
                  <div className="flex-1 min-h-[20px]">
                    {!autoConfig && (
                      <>
                        <button
                          type="button"
                          className="text-xs text-blue-600 hover:text-blue-700 underline"
                          onClick={handleManualConfigClick}
                        >
                          手动配置邮箱
                        </button>
                        <Dialog open={showManualConfig} onOpenChange={setShowManualConfig}>
                          <DialogContent
                            showOverlay={true}
                            className="max-w-[90vw] sm:max-w-md bg-white border-0 rounded-2xl shadow-2xl p-4 sm:p-6 max-h-[90vh] overflow-y-auto"
                          >
                      <DialogHeader className="mb-4">
                        <DialogTitle className="text-gray-800 text-lg">手动配置邮箱</DialogTitle>
                      </DialogHeader>
                      <Form {...manualConfigForm}>
                        <form onSubmit={handleManualConfigFormSubmit} className="space-y-4">
                          {/* IMAP 标签和配置行 */}
                          <div className="space-y-2">
                            <div className="text-xs font-medium text-gray-700">IMAP 服务器</div>
                            <div className="flex items-center gap-3">
                            <FormField
                              control={manualConfigForm.control}
                              name="imapHost"
                              render={({ field }) => (
                                <FormItem className="flex-1">
                                  <FormControl>
                                    <Input
                                      placeholder="imap.example.com"
                                      {...field}
                                      className="h-9 bg-gray-50 border-gray-200 focus:bg-white text-sm"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <div className="flex items-baseline gap-3">
                              <FormField
                                control={manualConfigForm.control}
                                name="imapSecure"
                                render={({ field }) => (
                                  <FormItem className="flex items-center gap-2">
                                    <FormControl>
                                      <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                        className="data-[state=checked]:bg-purple-600 scale-90"
                                      />
                                    </FormControl>
                                    <span className="text-xs text-gray-600 whitespace-nowrap -translate-y-0.5">SSL</span>
                                  </FormItem>
                                )}
                              />

                              <span className="text-xs text-gray-600 whitespace-nowrap">端口:</span>

                              <FormField
                                control={manualConfigForm.control}
                                name="imapPort"
                                render={({ field }) => (
                                  <FormItem className="w-16">
                                    <FormControl>
                                      <Input
                                        type="text"
                                        inputMode="numeric"
                                        pattern="[0-9]*"
                                        placeholder="993"
                                        {...field}
                                        onChange={(e) => {
                                          const value = e.target.value.replace(/[^0-9]/g, '');
                                          field.onChange(parseInt(value) || 993);
                                        }}
                                        className="h-9 bg-gray-50 border-gray-200 focus:bg-white text-sm text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                            </div>
                            </div>
                          </div>

                          {/* SMTP 标签和配置行 */}
                          <div className="space-y-2">
                            <div className="text-xs font-medium text-gray-700">SMTP 服务器</div>
                            <div className="flex items-center gap-3">
                            <FormField
                              control={manualConfigForm.control}
                              name="smtpHost"
                              render={({ field }) => (
                                <FormItem className="flex-1">
                                  <FormControl>
                                    <Input
                                      placeholder="smtp.example.com"
                                      {...field}
                                      className="h-9 bg-gray-50 border-gray-200 focus:bg-white text-sm"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <div className="flex items-baseline gap-3">
                              <FormField
                                control={manualConfigForm.control}
                                name="smtpSecure"
                                render={({ field }) => (
                                  <FormItem className="flex items-center gap-2">
                                    <FormControl>
                                      <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                        className="data-[state=checked]:bg-purple-600 scale-90"
                                      />
                                    </FormControl>
                                    <span className="text-xs text-gray-600 whitespace-nowrap -translate-y-0.5">SSL</span>
                                  </FormItem>
                                )}
                              />

                              <span className="text-xs text-gray-600 whitespace-nowrap">端口:</span>

                              <FormField
                                control={manualConfigForm.control}
                                name="smtpPort"
                                render={({ field }) => (
                                  <FormItem className="w-16">
                                    <FormControl>
                                      <Input
                                        type="text"
                                        inputMode="numeric"
                                        pattern="[0-9]*"
                                        placeholder="465"
                                        {...field}
                                        onChange={(e) => {
                                          const value = e.target.value.replace(/[^0-9]/g, '');
                                          field.onChange(parseInt(value) || 465);
                                        }}
                                        className="h-9 bg-gray-50 border-gray-200 focus:bg-white text-sm text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                            </div>
                            </div>
                          </div>

                          {/* 提交按钮 */}
                          <div className="flex gap-4 pt-4">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setShowManualConfig(false)}
                              className="flex-1 h-10 text-sm"
                            >
                              取消
                            </Button>
                            <Button
                              type="submit"
                              className="flex-1 h-10 bg-purple-600 hover:bg-purple-700 text-sm"
                            >
                              保存配置
                            </Button>
                          </div>
                        </form>
                      </Form>
                        </DialogContent>
                        </Dialog>
                      </>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 ml-auto">
                    <label htmlFor="autoConfig" className="text-xs text-gray-600">
                      Auto configure
                    </label>
                    <Switch
                      id="autoConfig"
                      checked={autoConfig}
                      onCheckedChange={setAutoConfig}
                      className="data-[state=checked]:bg-purple-600 scale-125"
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-10 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-2xl transition-colors duration-200 text-sm"
                  disabled={isSubmitting}
                >
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isSubmitting
                    ? (isAddConnectionMode ? t('auth.smtpImapLogin.addingConnection') : 'Logging in...')
                    : (isAddConnectionMode ? t('auth.smtpImapLogin.addConnectionButton') : 'Login')
                  }
                </Button>
              </form>
            </Form>
          </div>
          </div>
        </div>
      </div>
    </div>
  );
}
