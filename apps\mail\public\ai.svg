<svg width="191" height="191" viewBox="0 0 191 191" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="pastelGradient" x1="35%" y1="35%" x2="65%" y2="65%">
      <stop offset="0%" stop-color="#ffd5d0" stop-opacity="1" />
      <stop offset="50%" stop-color="#dbffe4" stop-opacity="1" />
      <stop offset="100%" stop-color="#e2d6ff" stop-opacity="1" />
      <animate attributeName="x1" values="35%;40%;35%" dur="18s" repeatCount="indefinite" />
      <animate attributeName="x2" values="65%;60%;65%" dur="18s" repeatCount="indefinite" />
      <animate attributeName="y1" values="35%;40%;35%" dur="20s" repeatCount="indefinite" />
      <animate attributeName="y2" values="65%;60%;65%" dur="20s" repeatCount="indefinite" />
    </linearGradient>
    
    <!-- Enhanced glow -->
    <filter id="softGlow" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur stdDeviation="8" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- Outer shine halo -->
    <radialGradient id="haloGradient" cx="50%" cy="50%" r="70%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#ffd5d0" stop-opacity="0" />
      <stop offset="80%" stop-color="#dbffe4" stop-opacity="0.1" />
      <stop offset="100%" stop-color="#e2d6ff" stop-opacity="0.3" />
    </radialGradient>
  </defs>
  
  <!-- Outer halo -->
  <circle cx="95" cy="95" r="90" fill="url(#haloGradient)" opacity="0.6">
    <animate attributeName="opacity" values="0.5;0.7;0.5" dur="6s" repeatCount="indefinite" />
  </circle>
  
  <!-- Base with enhanced glow -->
  <path d="M38.125 190.625V152.5H0V38.125H38.125V0H152.5V38.125H190.625V152.5H152.5V190.625H38.125ZM38.125 114.375H76.25V150.975H152.5V76.25H114.375V114.375H76.25V76.25H114.375V39.65H38.125V114.375Z" fill="url(#pastelGradient)" filter="url(#softGlow)" />
  
  <!-- Enhanced edge highlight -->
  <path d="M38.125 190.625V152.5H0V38.125H38.125V0H152.5V38.125H190.625V152.5H152.5V190.625H38.125ZM38.125 114.375H76.25V150.975H152.5V76.25H114.375V114.375H76.25V76.25H114.375V39.65H38.125V114.375Z" stroke="white" stroke-width="1.2" fill="none" opacity="0.7">
    <animate attributeName="stroke-opacity" values="0.6;0.8;0.6" dur="5s" repeatCount="indefinite" />
    <animate attributeName="stroke-width" values="1.0;1.4;1.0" dur="6s" repeatCount="indefinite" />
  </path>
</svg>
