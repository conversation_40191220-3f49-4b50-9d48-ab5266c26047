import {
  defaultUserSettings,
  userSettingsSchema,
  type UserSettings,
} from '@zero/db/user_settings_default';
import { createRateLimiterMiddleware, privateProcedure, router, publicProcedure } from '../trpc';
import { Ratelimit } from '@upstash/ratelimit';
import { userSettings } from '@zero/db/schema';
import { eq } from 'drizzle-orm';
import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { readErrorLogs, getErrorLogStats, getErrorLogDates } from '../../lib/error-logger';

export const settingsRouter = router({
  get: privateProcedure
    .use(
      createRateLimiterMiddleware({
        limiter: Ratelimit.slidingWindow(60, '1m'),
        generatePrefix: ({ session }) => `ratelimit:get-settings-${session?.user.id}`,
      }),
    )
    .query(async ({ ctx }) => {
      const { db, session } = ctx;
      const [result] = await db
        .select()
        .from(userSettings)
        .where(eq(userSettings.userId, session.user.id))
        .limit(1);

      // Returning null here when there are no settings so we can use the default settings with timezone from the browser
      if (!result) return { settings: defaultUserSettings };

      const settingsRes = userSettingsSchema.safeParse(result.settings);
      if (!settingsRes.success) {
        db.update(userSettings)
          .set({
            settings: defaultUserSettings,
            updatedAt: new Date(),
          })
          .where(eq(userSettings.userId, session.user.id));
        console.log('returning default settings');
        return { settings: defaultUserSettings };
      }

      return { settings: settingsRes.data };
    }),

  save: privateProcedure.input(userSettingsSchema.partial()).mutation(async ({ ctx, input }) => {
    const { db, session } = ctx;
    const timestamp = new Date();

    const [existingSettings] = await db
      .select()
      .from(userSettings)
      .where(eq(userSettings.userId, session.user.id))
      .limit(1);

    if (existingSettings) {
      const newSettings = { ...(existingSettings.settings as UserSettings), ...input };
      await db
        .update(userSettings)
        .set({
          settings: newSettings,
          updatedAt: timestamp,
        })
        .where(eq(userSettings.userId, session.user.id));
    } else {
      await db.insert(userSettings).values({
        id: crypto.randomUUID(),
        userId: session.user.id,
        settings: { ...defaultUserSettings, ...input },
        createdAt: timestamp,
        updatedAt: timestamp,
      });
    }

    return { success: true };
  }),

  // 错误日志相关API
  getErrorLogs: publicProcedure
    .input(z.object({
      errorCode: z.string(),
      date: z.string().optional()
    }))
    .query(async ({ input }) => {
      // 验证错误代码
      const expectedErrorCode = process.env['EMAIL_ERR_CODE'];
      if (!expectedErrorCode || input.errorCode !== expectedErrorCode) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '无效的错误代码'
        });
      }

      try {
        const logs = await readErrorLogs(input.date);
        return { success: true, logs };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '读取错误日志失败'
        });
      }
    }),

  getErrorLogStats: publicProcedure
    .input(z.object({
      errorCode: z.string(),
      date: z.string().optional()
    }))
    .query(async ({ input }) => {
      // 验证错误代码
      const expectedErrorCode = process.env['EMAIL_ERR_CODE'];
      if (!expectedErrorCode || input.errorCode !== expectedErrorCode) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '无效的错误代码'
        });
      }

      try {
        const stats = await getErrorLogStats(input.date);
        return { success: true, stats };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取错误统计失败'
        });
      }
    }),

  getErrorLogDates: publicProcedure
    .input(z.object({
      errorCode: z.string()
    }))
    .query(async ({ input }) => {
      // 验证错误代码
      const expectedErrorCode = process.env['EMAIL_ERR_CODE'];
      if (!expectedErrorCode || input.errorCode !== expectedErrorCode) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '无效的错误代码'
        });
      }

      try {
        const dates = await getErrorLogDates();
        return { success: true, dates };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取日期列表失败'
        });
      }
    }),
});
