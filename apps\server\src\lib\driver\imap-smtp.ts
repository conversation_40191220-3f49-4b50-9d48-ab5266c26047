import { deleteActiveConnection, FatalErrors, sanitizeContext, StandardizedError } from './utils';
import type { IGetThreadResponse, MailManager, ManagerConfig, ParsedDraft } from './types';
import type { Attachment, IOutgoingMessage, Label, ParsedMessage } from '../../types';
import { sanitizeTipTapHtml } from '../sanitize-tip-tap-html';
import { simpleParser, type AddressObject } from 'mailparser';
import type { CreateDraftData } from '../schemas';
import { createMimeMessage } from 'mimetext';
import * as nodemailer from 'nodemailer';
import { ImapFlow } from 'imapflow';
import { v4 as uuidv4 } from 'uuid';
// email-folder-variation.json文件是邮件提供商的邮箱别名，使用邮箱@之后的域名区分
// standardFolders是系统默认邮箱，分为五个基本邮箱（Basic）和其他默认邮箱（More）
// 除standardFolders以外的其他邮箱是用户自定义邮箱
import emailFolderVariationJson from './email-folder-variation.json';
import { handleEmailByAiLabel } from '../ai-label-handler';
// import { Queue } from 'bullmq';
import { getContext } from 'hono/context-storage';
import type { HonoContext } from '../../ctx';
import { aiLabelEmail } from '@zero/db/schema';
import { eq, and, count, desc, sql } from 'drizzle-orm';
import { imapSmtpConfig } from './config-imap-smtp';
import { handleEmailByAiSummary } from '../ai-summary-handler';
import { decrypt } from '../hash-utils';

type EmailFolderVariation = {
  [key: string]: {
    standardFolders: {
      Basic: {
        inbox: {
          defaultName: string,
          variation: string[]
        },
        sent: {
          defaultName: string,
          variation: string[]
        },
        drafts: {
          defaultName: string,
          variation: string[]
        },
        trash: {
          defaultName: string,
          variation: string[]
        },
        junk: {
          defaultName: string,
          variation: string[]
        },
      },
      More: {
        [key: string]: {
          defaultName: string,
          variation: string[]
        }
      }
    }
  }
}

const emailFolderVariation: EmailFolderVariation = emailFolderVariationJson;

//console.log("debug emailFolderVariation:",emailFolderVariation);

// IMAP连接池接口
interface ImapConnection {
  client: ImapFlow;
  lastUsed: number;
  inUse: boolean;
  email: string;
}

// IMAP连接池类
export class ImapConnectionPool {
  private connections: Map<string, ImapConnection[]> = new Map();
  private maxConnectionsPerUser = 3; // 每个用户最多3个连接
  private connectionTimeout = 5 * 60 * 1000; // 5分钟超时
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // 定期清理过期连接
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredConnections();
    }, 60 * 1000); // 每分钟清理一次
  }

  // 获取连接
  async getConnection(email: string, config: any): Promise<ImapConnection> {
    const userConnections = this.connections.get(email) || [];

    // 查找可用的连接
    for (const conn of userConnections) {
      if (!conn.inUse && this.isConnectionValid(conn)) {
        conn.inUse = true;
        conn.lastUsed = Date.now();
        logWithTime(`🔄 复用IMAP连接: ${email}`);
        return conn;
      }
    }

    // 如果没有可用连接，创建新连接
    if (userConnections.length < this.maxConnectionsPerUser) {
      const newConnection = await this.createConnection(email, config);
      userConnections.push(newConnection);
      this.connections.set(email, userConnections);
      logWithTime(`🆕 创建新IMAP连接: ${email} (总数: ${userConnections.length})`);
      return newConnection;
    }

    // 如果达到最大连接数，等待可用连接
    logWithTime(`⏳ 等待IMAP连接可用: ${email} (当前连接数: ${userConnections.length}/${this.maxConnectionsPerUser})`);
    logWithTime(`连接状态: ${userConnections.map((conn, i) => `[${i}] inUse:${conn.inUse} valid:${this.isConnectionValid(conn)}`).join(', ')}`);
    return this.waitForAvailableConnection(email, config);
  }

  // 释放连接
  releaseConnection(connection: ImapConnection): void {
    connection.inUse = false;
    connection.lastUsed = Date.now();
    logWithTime(`🔓 释放IMAP连接: ${connection.email}`);
  }
  private async closeImapConnection(): Promise<void> {
    
  }

  // 创建新连接
  private async createConnection(email: string, config: any): Promise<ImapConnection> {
    const { host, port, secure, user, pass } = this.parseConnectionInfo(config);

    const client = new ImapFlow({
      host,
      port,
      secure,
      auth: { user, pass },
      logger: false,
    });

    await client.connect();

    return {
      client,
      lastUsed: Date.now(),
      inUse: true,
      email,
    };
  }

  // 解析连接信息
  private parseConnectionInfo(config: any) {
    // 根据邮箱域名自动配置IMAP设置
    const email = config.email;
    let host = 'imap.mail.me.com';
    let port = 993;
    let secure = true;

    const emailDomain = email.split('@')[1];
    host = imapSmtpConfig[emailDomain] ? imapSmtpConfig[emailDomain].imapHost : `imap.${emailDomain}`;
    port = imapSmtpConfig[emailDomain] ? imapSmtpConfig[emailDomain].imapPort : 993;
    // if (email.includes('@163.com')) {
    //   host = 'imap.163.com';
    //   port = 993;
    // } else if (email.includes('@qq.com')) {
    //   host = 'imap.qq.com';
    //   port = 993;
    // } else if (email.includes('@gmail.com')) {
    //   host = 'imap.gmail.com';
    //   port = 993;
    // } else if (email.includes('@icloud.com')) {
    //   host = 'imap.mail.me.com';
    //   port = 993;
    // } else if (email.includes('@onemails.ai')) {
    //   host = 'imap.onemails.ai';
    //   port = 993;
    // } 

    return {
      host,
      port,
      secure,
      user: email,
      pass: config.password
    };
  }

  // 检查连接是否有效 (公共方法)
  isConnectionValid(connection: ImapConnection): boolean {
    try {
      return connection.client.usable &&
             (Date.now() - connection.lastUsed) < this.connectionTimeout;
    } catch {
      return false;
    }
  }

  // 等待可用连接
  private async waitForAvailableConnection(email: string, config: any): Promise<ImapConnection> {
    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(() => {
        const userConnections = this.connections.get(email) || [];
        for (const conn of userConnections) {
          if (!conn.inUse && this.isConnectionValid(conn)) {
            clearInterval(checkInterval);
            conn.inUse = true;
            conn.lastUsed = Date.now();
            resolve(conn);
            return;
          }
        }
      }, 100);

      // 30秒超时 - 增加超时时间以避免频繁超时
      setTimeout(() => {
        clearInterval(checkInterval);
        reject(new Error(`⏰ 等待IMAP连接超时 - ${new Date().toISOString()}`));
      }, 30000);
    });
  }

  // 清理过期连接
  private cleanupExpiredConnections(): void {
    for (const [email, connections] of this.connections.entries()) {
      const validConnections = connections.filter(conn => {
        if (!conn.inUse && !this.isConnectionValid(conn)) {
          try {
            conn.client.close();
            logWithTime(`🗑️ 清理过期IMAP连接: ${email}`);
          } catch (error) {
            logWithTime(`清理连接时出错: ${error}`);
          }
          return false;
        }
        return true;
      });

      if (validConnections.length === 0) {
        this.connections.delete(email);
      } else {
        this.connections.set(email, validConnections);
      }
    }
  }

  // 清理特定用户的连接
  clearUserConnections(email: string): void {
    const userConnections = this.connections.get(email);
    if (userConnections) {
      logWithTime(`🧹 清理用户连接池: ${email} (${userConnections.length}个连接)`);
      for (const conn of userConnections) {
        try {
          conn.client.close();
        } catch (error) {
          logWithTime(`关闭连接时出错: ${error}`);
        }
      }
      this.connections.delete(email);
    }
  }

  // 清理所有连接
  clearAllConnections(): void {
    logWithTime(`🧹 清理所有IMAP连接池 (${this.connections.size}个用户)`);
    for (const [email, connections] of this.connections.entries()) {
      for (const conn of connections) {
        try {
          conn.client.close();
        } catch (error) {
          logWithTime(`关闭连接时出错: ${error}`);
        }
      }
    }
    this.connections.clear();
  }

  // 🚨 关键修复：获取全局连接池实例用于清理
  static getGlobalPool(): ImapConnectionPool {
    return imapPool;
  }

  // 🚨 关键修复：静态方法直接清理全局连接池
  static clearGlobalPool(): void {
    imapPool.clearAllConnections();
  }

  // 销毁连接池
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clearAllConnections();
  }
}

// 全局连接池实例
const imapPool = new ImapConnectionPool();

// 带时间戳的日志函数
const logWithTime2 = (message: string, ...args: any[]) => {
  
  console.log(` ${message}`, ...args);
};
const logWithTime = (message: string, ...args: any[]) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`, ...args);
};


type ExtendedAuthConfig = {
  accessToken: string;
  refreshToken: string;
  email: string;
  host?: string;
  port?: number;
  secure?: boolean;
  smtpHost?: string;
  smtpPort?: number;
  smtpSecure?: boolean;
};

type ExtendedManagerConfig = {
  auth: ExtendedAuthConfig;
  c?: any;
};

export class ImapSmtpMailManager implements MailManager {
  public config: ManagerConfig;
  private currentConnection: ImapConnection | null = null; // 当前使用的连接
  private smtpTransport: nodemailer.Transporter;
  private credentials: { email: string; password: string } | null = null;
  // private standardFolders: { [key: string]: string } = {
  //   Inbox: 'INBOX',
  //   Sent: 'Sent',
  //   Drafts: 'Drafts',
  //   Trash: 'Trash',
  //   Junk: 'Junk',
  //   Archive: 'Archive',
  // };
  private userLabels: Label[] = [];

/**
 * 缓存邮件数据，避免重复获取
 */
  private threadsCache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_TTL = 30 * 1000; // 30秒缓存

  constructor(config: ManagerConfig) {
    this.config = config;

    if (!this.config.auth.accessToken) this.config.auth.accessToken = '';
    if (!this.config.auth.refreshToken) this.config.auth.refreshToken = '';

    // 解析连接信息
    const { user, pass } = this.parseConnectionInfo(
      config as ExtendedManagerConfig,
    );

    // 初始化SMTP传输器
    const { smtpHost, smtpPort, smtpSecure } = this.parseSmtpInfo(
      config as ExtendedManagerConfig,
    );

    // 为QQ和163邮箱配置特殊的TLS设置
    const transportConfig: any = {
      host: smtpHost,
      port: smtpPort,
      secure: smtpSecure,
      auth: {
        user,
        pass,
      },
    };

    // QQ邮箱配置 - QQ邮箱工作正常，保持简单配置
    if (user.includes('@qq.com') && smtpPort === 465 && smtpSecure) {
      transportConfig.tls = {
        minVersion: 'TLSv1.2',
        rejectUnauthorized: false,
      };
    }

    // 163邮箱配置 - 单独处理，支持多个端口
    if (user.includes('@163.com')) {
      if ((smtpPort === 465 || smtpPort === 994) && smtpSecure) {
        // 465端口或994端口SSL连接
        transportConfig.tls = {
          minVersion: 'TLSv1.2',
          rejectUnauthorized: false,
        };
      } else if (smtpPort === 587 && !smtpSecure) {
        // 587端口STARTTLS连接
        transportConfig.tls = {
          minVersion: 'TLSv1.2',
          rejectUnauthorized: false,
        };
      }

      // 163邮箱需要更长的超时时间
      transportConfig.connectionTimeout = 60000;
      transportConfig.greetingTimeout = 60000;
      transportConfig.socketTimeout = 60000;
    }

    this.smtpTransport = nodemailer.createTransport(transportConfig);

    // 保存认证信息，用于备用
    this.credentials = {
      email: user,
      password: pass,
    };

    logWithTime(`🚀 初始化IMAP-SMTP管理器，邮箱: ${user}`);
    logWithTime(`📧 SMTP: ${smtpHost}:${smtpPort} (secure: ${smtpSecure})`);
    logWithTime(`🔗 将使用连接池管理IMAP连接`);
  }

  private parseConnectionInfo(config: ExtendedManagerConfig) {
    // 根据邮箱域名自动配置IMAP设置
    const email = config.auth.email;
    const domain = email.split('@')[1] || '';

    let originalHost = config.auth.host || '';
    let port = config.auth.port || 993;
    let secure = config.auth.secure !== undefined ? config.auth.secure : true;

    // 如果没有提供host，根据邮箱域名自动配置
    if (!originalHost && domain) {
      const autoConfig = this.getAutoConfig(domain);
      originalHost = autoConfig.imapHost;
      port = autoConfig.imapPort;
      secure = autoConfig.imapSecure;
    }

    // 尝试使用域名而不是IP地址，因为Cloudflare Workers可能无法连接到某些IP
    const host = originalHost;
    logWithTime(`[IMAP] Host mapping: ${originalHost} -> ${host}`);

    // For IMAP, use email as username and refreshToken as password
    const user = config.auth.email;
    const pass = decrypt(config.auth.refreshToken); // Using refreshToken field as password

    logWithTime(`[IMAP] Auth info: user=${user}, pass=${pass ? '***' + pass.slice(-4) : 'MISSING'}`);

    return { host, port, secure, user, pass };
  }
    // 获取邮箱类型
  private getEmailProvider(): 'qq' | '163' | 'icloud' | 'aliyun' | 'other' {
    const email = this.credentials?.email || this.config.auth?.email || '';
    const domain = email.split('@')[1]?.toLowerCase();
    if (domain === 'qq.com') {
      return 'qq';
    } else if (domain === '163.com') {
      return '163';
    } else if (domain === 'icloud.com') {
      return 'icloud';
    } else if (domain === 'aihubs.cn') {
      return 'aliyun';
    } else {
      return 'other';
    }
  }

  // 根据邮箱域名自动配置IMAP/SMTP设置
  private getAutoConfig(domain: string) {
    const configs: Record<string, {
      imapHost: string;
      imapPort: number;
      imapSecure: boolean;
      smtpHost: string;
      smtpPort: number;
      smtpSecure: boolean;
    }> = imapSmtpConfig;

    return configs[domain] || {
      imapHost: `imap.${domain}`,
      imapPort: 993,
      imapSecure: true,
      smtpHost: `smtp.${domain}`,
      smtpPort: 465,
      smtpSecure: true,
    };
  }

  private parseSmtpInfo(config: ExtendedManagerConfig) {
    // 根据邮箱域名自动配置SMTP设置
    const email = config.auth.email;
    const domain = email.split('@')[1] || '';

    let originalSmtpHost = config.auth.smtpHost || config.auth.host || '';
    let smtpPort = config.auth.smtpPort || this.getSmtpPort(config.auth.port || 993);
    let smtpSecure = config.auth.smtpSecure !== undefined
      ? config.auth.smtpSecure
      : this.getSmtpSecure(config.auth.port || 993);

    // 如果没有提供SMTP host，根据邮箱域名自动配置
    if (!originalSmtpHost && domain) {
      const autoConfig = this.getAutoConfig(domain);
      originalSmtpHost = autoConfig.smtpHost;
      smtpPort = autoConfig.smtpPort;
      smtpSecure = autoConfig.smtpSecure;
    }

    // 尝试使用域名而不是IP地址，因为Cloudflare Workers可能无法连接到某些IP
    const smtpHost =  originalSmtpHost;
    logWithTime(`[SMTP] Host mapping: ${originalSmtpHost} -> ${smtpHost}`);

    return { smtpHost, smtpPort, smtpSecure };
  }

  private getSmtpPort(imapPort: number): number {
    // Common SMTP ports based on IMAP ports
    switch (imapPort) {
      case 993:
        return 465; // Secure IMAP -> Secure SMTP
      case 143:
        return 587; // Non-secure IMAP -> TLS SMTP
      default:
        return 587; // Default to TLS port
    }
  }

  private getSmtpSecure(imapPort: number): boolean {
    // Determine if SMTP should use SSL/TLS
    return imapPort === 993;
  }

  public getScope(): string {
    // IMAP/SMTP doesn't use OAuth scopes
    return 'imap smtp';
  }

  // Connection management methods - 使用连接池
  private async ensureImapConnection(): Promise<ImapFlow> {
    if (!this.currentConnection || !imapPool.isConnectionValid(this.currentConnection)) {
      // 如果有旧连接，先释放
      if (this.currentConnection) {
        imapPool.releaseConnection(this.currentConnection);
      }

      // 从连接池获取新连接
      this.currentConnection = await imapPool.getConnection(
        this.credentials?.email || '',
        {
          email: this.credentials?.email || '',
          password: this.credentials?.password || ''
        }
      );
    }

    return this.currentConnection.client;
  }

  // 释放当前连接
  private releaseCurrentConnection(): void {
    if (this.currentConnection) {
      imapPool.releaseConnection(this.currentConnection);
      this.currentConnection = null;
    }
  }

  // 🚨 关键修复：清理连接池方法
  public clearConnectionPool(): void {
    const email = this.credentials?.email;
    if (email) {
      logWithTime(`🧹 清理IMAP连接池: ${email}`);
      imapPool.clearUserConnections(email);
      // 释放当前连接
      this.releaseCurrentConnection();
    }
  }

  // 清理资源
  async cleanup(): Promise<void> {
    this.releaseCurrentConnection();

    // 关闭SMTP连接
    if (this.smtpTransport) {
      this.smtpTransport.close();
    }
  }



  // Test method to verify connection
  public async testConnection(): Promise<{ imap: boolean; smtp: boolean }> {
    try {
      // Test IMAP connection
      const imapClient  = await this.ensureImapConnection();
      const imapSuccess = imapClient.usable;
      // Test SMTP connection
      let smtpSuccess = false;
      try {
        await this.smtpTransport.verify();
        smtpSuccess = true;
      } catch (error) {
        console.error('SMTP verification failed:', error);
      }
      return { imap: imapSuccess, smtp: smtpSuccess };
    } catch (error) {
      console.error('Connection test failed:', error);
      return { imap: false, smtp: false };
    }
  }

  // Folder mapping methods
  private async findFolderNameVariation(variations: string[]): Promise<string | null> {
      try {
        const imapClient = await this.ensureImapConnection();
        const mailboxes = await imapClient.list();
  
        for (const variation of variations) {
          const match = mailboxes.find(
            (box) =>
              box.name === variation || box.path === variation || box.path.endsWith(`/${variation}`),
          );
  
          if (match) return match.path;
        }
  
        return null;
      } catch (error) {
        console.error('Error finding folder variation:', error);
        return null;
      }
    }
  
    // Folder mapping methods
    private async getImapFolderPath(folder: string): Promise<string> {
      let emailDomain:string = 'icloud.com';
      if (this.credentials) {
        emailDomain = this.credentials.email.split('@').pop()??'icloud.com';
      }
      const userFolderVariation = emailFolderVariation[emailDomain];

      let folderNames = {
        inbox: [] as string[],
        sent: [] as string[],
        drafts: [] as string[],
        trash: [] as string[],
        junk: [] as string[],
        archive: ['Archive', 'Archived', 'All Mail'] as string[]
      }
      for (const currentDomain of Object.keys(emailFolderVariation)) {
        const currentFolderVariation = emailFolderVariation[currentDomain];
        if (currentFolderVariation) {
          const inboxVariations = currentFolderVariation.standardFolders.Basic.inbox.variation;
          const sentVariations = currentFolderVariation.standardFolders.Basic.sent.variation;
          const draftsVariations = currentFolderVariation.standardFolders.Basic.drafts.variation;
          const trashVariations = currentFolderVariation.standardFolders.Basic.trash.variation;
          const junkVariations = currentFolderVariation.standardFolders.Basic.junk.variation;
          const archiveVariations = currentFolderVariation.standardFolders.More['archive']?.variation;
          folderNames.inbox = [...new Set([...inboxVariations, ...folderNames.inbox])];
          folderNames.sent = [...new Set([...sentVariations, ...folderNames.sent])];
          folderNames.drafts = [...new Set([...draftsVariations, ...folderNames.drafts])];
          folderNames.trash = [...new Set([...trashVariations, ...folderNames.trash])];
          folderNames.junk = [...new Set([...junkVariations, ...folderNames.junk])];
          if (archiveVariations) {
            folderNames.archive = [...new Set([...archiveVariations, ...folderNames.archive])];
          }
        }
      }
      // console.log("debug folderNames:",folderNames);

      // const currentFolderVariation = emailFolderVariation[this.credentials]
      // Map standard folder names to common IMAP folder paths
      const folderLower = folder.toLowerCase();
      switch (folderLower) {
        case 'inbox':
          return 'INBOX';
        case 'sent':
          return (
            (await this.findFolderNameVariation(folderNames.sent)) || 'Sent'
          );
        case 'drafts':
          return (await this.findFolderNameVariation(folderNames.drafts)) || 'Drafts';
        case 'trash':
        case 'bin':
          return (
            (await this.findFolderNameVariation(folderNames.trash)) || 'Trash'
          );
        case 'spam':
        case 'junk':
          return (await this.findFolderNameVariation(folderNames.junk)) || 'Junk';
        case 'archive':
          return (
            (await this.findFolderNameVariation(folderNames.archive)) || 'Archive'
          );
        default:
          return folder;
      }
    }
  
    private async listAllFolders(): Promise<string[]> {
      const imapClient = await this.ensureImapConnection();
  
      try {
        // Get all mailboxes
        const mailboxes = await imapClient.list();
        // Extract folder paths
        return mailboxes.map((mailbox: { path: string }) => mailbox.path);
      } catch (error) {
        console.error('Error listing folders:', error);
        return [];
      }
    }
  
    private async checkMessageInFolder(folder: string, messageId: string): Promise<boolean> {
      try {
        const imapClient = await this.ensureImapConnection();
        // Open the folder
        await imapClient.mailboxOpen(folder);
  
        // Search for the message by ID
        let results = await imapClient.search({
          header: { 'Message-ID': `<${messageId}>` },
        });
  
        if (!results.length) {
          results = await imapClient.search(
            {
              uid: messageId,
            },
            { uid: true },
          );
        }
  
        return results.length > 0;
      } catch (error) {
        // Return false if we can't check the folder
        return false;
      }
    }
  
    private async findMessageFolder(messageId: string): Promise<string | null> {
      const imapClient = await this.ensureImapConnection();
  
      // First try common folders to optimize search
      let commonFoldersSet = new Set<string>();
      let emailDomain = null
      if (this.credentials) {
        emailDomain = this.credentials.email.split('@').pop();
      }
      if (emailDomain) {
        
        // 有用户邮箱
        const userFolderVariation = emailFolderVariation[emailDomain];
        if (userFolderVariation) {
          // 当前邮箱json中有配置
          commonFoldersSet.add(userFolderVariation.standardFolders.Basic.inbox.defaultName);
          commonFoldersSet.add(userFolderVariation.standardFolders.Basic.sent.defaultName);
          commonFoldersSet.add(userFolderVariation.standardFolders.Basic.drafts.defaultName);
          commonFoldersSet.add(userFolderVariation.standardFolders.Basic.junk.defaultName);
          commonFoldersSet.add(userFolderVariation.standardFolders.Basic.trash.defaultName);
          if (userFolderVariation.standardFolders.More['archive']?.defaultName) {
            commonFoldersSet.add(userFolderVariation.standardFolders.More['archive']?.defaultName);
          }
        }else {
          // 当前邮箱json中没有配置
          commonFoldersSet.add('INBOX');
          commonFoldersSet.add('Sent');
          commonFoldersSet.add('Drafts');
          commonFoldersSet.add('Archive');
          commonFoldersSet.add('Sent Messages');
          commonFoldersSet.add('Junk');
          commonFoldersSet.add('Deleted Messages');
        }
      }else {
        // 没有用户邮箱
        commonFoldersSet.add('INBOX');
        commonFoldersSet.add('Sent');
        commonFoldersSet.add('Drafts');
        commonFoldersSet.add('Archive');
        commonFoldersSet.add('Sent Messages');
        commonFoldersSet.add('Junk');
        commonFoldersSet.add('Deleted Messages');
      }

      const commonFolders:string[] = Array.from(commonFoldersSet);
      // console.log("debug commonFolders:", commonFolders);
      for (const folder of commonFolders) {
        try {
          const found = await this.checkMessageInFolder(folder, messageId);
          if (found) return folder;
        } catch (error) {
          // Continue to next folder
        }
      }
  
      // If not found in common folders, check all folders
      const allFolders = await this.listAllFolders();
  
      for (const folder of allFolders) {
        if (commonFolders.includes(folder)) continue; // Skip already checked folders
  
        try {
          const found = await this.checkMessageInFolder(folder, messageId);
          if (found) return folder;
        } catch (error) {
          // Continue to next folder
        }
      }
  
      return null;
    }
  
    private async parseImapMessage(
      parsed: any,
      uid: number,
      flags: string[],
      isAiLabel?: boolean,
      folder?: string,
    ): Promise<ParsedMessage> {
      // Extract message data from the parsed email
      const headers: { [key: string]: string } = {};
      if (parsed.headerLines) {
        parsed.headerLines.forEach((header: { key: string; line: string }) => {
          headers[header.key.toLowerCase()] = header.line;
        });
      }
  
      // Extract email addresses
      const from = parsed.from?.value[0] || { address: '', name: '' };
  
      const to =
        parsed.to?.value.map((addr: AddressObject) => ({
          email: addr.address || '',
          name: addr.name || '',
        })) || [];
  
      const cc =
        parsed.cc?.value.map((addr: AddressObject) => ({
          email: addr.address || '',
          name: addr.name || '',
        })) || null;
  
      const bcc =
        parsed.bcc?.value.map((addr: AddressObject) => ({
          email: addr.address || '',
          name: addr.name || '',
        })) || [];
  
      // Parse attachments
      const attachments: Attachment[] = (parsed.attachments || []).map((att: any, index: number) => {
        // console.log('debug email attachment:', att);
        return {
          filename: att.filename || `attachment-${index}`,
          mimeType: att.contentType || 'application/octet-stream',
          size: att.size || 0,
          attachmentId: `${uid}:${att.filename || index}`,
          headers: Object.entries(att.headers || {}).map(([name, value]) => ({
            name,
            value: value as string,
          })),
          body: att.content.toString('base64'), // 文件Buffer转换成base64
        };
      });
  
      // Determine if the message is unread based on flags
      const unread = !flags.includes('\\Seen');

      // Convert IMAP flags to tags
      const tags: Array<{ name: string; id: string; type: string }> = [];
      if (flags.includes('\\Flagged')) {
        tags.push({ name: 'STARRED', id: 'STARRED', type: 'system' });
      }
  
      // Extract message ID (remove angle brackets if present)
      // 统一使用UID作为messageId，确保与list()方法返回的ID一致
      let messageId = String(uid);   //重点 这里取uid
      // 如果是AI label的邮件，则uid改成（ 邮箱:文件夹:uid ）的格式
      if (isAiLabel && folder) {
        const userEmail = this.credentials?.email || this.config.auth?.email;
        messageId = `${userEmail}:${folder}:${uid}`;
      }

      // 保存真实的Message-ID到headers中，以备后用
      const realMessageId = parsed.messageId;
      if (realMessageId) {
        headers['message-id'] = realMessageId;
      }
  
      // Get HTML content with fallback to text
      const html = parsed.html || (parsed.text ? parsed.text.replace(/\n/g, '<br>') : '');
      // Extract header information
      const references = parsed.references || '';
      const inReplyTo = parsed.inReplyTo || '';
      const subject = parsed.subject || '(No subject)';
      // Create sender object
      const sender = {
        email: from.address || '',
        name: from.name || '',
      };
  
      // Get received date
      const receivedOn = parsed.date ? new Date(parsed.date).toISOString() : new Date().toISOString();
  
      return {
        id: messageId,
        threadId: messageId,
        title: parsed.subject || '',
        tags, // Use converted tags from IMAP flags
        tls: false, // Simplified TLS detection
        unread,
        sender,
        to,
        cc,
        bcc,
        receivedOn,
        subject,
        references,
        inReplyTo,
        messageId,
        body: parsed.text || '',
        decodedBody: html,
        processedHtml: html,
        blobUrl: '',
        attachments,
      };
    }
  
    private async prepareMailOptions(data: IOutgoingMessage): Promise<nodemailer.SendMailOptions> {
      const processedHtml = await sanitizeTipTapHtml(data.message);
  
      // Prepare recipients
      const to = this.formatRecipients(data.to);
      const cc = data.cc ? this.formatRecipients(data.cc) : undefined;
      const bcc = data.bcc ? this.formatRecipients(data.bcc) : undefined;
  
      console.log(`Preparing email to: ${to}, cc: ${cc || 'none'}, bcc: ${bcc || 'none'}`);
  
      // Basic email options
      const mailOptions: nodemailer.SendMailOptions = {
        from: data.fromEmail || this.config.auth.email,
        to,
        cc,
        bcc,
        subject: data.subject || '',
        html: processedHtml,
        headers: {},
      };
  
      // Add custom headers if provided
      if (data.headers) {
        Object.entries(data.headers).forEach(([key, value]) => {
          if (value) {
            mailOptions.headers![key] = value;
          }
        });
      }
  
      // Add message ID if replying to a thread
      if (data.headers?.['in-reply-to']) {
        mailOptions.inReplyTo = data.headers['in-reply-to'];
      }
      if (data.headers?.['references']) {
        mailOptions.references = data.headers['references'];
      }
  
      // Add attachments if provided
      if (data.attachments && data.attachments.length > 0) {
        console.log(`Processing ${data.attachments.length} attachments`);
        mailOptions.attachments = await Promise.all(
          data.attachments.map(async (file) => {
            const content = await file.arrayBuffer();
            return {
              filename: file.name,
              content: Buffer.from(content),
              contentType: file.type || 'application/octet-stream',
            };
          }),
        );
      }
  
      return mailOptions;
    }
  
    private formatRecipients(recipients: any): string {
      if (Array.isArray(recipients)) {
        // Handle array of recipient objects
        return recipients
          .map((recipient) => {
            if (typeof recipient === 'string') return recipient;
            // Handle different recipient formats
            if (typeof recipient === 'object') {
              const email = recipient.email || recipient.address || '';
              const name = recipient.name || '';
  
              if (!email) return '';
              return name ? `"${name}" <${email}>` : email;
            }
  
            return '';
          })
          .filter(Boolean) // Remove empty entries
          .join(', ');
      } else if (typeof recipients === 'string') {
        // Handle string input
        return recipients;
      }
      return '';
    }
  
    private async findFolder(folderName: string): Promise<string | null> {
      const provided =  this.getEmailProvider();
      if(provided === '163'){
        folderName = "草稿箱";
      }
      const imapClient = await this.ensureImapConnection();
      const mailboxes = await imapClient.list();
      // console.log('debug findFolder mailboxes:', mailboxes);

      let emailDomain:string = 'icloud.com';
      if (this.credentials) {
        emailDomain = this.credentials.email.split('@').pop()??'icloud.com';
      }
      
      const userFolderVariation = emailFolderVariation[emailDomain];
      let folderNameVariations:string[] = [];
      if (folderName.toLocaleLowerCase() === 'drafts') {
        folderNameVariations = userFolderVariation?.standardFolders.Basic.drafts.variation??[];
      }else if (folderName.toLocaleLowerCase() === 'trash') {
        folderNameVariations = userFolderVariation?.standardFolders.Basic.trash.variation??[];
      }
  
      // Common folder name variations
      const variations = [
        folderName,
        folderName.toLowerCase(),
        folderName.toUpperCase(),
        folderName.charAt(0).toUpperCase() + folderName.slice(1).toLowerCase(),
        ...folderNameVariations
      ];
  
      for (const mailbox of mailboxes) {
        const path = mailbox.path;
        const name = path.split('/').pop() || path;
  
        if (variations.includes(name)) {
          return path;
        }
      }
  
      return null;
    }
  
    private extractMessageId(message: any): string | null {
      // Extract Message-ID from headers
      const headers = message.envelope || {};
      const messageId = headers['messageId'];
      if (messageId) {
        const match = messageId.match(/<([^>]+)>/);
        if (match && match[1]) {
          return match[1];
        }
      }
      const uid = message.uid;
      return String(uid);
    }
  
    private extractThreadId(message: any): string | null {
      // Try to extract thread ID from References or In-Reply-To headers
      const headers = message.headers || {};
  
      // First check message-id
      const msgId = this.extractMessageId(message);
      if (msgId) return msgId;
  
      // Then look for references
      const references = headers['references'];
      if (references) {
        // Get the first message ID in references chain
        const matches = references.match(/<([^>]+)>/g);
        if (matches && matches.length > 0) {
          // Extract the first reference without the angle brackets
          return matches[0].replace(/[<>]/g, '');
        }
      }
      // Finally try in-reply-to
      const inReplyTo = headers['in-reply-to'];
      if (inReplyTo) {
        const match = inReplyTo.match(/<([^>]+)>/);
        if (match && match[1]) {
          return match[1];
        }
      }
      return null;
    }

    public async get(id: string, folderHint?: string): Promise<IGetThreadResponse> {
      return this.withErrorHandler(
        'get',
        async () => {
          console.log(`debug get id:${id} folderHint:${folderHint}`);
          // 如果是AI label中的邮件，另外处理
          // 检查格式 邮箱:文件夹:uid
          const regexGuid = /^[^\s:@]+@[^\s:@]+\.[^\s:@]+:[^:]+:[^:]+$/; 
          if (folderHint === 'ai-label') {
            if (!regexGuid.test(id)) {
              throw new Error('Please use formated guid on ai-label folder');
            }
            const [currentEmail, currentFolder, currentUid] = id.split(':');
            console.log(`debug ai-label get currentEmail:${currentEmail} currentFolder:${currentFolder} currentUid:${currentUid}`);
            if (currentEmail && currentFolder && currentUid) {
              const imapClient = await this.ensureImapConnection();
              // let folder = folderHint ? await this.getImapFolderPath(currentFolder) : 'inbox';
              // await imapClient.mailboxOpen(folder);
              await imapClient.mailboxOpen(currentFolder);
              const fetchOptions = {
                    uid: true,
                    envelope: true,
                    bodyStructure: true,
                    source: true,
                    flags: true,
                  };
              const fetchedMessage = await imapClient.fetchOne(currentUid, fetchOptions, {
                uid: true,
              });
              // console.log('debug ai-label fetchedMessage:',fetchedMessage);
              if (!fetchedMessage || !fetchedMessage.source?.length) {
                throw new Error('uid not found');
              }
              const parsed = await simpleParser(fetchedMessage.source);
              const uidNumber: number = parseInt(currentUid, 10)
              const parsedMessage = await this.parseImapMessage(
                parsed,
                uidNumber,
                Array.isArray(fetchedMessage.flags)
                  ? fetchedMessage.flags
                  : Array.from(fetchedMessage.flags ?? []),
                true,
                currentFolder,
              );
              const messages:ParsedMessage[] = [];
              messages.push(parsedMessage);
              const threadMessages = messages.sort(
                (a: ParsedMessage, b: ParsedMessage) =>
                  new Date(a.receivedOn).getTime() - new Date(b.receivedOn).getTime(),
              );
              let allMessageTags = [];
              for (const threadMessage of threadMessages) {
                allMessageTags.push(...threadMessage.tags);
              }
              const labels = allMessageTags;
              return {
                messages: threadMessages,
                latest: threadMessages[threadMessages.length - 1],
                hasUnread: threadMessages.some((msg: ParsedMessage) => msg.unread),
                totalReplies: threadMessages.length,
                labels: labels.map((label: Label) => ({ id: label.id, name: label.name })),
              };
            }
          }

          const imapClient = await this.ensureImapConnection();
            console.log(`🔍 [MAIL.GET] 请求详情 - ID: ${id}, folder: ${folderHint}, 调用栈: ${new Error().stack?.split('\n')[2]?.trim()}`);
          // Check if we need to convert from thread: prefix
          const messageId = id.startsWith('thread:') ? id.substring(7) : id;
          console.log(`Getting thread with ID: ${messageId}`);

          // 🚀 优先使用传入的folder参数，避免不必要的文件夹搜索
          let folder = folderHint ? await this.getImapFolderPath(folderHint) : null;
          if (!folder) {
            console.log(`Message with ID ${messageId} not found in any folder, trying INBOX`);
            return this.get(messageId, 'inbox');
          }
  
          console.log(`Found message ${messageId} in folder: ${folder}`);
  
          try {
            // Select the folder
            await imapClient.mailboxOpen(folder);
  
            // First try to search by Message-ID header
            // 直接使用messageId作为UID，但需要确保类型正确
            let results = [messageId];
            // let results = await imapClient.search(
            //   {
            //     header: { 'Message-ID': `<${messageId}>` },
            //   },
            //   { uid: true },
            // );
            /**
            
            
            
  
            // As a last resort, try looking up by UID if it's a numeric ID 提前通过UID查找
            if (!results || !results?.length) {
              try {
                console.log(`No messages found with headers, trying as UID: ${messageId}`);
                // 直接使用messageId作为UID字符串，避免parseInt精度丢失
                const uidResults = await imapClient.search(
                  { uid: messageId },
                  { uid: true },
                );
                if (uidResults && uidResults.length > 0) {
                  results = [messageId];
                }
              } catch (e) {
                console.error(`Error parsing UID from ${messageId}:`, e);
              }
            }
  
            // If that doesn't work, try References or In-Reply-To headers to find related messages
            if (!results || !results.length) {
              console.log(`No messages found with Message-ID: ${messageId}, trying References`);
  
              results = await imapClient.search(
                {
                  header: { References: `${messageId}` },
                },
                { uid: true },
              );
  
              // Try In-Reply-To as well
              if (!results || !results.length) {
                console.log(`No messages found with References: ${messageId}, trying In-Reply-To`);
  
                results = await imapClient.search(
                  {
                    header: { 'In-Reply-To': `<${messageId}>` },
                  },
                  { uid: true },
                );
              }
            } */
            if (!results || !results.length) {
              throw new Error(`Thread with ID ${messageId} not found`);
            }
  
           // console.log(`Found ${results.length} messages for thread ID: ${messageId}`);
            //console.log('debug mail.get get results:', results);

            // Fetch messages and parse them - 改为串行处理
            const messages:ParsedMessage[] = [];
            for (const uid of results) {
               // console.log(`Fetching message data for UID: ${uid} (type: ${typeof uid})`);
                try {
                  const fetchOptions = {
                    uid: true,
                    envelope: true,
                    bodyStructure: true,
                    source: true,
                    flags: true,
                  };
  
                  // 确保UID是正确的类型 - ImapFlow期望数字或字符串
                  const uidParam = typeof uid === 'string' ? uid : String(uid);
                  const fetchedMessage = await imapClient.fetchOne(uidParam, fetchOptions, {
                    uid: true,
                  });
                  if (!fetchedMessage || !fetchedMessage.source?.length) {
                    console.warn(`⚠️ UID ${uid} 不存在或已被删除，跳过此邮件`);
                    continue; // 跳过这个UID，继续处理下一个
                  }
  
                 // console.log(`Parsing message with UID: ${uid}`);
                  const parsed = await simpleParser(fetchedMessage.source);
  
                  // 确保UID是数字类型给parseImapMessage，但避免精度丢失
                  // 对于大数字，我们使用BigInt然后转换为Number，如果超出安全范围则保持字符串
                  let uidNumber: number;
                  if (typeof uid === 'string') {
                    const bigIntUid = BigInt(uid);
                    if (bigIntUid <= Number.MAX_SAFE_INTEGER) {
                      uidNumber = Number(bigIntUid);
                    } else {
                      // 如果超出安全范围，使用字符串的哈希值作为数字ID
                      uidNumber = uid.split('').reduce((hash, char) => {
                        return ((hash << 5) - hash) + char.charCodeAt(0);
                      }, 0);
                    }
                  } else {
                    uidNumber = uid;
                  }
                  const parsedMessage = await this.parseImapMessage(
                    parsed,
                    uidNumber,
                    Array.isArray(fetchedMessage.flags)
                      ? fetchedMessage.flags
                      : Array.from(fetchedMessage.flags ?? []),
                  );

                  messages.push(parsedMessage);
                } catch (error) {
                  console.error(`Error fetching message UID ${uid}:`, error);
                  throw error;
                }
            }
  
            // Group messages by thread and sort by date
            const threadMessages = messages.sort(
              (a: ParsedMessage, b: ParsedMessage) =>
                new Date(a.receivedOn).getTime() - new Date(b.receivedOn).getTime(),
            );
  
            // Get labels (folders) for this thread
            let allMessageTags = [];
            for (const threadMessage of threadMessages) {
              allMessageTags.push(...threadMessage.tags);
            }
            const labels = allMessageTags;
            // console.log('threadMessages mail.get get labels:', threadMessages);
            const userEmail = this.credentials?.email || this.config.auth?.email;
            // Use BullMQ to handle AI label processing as a background task
            // const aiLabelQueue = new Queue('ai-label', {
            //   connection: {
            //   host: 'localhost',
            //   port: 8079,
            //   },
            // });
            // await aiLabelQueue.add('handleEmailByAiLabel', {
            //   userEmail,
            //   folder,
            //   messageId,
            //   threadMessages,
            // });
            // console.log("debug return get");
            let formatFolder = folderHint??'inbox';
            // 把邮件传给AI打上标签
            handleEmailByAiLabel(userEmail, folder, messageId, threadMessages);
            // 把邮件传给AI进行总结
            // handleEmailByAiSummary(userEmail, formatFolder, messageId, threadMessages);
            // console.log("debug return get");
  
            return {
              messages: threadMessages,
              latest: threadMessages[threadMessages.length - 1],
              hasUnread: threadMessages.some((msg: ParsedMessage) => msg.unread),
              totalReplies: threadMessages.length,
              labels: labels.map((label: Label) => ({ id: label.id, name: label.name })),
            };
          } catch (error) {
            console.error(`Error retrieving thread ${messageId}:`, error);
            throw error;
          }
        },
        { id },
      );
    }
  
    public async create(data: IOutgoingMessage): Promise<{ id?: string | null }> {
      return this.withErrorHandler(
        'create',
        async () => {
          console.log('Preparing to send email');
          // Prepare mail options
          const mailOptions = await this.prepareMailOptions(data);
  
          try {
            // First verify SMTP connection
            console.log('Verifying SMTP connection...');
            await this.smtpTransport.verify();
  
            console.log('Sending email...');
            const result = await this.smtpTransport.sendMail(mailOptions);
            console.log('Email sent successfully:', result.messageId);

            
            // TODO 手动保存到sent邮箱
            // console.log('debug send email mailOptions:',mailOptions);
            // 163邮箱不需要手动保存
            const userEmail = this.credentials?.email || this.config.auth?.email;
            if (userEmail.endsWith('163.com')) {
              return { id: result.messageId };
            }
            const MailComposer = require("nodemailer/lib/mail-composer");
            const imapClient = await this.ensureImapConnection();
            const mailComposerInstance = new MailComposer(mailOptions);
            mailComposerInstance.compile().build(async (err:any, message:any) => {
              if (err) throw err;
              // console.log('debug send email buffer email:', message);
              // console.log('debug send email buffer to string email:', message.toString());
              let rfc822Email = message.toString();
              let sentFolder = await this.getImapFolderPath('sent');
              await imapClient.append(sentFolder, rfc822Email, ['\\Seen']);
            });

  
            return { id: result.messageId };
          } catch (error) {
            console.error('Error sending email:', error);
            throw new Error(`Failed to send email: ${(error as Error).message}`);
          }
        },
        { data },
      );
    }
  
    public async sendDraft(draftId: string, data: IOutgoingMessage): Promise<void> {
      return this.withErrorHandler(
        'sendDraft',
        async () => {
          const imapClient = await this.ensureImapConnection();
  
          // First get the draft
          const draft = await this.getDraft(draftId);
  
          // Merge the draft data with any updates in the data parameter
          const mailOptions = await this.prepareMailOptions({
            ...data,
            to: data.to || (draft.to ? draft.to.map((email) => ({ email, name: '' })) : []),
            subject: data.subject || draft.subject || '',
            message: data.message || draft.content || '',
          });
  
          try {
            // Send the email
            await this.smtpTransport.sendMail(mailOptions);
  
            // Delete the draft after sending
            // Find the Drafts folder
            const draftsFolder = await this.findFolder('Drafts');
  
            if (draftsFolder) {
              // Open the drafts folder
              await imapClient.mailboxOpen(draftsFolder);
  
              // Search for the draft by ID
              const results = await imapClient.search({
                header: { 'Message-ID': `<${draftId}>` },
              });
  
              if (results.length > 0) {
                // Delete the draft
                await imapClient.messageFlagsAdd(results, ['\\Deleted']);
                // TODO mailboxExpunge方法不存在
                // await imapClient.mailboxExpunge();
              }
            }
          } catch (error) {
            console.error('Error sending draft:', error);
            throw new Error(`Failed to send draft: ${(error as Error).message}`);
          }
        },
        { draftId, data },
      );
    }
  
    public async delete(id: string): Promise<void> {
      return this.withErrorHandler(
        'delete',
        async () => {
          // console.log('debug delete email run');
          const imapClient = await this.ensureImapConnection();
  
          // Find the folder containing this message
          const folder = await this.findMessageFolder(id);
  
          if (!folder) {
            throw new Error(`Message with ID ${id} not found in any folder`);
          }
  
          // Open the folder
          await imapClient.mailboxOpen(folder);
  
          // Search for the message by ID
          let results = await imapClient.search({
            header: { 'Message-ID': `<${id}>` },
          });

          // 确保results是数组
          if (!results || !Array.isArray(results)) {
            results = [];
          }

          if (!results.length) {
            // Try looking up by UID if it's a numeric ID
            try {
              const uid = parseInt(id);
              if (!isNaN(uid)) {
                results.push(uid);
              }
            } catch (e) {
              // Ignore parsing errors
            }
          }

          if (!results.length) {
            throw new Error(`Message with ID ${id} not found`);
          }
  
          // Move to trash or mark as deleted
          try {
            // First try to move to trash folder
            const trashFolder = await this.findFolder('Trash');
  
            if (trashFolder) {
              // Move to trash if trash folder exists
              await imapClient.messageMove(results, trashFolder);
            } else {
              // Otherwise flag as deleted
              await imapClient.messageFlagsAdd(results, ['\\Deleted']);
              // Expunge to remove the messages marked for deletion
              // TODO mailboxExpunge方法不存在
              // await imapClient.mailboxExpunge();
            }
          } catch (error) {
            console.error('Error deleting message:', error);
            throw error;
          }
        },
        { id },
      );
    }
  
  
    public async list(params: {
      folder: string;
      query?: string;
      maxResults?: number;
      labelIds?: string[];
      pageToken?: string | number;
    }): Promise<{ threads: { id: string; $raw?: unknown }[]; nextPageToken: string | null }> {
      return this.withErrorHandler(
        'list',
        async () => {          
          let { folder, query, maxResults = 10, pageToken } = params;
          console.log(`debug list folder:${folder} query:${query} pageToken:${pageToken}`);
          // AI label 虚拟邮箱
          
          const userEmail = this.credentials?.email || this.config.auth?.email;
          if (folder === 'ai-label') {
            if (!query) {
              // throw new Error('query is needed for AI label');
              console.warn('query is needed for AI label');
              return {threads: [], nextPageToken: null};
            }
            const c = getContext<HonoContext>();
            const emailResults = await c.var.db
            .select()
            .from(aiLabelEmail)
            .orderBy(desc(aiLabelEmail.receivedOn))
            .where(and(eq(aiLabelEmail.email, userEmail), eq(aiLabelEmail.labelName, query)));
            // 分页
            if (!emailResults.length) {
              return {threads: [], nextPageToken: null}
            }
            let startIndex = 1;
            if (pageToken && typeof pageToken === 'string') {
              startIndex = parseInt(pageToken, 10);
              if (isNaN(startIndex)) startIndex = 1;
            } else if (typeof pageToken === 'number') {
              startIndex = pageToken;
            }
            const endIndex = Math.min(startIndex + maxResults - 1, emailResults.length);
            const nextPageToken = endIndex < emailResults.length ? `${endIndex + 1}` : null;
            const currentEmailResults = emailResults.slice(startIndex-1, endIndex);
            const resultThreads = currentEmailResults.map((item)=>{
              return {
                id: item.guid
              }
            })
            return {
              threads: resultThreads,
              nextPageToken,
            }
          }
          const imapClient = await this.ensureImapConnection();
          console.log(`maxResults list email run ${maxResults}`);
          // maxResults = 10;
          // Map folder name to IMAP path
          const folderPath = await this.getImapFolderPath(folder);
          console.log(`Listing emails from folder up: ${folderPath}`);
  
          try {
            // Open the mailbox
            const mailbox = await imapClient.mailboxOpen(folderPath);
            console.log(`Mailbox opened: ${folderPath}, total messages: ${mailbox.exists}`);
  
            // Handle empty mailbox
            if (mailbox.exists === 0) {
              return { threads: [], nextPageToken: null };
            }
  
            // Determine range to fetch - 修复分页逻辑
            let startSeq = 1;
            if (pageToken && typeof pageToken === 'string') {
              startSeq = parseInt(pageToken, 10);
              if (isNaN(startSeq)) startSeq = 1;
            } else if (typeof pageToken === 'number') {
              startSeq = pageToken;
            }

            // Calculate end sequence number
            const endSeq = Math.min(startSeq + maxResults - 1, mailbox.exists);
  
            // Build search criteria
            const searchCriteria: any = {};
            if (query) {
              searchCriteria.text = query;
            }
  
            // Get message UIDs
            let messageIds;
            // 不使用query,query‘is:important NOT is:sent NOT is:draft’只适用gmail
             {
              // 修复：从最新邮件开始，按页获取不重复的邮件
              // 第一页：获取最新的 maxResults 封邮件
              // 第二页：获取接下来的 maxResults 封邮件
              const rangeStart = Math.max(1, mailbox.exists - endSeq + 1);
              const rangeEnd = Math.max(1, mailbox.exists - startSeq + 1);
              const range = `${rangeStart}:${rangeEnd}`;
              console.log(`📧 分页信息: startSeq=${startSeq}, endSeq=${endSeq}, 邮箱总数=${mailbox.exists}`);
              console.log(`📧 获取邮件范围: ${range} (从序号${rangeStart}到${rangeEnd})`);
              try {
                // 确保邮箱仍然处于打开状态
                await imapClient.mailboxOpen(folderPath);
                const messages = imapClient.fetch(range, { uid: true });
                messageIds = [];
                for await (const msg of messages) {
                  messageIds.push(msg.uid);
                }
              } catch (error) {
                console.error(`Error fetching message range ${range}:`, error);
                messageIds = [];
              }
            }
  
            // Sort UIDs in descending order (newest first)
            messageIds.sort((a, b) => b - a);
            // console.log('debug mail.listThreads list messageIds:', messageIds," folder: ",folder);
  
            // Fetch message data for the UIDs
            const threadsMap = new Map(); // Map to track unique threads
            for (const uid of messageIds) {
              try {
                const fetchOptions = {
                  uid: true,
                  envelope: true,
                  bodyStructure: true,
                  flags: true,
                  headers: ['message-id', 'references', 'in-reply-to'],
                };
  
                const fetchedMessage = await imapClient.fetchOne(uid, fetchOptions, {
                  uid: true,
                });
                // console.log('debug mail.listThreads list fetchedMessage:', fetchedMessage);
                if (!fetchedMessage) {
                  console.warn(`No message data returned for UID ${uid}`);
                  continue;
                }
  
                // Extract message ID
                // 使用UID作为邮件ID，确保get()方法能正确找到邮件
                const msgId: string = String(fetchedMessage.uid);

                // Use UID as thread ID for consistency
                const threadId = msgId;
  
                // Extract historyId if available (IMAP doesn't have this, so set to null)
                const historyId: string | null = null;
  
                // Skip duplicates in the same thread
                if (!threadsMap.has(threadId)) {
                  threadsMap.set(threadId, {
                    id: threadId,
                    historyId,
                    $raw: {
                      uid,
                      envelope: fetchedMessage.envelope,
                      flags: fetchedMessage.flags,
                    },
                  });
                }
              } catch (error) {
                console.error(`Error fetching message ${uid}:`, error);
              }
            }
            // console.log('debug mail.listThreads list threadsMap:', threadsMap);
  
            // Get threads sorted by most recent messages
            const threads = Array.from(threadsMap.values()).map((thread: any) => ({
              id: thread.id,
              historyId: thread.historyId ?? null,
              $raw: thread.$raw,
            }));
  
            // Calculate next page token - 修复分页逻辑
            const nextPageToken = endSeq < mailbox.exists ? `${endSeq + 1}` : null;
            console.log(`📧 下一页token: ${nextPageToken} (当前endSeq=${endSeq}, 总邮件=${mailbox.exists})`);
  
            return {
              threads: threads.map((t: any) => ({
                id: t.id,
                historyId: t.historyId ?? null,
                $raw: t.$raw,
              })),
              nextPageToken,
            };
          } catch (error) {
            console.error(`Error listing messages from folder ${folderPath}:`, error);
            throw error;
          }
        },
        { ...params },
      );
    }
  
    public async count(): Promise<{ count?: number; label?: string }[]> {
      return this.withErrorHandler(
        'count',
        async () => {
          const imapClient = await this.ensureImapConnection();
  
          // Get all mailboxes
          const mailboxes = await imapClient.list();
          console.log('原有的文件夹---:', mailboxes);
  
          // Result array for folder counts
          const counts = [];
  
          for (const mailbox of mailboxes) {
            try {
              const status = await imapClient.status(mailbox.path, {
                messages: true,
                unseen: true,
              });
  
              counts.push({
                label: mailbox.path,
                count: status.unseen || 0,
              });
            } catch (error) {
              //console.error(`Error getting count for folder ${mailbox.path}:`, error);
            }
          } 
  
          return counts;
        },
        { email: this.config.auth?.email },
      );
    }
  
    //获取标签
    public async getUserLabels(): Promise<Label[]> {
      return this.withErrorHandler(
        'getUserLabels',
        async () => {
         
          // Return cached labels if available
          if (this.userLabels.length > 0) {
            return this.userLabels;
          }
  
          const imapClient = await this.ensureImapConnection();
          console.log('Getting user labels (folders)');
  
          try {
            // Get all mailboxes/folders
            const mailboxes = await imapClient.list();
            console.log(`Found ${mailboxes.length} mailboxes`);
  
            // Convert IMAP folders to Labels
            this.userLabels = mailboxes.map((mailbox: any) => {
              const name = mailbox.name || mailbox.path.split('/').pop() || mailbox.path;
  
              // Determine if this is a system folder
              const isSystemFolder =
                !!mailbox.specialUse ||
                ['INBOX', 'Drafts', 'Sent', 'Trash', 'Junk', 'Spam'].includes(name);
  
              return {
                id: mailbox.path,
                name,
                type: isSystemFolder ? 'system' : 'user',
                color: {
                  backgroundColor: '#E3E3E3',
                  textColor: '#333333',
                },
              };
            });
  
            return this.userLabels;
          } catch (error) {
            console.error('Error getting user labels:', error);
            return [];
          }
        },
        { email: this.config.auth?.email },
      );
    }
  
    public async markAsRead(threadIds: string[]): Promise<void> {
      return this.withErrorHandler(
        'markAsRead',
        async () => {
          const imapClient = await this.ensureImapConnection();
          console.log(`Marking ${threadIds.length} threads as read`);
  
          for (const threadId of threadIds) {
            // Get the actual message ID (remove thread: prefix if present)
            const messageId = threadId.startsWith('thread:') ? threadId.substring(7) : threadId;
            // Find the folder containing this message
            const folder = await this.findMessageFolder(messageId);
  
            if (!folder) {
              console.warn(`Message ${messageId} not found in any folder, skipping mark as read`);
              continue;
            }
  
            // Open the folder
            await imapClient.mailboxOpen(folder);
  
            // Search for the message by ID
            const results = await imapClient.search({
              header: { 'Message-ID': `<${messageId}>` },
            });
  
            if (!results.length) {
              // Try looking up by references
              // const refResults = await imapClient.search({
              //   header: { References: messageId },
              // });
              if (false) {
                // Add found messages to results
                // results.push(...refResults);
              } else {
                // Try looking up by UID
                try {
                  const uid = parseInt(messageId);
                  if (!isNaN(uid)) {
                    const uidResults = await imapClient.search({ uid: String(uid) });
                    if (uidResults.length) {
                      results.push(...uidResults);
                    }
                  }
                } catch (e) {
                  // Ignore parsing errors
                }
              }
            }
  
            if (results.length) {
              // Mark as read by adding the \Seen flag
              await imapClient.messageFlagsAdd(results, ['\\Seen']);
              console.log(`Marked message(s) ${results.join(', ')} as read in folder ${folder}`);
            } else {
              console.warn(
                `Message ${messageId} not found in folder ${folder}, skipping mark as read`,
              );
            }
          }
        },
        { threadIds },
      );
    }
  
    public async markAsUnread(threadIds: string[]): Promise<void> {
      return this.withErrorHandler(
        'markAsUnread',
        async () => {
          const imapClient = await this.ensureImapConnection();
          console.log(`Marking ${threadIds.length} threads as unread`);
          // console.log('debug markAsUnread threadIds:', threadIds);
  
          for (const threadId of threadIds) {
            // Get the actual message ID (remove thread: prefix if present)
            const messageId = threadId.startsWith('thread:') ? threadId.substring(7) : threadId;
            // Find the folder containing this message
            const folder = await this.findMessageFolder(messageId);
  
            if (!folder) {
              console.warn(`Message ${messageId} not found in any folder, skipping mark as unread`);
              continue;
            }
            // console.log('debug markAsUnread found email in mailbox:',folder);
  
            // Open the folder
            await imapClient.mailboxOpen(folder);
  
            // Search for the message by ID
            const results = await imapClient.search({
              header: { 'Message-ID': `<${messageId}>` },
            });

            // console.log('debug markAsUnread search by Message-ID:',results);
  
            if (!results.length) {
              // Try looking up by references 不要通过References头查找，可能找到一堆
              // const refResults = await imapClient.search({
              //   header: { References: messageId },
              // });
              // console.log('debug markAsUnread search by References:',refResults);
              if (false) {
                // Add found messages to results
                results.push(...refResults);
              } else {
                // Try looking up by UID as last resort
                try {
                  const uid = parseInt(messageId);
                  if (!isNaN(uid)) {
                    // uidResults不是找到的邮件uid列表，是sequence number
                    const uidResults = await imapClient.search({ uid: String(uid) });
                    // console.log('debug markAsUnread search by uid:',uidResults);
                    if (uidResults.length) {
                      results.push(...uidResults);
                    }
                  }
                } catch (e) {
                  // Ignore parsing errors
                }
              }
            }
  
            if (results.length) {
              // Mark as unread by removing the \Seen flag
              // console.log('debug markAsUnread before messageFlagsRemove:',results);
              // messageFlagsRemove操作也是通过sequence number
              await imapClient.messageFlagsRemove(results, ['\\Seen']);
              console.log(`Marked message(s) ${results.join(', ')} as unread in folder ${folder}`);
            } else {
              console.warn(
                `Message ${messageId} not found in folder ${folder}, skipping mark as unread`,
              );
            }
          }
        },
        { threadIds },
      );
    }
  
    // 草稿相关方法 - 现在支持草稿功能
    public async createDraft(
      data: CreateDraftData,
    ): Promise<{ id?: string | null; success?: boolean; error?: string }> {
      return this.withErrorHandler(
        'createDraft',
        async () => {
          const imapClient = await this.ensureImapConnection();
  
          try {
            // First, find the Drafts folder
            const draftsFolder = await this.findFolder('Drafts');
  
            if (!draftsFolder) {
              throw new Error('Drafts folder not found');
            }
  
            // Create MIME message
            const msg = createMimeMessage();
            msg.setSender(this.config.auth.email);
  
            // Add recipients
            if (data.to) {
              const toAddresses = data.to.split(',').map((addr) => ({ addr: addr.trim() }));
              msg.setTo(toAddresses);
            }
  
            if (data.cc) {
              const ccAddresses = data.cc.split(',').map((addr) => ({ addr: addr.trim() }));
              msg.setCc(ccAddresses);
            }
  
            if (data.bcc) {
              const bccAddresses = data.bcc.split(',').map((addr) => ({ addr: addr.trim() }));
              msg.setBcc(bccAddresses);
            }
  
            // Set subject and content
            msg.setSubject(data.subject || '');
            msg.addMessage({
              contentType: 'text/html',
              data: await sanitizeTipTapHtml(data.message || ''),
            });
  
            // Add attachments if present
            if (data.attachments && data.attachments.length > 0) {
              for (const attachment of data.attachments) {
                const arrayBuffer = await attachment.arrayBuffer();
                const base64Data = Buffer.from(arrayBuffer).toString('base64');
                msg.addAttachment({
                  filename: attachment.name,
                  contentType: attachment.type,
                  data: base64Data,
                });
              }
            }
  
            // Generate the raw email
            const rawEmail = msg.asRaw();
  
            // Open drafts folder
            await imapClient.mailboxOpen(draftsFolder);
  
            // If updating existing draft
            if (data.id) {
              // First, find and delete the existing draft
              const results = await imapClient.search({
                header: { 'Message-ID': `<${data.id}>` },
              });
  
              if (results.length > 0) {
                // Mark the existing draft for deletion
                await imapClient.messageFlagsAdd(results, ['\\Deleted']);
                // TODO mailboxExpunge不存在
                // await imapClient.mailboxExpunge();
              }
            }
  
            // Append the new draft to the Drafts folder 添加草稿时自动设为已读
            const appendResult = await imapClient.append(draftsFolder, rawEmail, ['\\Draft', '\\Seen']);
  
            // Extract the message ID from the rawEmail
            const messageIdMatch = rawEmail.match(/Message-ID:\s*<([^>]+)>/i);
            const messageId = messageIdMatch ? messageIdMatch[1] : `draft-${uuidv4()}`;
  
            return {
              id: messageId,
              success: true,
            };
          } catch (error) {
            console.error('Error creating draft:', error);
            return {
              success: false,
              error: `Failed to create draft: ${(error as Error).message}`,
            };
          }
        },
        { data },
      );
    }
  
    public async getDraft(draftId: string): Promise<ParsedDraft> {
      return this.withErrorHandler(
        'getDraft',
        async () => {
          const imapClient = await this.ensureImapConnection();

          // Find the Drafts folder
          const draftsFolder = await this.findFolder('Drafts');

          if (!draftsFolder) {
            throw new Error('Drafts folder not found');
          }

          // Open the drafts folder
          await imapClient.mailboxOpen(draftsFolder);

          let uid: number;

          // 🎯 支持 UID 直接查询和 Message-ID 查询
          if (/^\d+$/.test(draftId)) {
            // 如果 draftId 是纯数字，直接作为 UID 使用
            uid = parseInt(draftId, 10);
            console.log(`📧 [DRAFTS] 使用 UID 直接查询: ${uid}`);
          } else {
            // 否则通过 Message-ID 搜索获取 UID
            console.log(`📧 [DRAFTS] 通过 Message-ID 搜索: ${draftId}`);
            const results = await imapClient.search(
              {
                header: { 'Message-ID': `<${draftId}>` },
              },
              { uid: true },
            );
            // console.log('debug drafts.get getDraft results:', results);

            if (!results.length) {
              throw new Error(`Draft with ID ${draftId} not found`);
            }

            uid = results[0];
          }
          const fetchOptions = {
            uid: true,
            source: true,
          };
  
          const fetchedMessage = await imapClient.fetchOne(uid, fetchOptions, {
            uid: true,
          });
          if (!fetchedMessage.source) {
            throw new Error(`debug drafts.get getDraft ${draftId} email source not found`);
          }
          const parsed = await simpleParser(fetchedMessage.source);
  
          // Extract information for ParsedDraft
          const to =
            parsed.to?.value?.map(
              (addr: { address?: string; value: string }) => addr.address || addr.value,
            ) || [];
          const subject = parsed.subject || '';
          const content = parsed.html || parsed.text?.replace(/\n/g, '<br>') || '';
  
          return {
            id: draftId,
            to,
            subject,
            content,
            rawMessage: parsed,
          };
        },
        { draftId },
      );
    }
  
    public async listDrafts(params: {
      q?: string;
      maxResults?: number;
      pageToken?: string | number;
    }): Promise<{ threads: { id: string; $raw: unknown }[]; nextPageToken: string | null }> {
      return this.withErrorHandler(
        'listDrafts',
        async () => {          
          const { q, maxResults = 20, pageToken } = params;
          console.log(`debug listDrafts called, params q:${q} maxResults:${maxResults} pageToken:${pageToken}`);          
          const imapClient = await this.ensureImapConnection();
  
          // Find the Drafts folder
          const draftsFolder = await this.findFolder('Drafts');
  
          if (!draftsFolder) {
            throw new Error('Drafts folder not found');
          }

          // console.log("debug listDrafts draftsFolder:",draftsFolder);
  
          // Open the drafts folder
          const mailbox = await imapClient.mailboxOpen(draftsFolder);
  
          // Determine range to fetch - 修复分页逻辑，与list()方法保持一致
          let startSeq = 1;
          if (pageToken && typeof pageToken === 'string') {
            startSeq = parseInt(pageToken, 10);
            if (isNaN(startSeq)) startSeq = 1;
          } else if (typeof pageToken === 'number') {
            startSeq = pageToken;
          }

          // Calculate end sequence number
          const endSeq = Math.min(startSeq + maxResults - 1, mailbox.exists);
  
          // Search criteria
          let searchCriteria: any = {};
  
          if (q) {
            searchCriteria.text = q;
          }

          // console.log("debug listDrafts searchCriteria: ",searchCriteria);
  
          // Get the message UIDs
          let uids;
  
          if (Object.keys(searchCriteria).length > 0) {
            const searchResults = await imapClient.search(searchCriteria);
            uids = Array.isArray(searchResults) ? searchResults.slice(0, maxResults) : [];
          } else {
            // 修复：从最新邮件开始，按页获取不重复的邮件，与list()方法保持一致
            const rangeStart = Math.max(1, mailbox.exists - endSeq + 1);
            const rangeEnd = Math.max(1, mailbox.exists - startSeq + 1);
            const range = `${rangeStart}:${rangeEnd}`;
            console.log(`📧 [DRAFTS] 分页信息: startSeq=${startSeq}, endSeq=${endSeq}, 邮箱总数=${mailbox.exists}`);
            console.log(`📧 [DRAFTS] 获取邮件范围: ${range} (从序号${rangeStart}到${rangeEnd})`);

            // 确保邮箱仍然处于打开状态
            const draftMailboxObject = await imapClient.mailboxOpen(draftsFolder);
            if (draftMailboxObject.exists !== 0) {
              // 草稿邮箱中有邮件时调用fetch
              const messages = imapClient.fetch(range, { uid: true });
              uids = [];
              for await (const msg of messages) {
                uids.push(msg.uid);
              }
            }else {
              // 草稿邮箱中没有邮件，不调用fetch，否则报错
              uids = [];
            }
            
          }
  
          // Sort UIDs in descending order (newest first)
          uids.sort((a: number, b: number) => b - a);
          // console.log("debug listDrafts uids: ",uids);
  
          // Fetch message data
          const drafts = [];
  
          for (const uid of uids) {
            try {
              const fetchOptions = {
                uid: true,
                envelope: true,
                bodyStructure: true,
                headers: ['message-id', 'subject', 'from', 'to', 'cc', 'bcc', 'date'],
              };
  
              const fetchedMessage = await imapClient.fetchOne(uid, fetchOptions, {
                uid: true,
              });

              if (!fetchedMessage) {
                console.warn(`⚠️ [DRAFTS] UID ${uid} 不存在或已被删除，跳过此草稿`);
                continue;
              }

              const messageId = fetchedMessage.envelope?.['messageId'] || `draft-${uid}`;

              drafts.push({
                id: String(uid), // 🎯 使用 UID 作为 ID，与 list() 方法保持一致
                $raw: {
                  uid: uid,
                  messageId: messageId, // 🎯 保存真实的 Message-ID 到 $raw
                  envelope: fetchedMessage.envelope,
                  flags: fetchedMessage.flags,
                  receivedOn: fetchedMessage.envelope?.date
                    ? new Date(fetchedMessage.envelope.date).toISOString()
                    : new Date().toISOString(),
                },
              });
            } catch (error) {
              console.error(`Error fetching draft ${uid}:`, error);
            }
          }
  
          // Calculate next page token - 修复分页逻辑，与list()方法保持一致
          const nextPageToken = endSeq < mailbox.exists ? `${endSeq + 1}` : null;
          console.log(`📧 [DRAFTS] 下一页token: ${nextPageToken} (当前endSeq=${endSeq}, 总邮件=${mailbox.exists})`);
  
          return {
            threads: drafts,
            nextPageToken,
          };
        },
        { ...params },
      );
    }
  
    
  
    public async getAttachment(messageId: string, attachmentId: string): Promise<string | undefined> {
      return this.withErrorHandler(
        'getAttachment',
        async () => {
          const imapClient = await this.ensureImapConnection();
          console.log(`Getting attachment ${attachmentId} for message ${messageId}`);
  
          // Parse the attachmentId to extract UID and attachment identifier
          // Format: 123:filename or 123:index
          const parts = attachmentId.split(':');
  
          if (parts.length !== 2 || !parts[0]) {
            throw new Error(`Invalid attachment ID format: ${attachmentId}`);
          }
  
          const uid = parseInt(parts[0]);
          const attachmentIdentifier = parts[1];
  
          if (isNaN(uid)) {
            throw new Error(`Invalid UID in attachment ID: ${attachmentId}`);
          }
  
          // Find the folder containing this message
          const folder = await this.findMessageFolder(messageId);
  
          if (!folder) {
            throw new Error(`Message with ID ${messageId} not found in any folder`);
          }
  
          // Open the folder
          await imapClient.mailboxOpen(folder);
  
          try {
            // Fetch the message with its structure
            const fetchedMessage = await imapClient.fetchOne(
              uid + '',
              {
                uid: true,
                source: true,
                bodyStructure: true,
              },
              { uid: true },
            );
  
            if (!fetchedMessage || !fetchedMessage.source) {
              console.warn(`⚠️ UID ${uid} 不存在或已被删除，无法获取附件`);
              return undefined; // 返回undefined表示附件不存在
            }
  
            // Parse the message
            const parsed = await simpleParser(fetchedMessage.source);
  
            // Find the attachment by filename or index
            const attachment = parsed.attachments?.find(
              (
                att: { filename?: string; contentId?: string; contentDisposition?: string },
                index: number,
              ) => {
                // Try to match by filename first
                if (att.filename === attachmentIdentifier) {
                  return true;
                }
                // Try to match by content ID
                if (att.contentId && att.contentId.replace(/[<>]/g, '') === attachmentIdentifier) {
                  return true;
                }
                // Try to match by index
                if (attachmentIdentifier === index.toString()) {
                  return true;
                }
                return false;
              },
            );
  
            if (!attachment) {
              throw new Error(`Attachment ${attachmentIdentifier} not found in message ${messageId}`);
            }
  
            // Convert to base64
            return attachment.content?.toString('base64');
          } catch (error) {
            console.error(`Error getting attachment ${attachmentId}:`, error);
            throw error;
          }
        },
        { messageId, attachmentId },
      );
    }
  
    public normalizeIds(ids: string[]): { threadIds: string[] } {
      return this.withSyncErrorHandler(
        'normalizeIds',
        () => {
          const threadIds: string[] = ids.map((id) =>
            id.startsWith('thread:') ? id.substring(7) : id,
          );
          return { threadIds };
        },
        { ids },
      );
    }
  
    public async starred(
    messageIds: string[],
    options: { addLabels: string[]; removeLabels: string[] },
  ): Promise<void> {
    return this.withErrorHandler(
      'starred',
      async () => {
          const imapClient = await this.ensureImapConnection();
          try {
            logWithTime(`Marking ${messageIds.length} threads as starred`);
          logWithTime(`开始标星 ---  threads as starred -- ${messageIds}`);

          for (const threadId of messageIds) {
            // Get the actual message ID (remove thread: prefix if present)
            const messageId = threadId.startsWith('thread:') ? threadId.substring(7) : threadId;
            // Find the folder containing this message
            const folder = await this.findMessageFolder(messageId);

            if (!folder) {
              console.warn(`Message ${messageId} not found in any folder, skipping mark as starred`);
              continue;
            }
            // Open the folder
            await imapClient.mailboxOpen(folder);

            // Search for the message by ID
            let results = await imapClient.search({
              header: {'Message-ID': `<${messageId}>`},
            },{uid: true});

            if (!results || !results.length) {
              // Try looking up by references
              // const refResults = await imapClient.search({
              //   header: {'References': messageId},
              // },{uid: true});
              if (false) {
                // Add found messages to results
                // if (!results) results = [];
                // results.push(...refResults);
              } else {
                // Try looking up by UID
                try {
                  const uid = parseInt(messageId);
                  if (!isNaN(uid)) {
                    const uidResults = await imapClient.search({ uid },{uid: true});
                    if (uidResults && uidResults.length) {
                      if (!results) results = [];
                      results.push(...uidResults);
                    }
                  }
                } catch (e) {
                  // Ignore parsing errors
                }
              }
            }

            if (results.length) {
              // 确保results是数组类型
              const messageUids = Array.isArray(results) ? results : [];

              if (messageUids.length > 0) {
                // 检查是添加还是移除标星
                if (options.addLabels.includes('STARRED')) {
                  // 添加标星标志
                  await imapClient.messageFlagsAdd(messageUids, ['\\Flagged'],{uid: true});
                  logWithTime(`Marked message(s) ${messageUids.join(', ')} as starred in folder ${folder}`);
                } else if (options.removeLabels.includes('STARRED')) {
                  // 移除标星标志
                  await imapClient.messageFlagsRemove(messageUids,['\\Flagged'],{uid:true});
                  logWithTime(`Removed star from message(s) ${messageUids.join(', ')} in folder ${folder}`);
                }
              }
            } else {
              console.warn(
                `Message ${messageId} not found in folder ${folder}, skipping mark as starred`,
              );
            }
          }
          
        }catch (error) {
          console.error(`Error modifying labels for messages ${messageIds}:`, error);
          throw new Error(`Failed to modify labels: ${(error as Error).message}`);
        }
      },
      { messageIds, options },
    );
  }

  public async modifyLabels(
    messageIds: string[],
    options: { addLabels: string[]; removeLabels: string[] },
  ): Promise<void> {
    return this.withErrorHandler(
      'modifyLabels',
      async () => {
        // 确保IMAP连接
        //const imapClient = await this.ensureImapConnection();

        logWithTime('Modifying labels for messages:', messageIds, options.addLabels, options.removeLabels);
        
        // 获取源文件夹
        const sourceFolder = options.removeLabels[0];
        // 获取目标文件夹
        let targetFolderId = options.addLabels[0];
        logWithTime(`进行移动: id ${messageIds} ${sourceFolder} -> ${targetFolderId}`);

        if(targetFolderId === 'STARRED'){
          // 添加标星
          logWithTime(`进行收藏: ${sourceFolder} `);
          await this.starred(messageIds, { addLabels: ['STARRED'], removeLabels: [] });
          return;
        }

        // 检查是否是取消标星操作
        if(sourceFolder === 'STARRED' && options.removeLabels.includes('STARRED')){
          // 移除标星
          logWithTime(`取消收藏: ${messageIds} `);
          await this.starred(messageIds, { addLabels: [], removeLabels: ['STARRED'] });
          return;
        }
        if(!targetFolderId && sourceFolder === 'INBOX'){
          //如果移动到archive
          targetFolderId = 'Archive';
        }
        
        if (!sourceFolder) {
          throw new Error('sourceFolder ID is undefined');
        }
        if (!targetFolderId) {
          throw new Error('Target folder ID is undefined');
        }
        // 映射源文件夹名称
        const mappedSourceFolder = await this.getImapFolderPath(sourceFolder);
        logWithTime(`源文件夹映射: ${sourceFolder} -> ${mappedSourceFolder}`);
          // 映射目标文件夹名称
          const mappedTargetFolder = await this.getImapFolderPath(targetFolderId);
          logWithTime(`目标文件夹映射: ${targetFolderId} -> ${mappedTargetFolder}`);

          // 如果源文件夹和目标文件夹相同，跳过
          if (mappedSourceFolder === mappedTargetFolder) {
            logWithTime(`源文件夹和目标文件夹相同，跳过移动: ${mappedSourceFolder}`);
            return;
          }
          
          // 🔧 使用连接池获取IMAP连接并执行移动操作
          const imapClient  = await this.ensureImapConnection();
          const lock = await imapClient.getMailboxLock(mappedSourceFolder);

          // 将字符串数组转换为数字数组
          let numericMessageIds:number[] = [];
          for (const messageId of messageIds) {
            // console.log("debug modifyLabels messageId:",messageId);
            if (isNaN(parseFloat(messageId))) {
              //messageId不是数字
              const emailUid = await imapClient.search({
                header: {'Message-ID': `<${messageId}>`}
              },{uid: true});
              // console.log("debug emailUid:",emailUid);
              if (!emailUid || !emailUid.length || emailUid[0] === undefined) {
                throw new Error('message id not found in mailbox');
              }
              numericMessageIds.push(emailUid[0]);
            }else {
              //messageId是数字,UID
              numericMessageIds.push(parseInt(messageId,10));
            }
          }
          
          // const numericMessageIds = messageIds
          //   .map((id) => parseInt(id, 10))
          //   .filter((id) => !isNaN(id));

          if (numericMessageIds.length === 0) {
            throw new Error('No valid numeric UIDs found in messageIds');
          }
          logWithTime(`执行移动: ${numericMessageIds.join(',')} 从 ${mappedSourceFolder} 到 ${mappedTargetFolder}`);
          
          try {
            logWithTime(`📂 已打开源文件夹: ${mappedSourceFolder}`);

            // 移动邮件到目标文件夹 (使用UID模式)
            const result = await imapClient.messageMove(numericMessageIds, mappedTargetFolder, { uid: true });

            logWithTime(`✅ 移动成功: ${numericMessageIds.length} 封邮件从 ${mappedSourceFolder} 到 ${mappedTargetFolder}`);
            logWithTime(`📊 移动结果:`, result);

          } catch (error) {
            console.error(`❌ 移动失败 ${mappedTargetFolder}:`, error);
            throw new Error(`Failed to move messages: ${(error as Error).message}`);
          } finally {
            // 🔧 确保释放邮箱锁
            lock.release();
            logWithTime(`🔓 已释放源文件夹锁: ${mappedSourceFolder}`);
          }
      },
      { messageIds, options },
    );
  }
  
    public async getLabel(labelId: string): Promise<Label> {
      return this.withErrorHandler(
        'getLabel',
        async () => {
          const imapClient = await this.ensureImapConnection();
  
          // If we already have the label in cache, return it
          const cachedLabel = this.userLabels.find((label) => label.id === labelId);
          if (cachedLabel) return cachedLabel;
  
          // Otherwise fetch all labels and look for it
          const labels = await this.getUserLabels();
          const label = labels.find((label) => label.id === labelId);
  
          if (!label) {
            throw new Error(`Label with ID ${labelId} not found`);
          }
  
          return label;
        },
        { labelId },
      );
    }
  
    public async createLabel(label: {
      name: string;
      color?: { backgroundColor: string; textColor: string };
    }): Promise<void> {
      return this.withErrorHandler(
        'createLabel',
        async () => {
          const imapClient = await this.ensureImapConnection();
  
          try {
            // Create the folder via IMAP
            await imapClient.mailboxCreate(label.name);
  
            // Refresh the labels cache
            this.userLabels = [];
            await this.getUserLabels();
          } catch (error) {
            console.error(`Error creating folder ${label.name}:`, error);
            throw new Error(`Failed to create folder: ${(error as Error).message}`);
          }
        },
        { label },
      );
    }
  
    public async deleteLabel(id: string): Promise<void> {
      return this.withErrorHandler(
        'deleteLabel',
        async () => {
          const imapClient = await this.ensureImapConnection();
  
          try {
            // Delete the folder via IMAP
            await imapClient.mailboxDelete(id);
  
            // Refresh the labels cache
            this.userLabels = [];
            await this.getUserLabels();
          } catch (error) {
            console.error(`Error deleting folder ${id}:`, error);
            throw new Error(`Failed to delete folder: ${(error as Error).message}`);
          }
        },
        { id },
      );
    }
  
    public async updateLabel(id: string, label: Label): Promise<void> {
      return this.withErrorHandler(
        'updateLabel',
        async () => {
        const imapClient = await this.ensureImapConnection();
  
          try {
            // Get the existing label
            const existingLabel = await this.getLabel(id);
  
            // For IMAP, we can only rename folders, not change colors
            if (existingLabel.name !== label.name) {
              await imapClient.mailboxRename(id, label.name);
            }
  
            // Refresh the labels cache
            this.userLabels = [];
            await this.getUserLabels();
          } catch (error) {
            console.error(`Error updating folder ${id}:`, error);
            throw new Error(`Failed to update folder: ${(error as Error).message}`);
          }
        },
        { id, label },
      );
    }
  
    public async getAliases(): Promise<any[]> {
      return this.withErrorHandler(
        'getAliases',
        async () => {
          return [];
        },
        {},
      );
    }
  
    public async getEmailAliases(): Promise<{ email: string; name?: string; primary?: boolean }[]> {
      return this.withErrorHandler(
        'getEmailAliases',
        async () => {
          // For IMAP/SMTP, we don't have a direct way to get aliases
          // Return the primary email from the config
          return [{ email: this.config.auth.email, primary: true }];
        },
        {},
      );
    }

  public async getUserInfo(): Promise<{ address: string; name: string; photo: string }> {
    return this.withErrorHandler(
      'getUserInfo',
      async () => {
       
        // For IMAP/SMTP, we don't have a direct way to get user info
        // So we'll return the email address from the config
        return {
          address: this.config.auth.email,
          name: '', // No way to get name via IMAP
          photo: '', // No way to get photo via IMAP
        };
      },
      {},
    );
  }
  public async revokeRefreshToken(refreshToken: string): Promise<boolean> {
    return this.withErrorHandler(
      'revokeRefreshToken',
      async () => {
        // For IMAP/SMTP, there's no concept of revoking a refresh token
        // since we use direct password authentication
        // We'll just return true to indicate success
        logWithTime('Refresh token revocation not applicable for IMAP/SMTP');
        return true;
      },
      { refreshToken },
    )}

  public async getTokens(
    code: string,
  ): Promise<{ tokens: { access_token?: string; refresh_token?: string; expiry_date?: number } }> {
    return this.withErrorHandler<{
      tokens: { access_token?: string; refresh_token?: string; expiry_date?: number };
    }>(
      'getTokens',
      async () => {
        // IMAP doesn't use OAuth tokens typically, but we'll return a shaped object for consistency
        return {
          tokens: {
            // For IMAP/SMTP, access token isn't relevant, but we maintain interface compatibility
            access_token: this.config.auth.accessToken || '',
            // We don't have a refresh token concept in IMAP/SMTP
            refresh_token: this.config.auth.refreshToken || '',
            // Set an expiry date far in the future (1 year from now)
            expiry_date: Date.now() + 365 * 24 * 60 * 60 * 1000,
          },
        };
      },
      { code },
    );
  }

  // Error handling methods
  private async withErrorHandler<T>(
    operation: string,
    fn: () => Promise<T> | T,
    context?: Record<string, unknown>,
  ): Promise<T> {
    try {
      const result = await Promise.resolve(fn());
      // 操作成功完成后，释放当前连接
      this.releaseCurrentConnection();
      return result;
    } catch (error: any) {
      const isFatal = FatalErrors.includes(error.message);
      // Detailed error logging
      console.error(
        `[${isFatal ? 'FATAL_ERROR' : 'ERROR'}] [IMAP/SMTP Driver] Operation: ${operation}`,
        {
          error: error.message,
          context: sanitizeContext(context),
          stack: error.stack,
          isFatal,
        },
      );
      // Close connection on fatal errors or release on non-fatal errors
      if (isFatal) {
        this.releaseCurrentConnection();
      } else {
        // 即使是非致命错误，也要释放连接以避免连接池饱和
        this.releaseCurrentConnection();
      }
      throw new StandardizedError(error, operation, context);
    }
  }

  private withSyncErrorHandler<T>(
    operation: string,
    fn: () => T,
    context?: Record<string, unknown>,
  ): T {
    try {
      return fn();
    } catch (error: any) {
      const isFatal = FatalErrors.includes(error.message);
      console.error(`[IMAP/SMTP Driver Error] Operation: ${operation}`, {
        error: error.message,
        context: sanitizeContext(context),
        stack: error.stack,
        isFatal,
      });
      if (isFatal ) {
        logWithTime('Deleting active connection due to fatal error');
      }
      throw new StandardizedError(error, operation, context);
    }
  }
}
