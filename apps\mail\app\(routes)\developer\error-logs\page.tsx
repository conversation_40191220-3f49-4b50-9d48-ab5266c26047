'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTRPC } from '@/providers/query-provider';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { AlertCircle, CheckCircle, XCircle, Calendar, Mail, Server, Clock } from 'lucide-react';

interface ErrorLogEntry {
  id: string;
  email: string;
  errorType: 'password_error' | 'config_error';
  errorMessage: string;
  connectionType: 'IMAP' | 'SMTP';
  timestamp: string;
  config: {
    host: string;
    port: number;
    secure: boolean;
  };
  serverTimestamp: string;
}

interface ErrorStats {
  totalLogs: number;
  passwordErrors: number;
  configErrors: number;
  imapErrors: number;
  smtpErrors: number;
  passwordErrorRate: string;
  configErrorRate: string;
}

export default function ErrorLogsPage() {
  const [errorCode, setErrorCode] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authError, setAuthError] = useState('');
  const trpc = useTRPC();

  // 获取可用日期列表
  const { data: datesData, refetch: refetchDates, error: datesError } = useQuery(
    trpc.settings.getErrorLogDates.queryOptions(
      { errorCode },
      {
        enabled: isAuthenticated && !!errorCode,
        retry: false,
        throwOnError: true
      }
    )
  );

  // 获取错误日志
  const { data: logsData, isLoading: logsLoading, refetch: refetchLogs, error: logsError } = useQuery(
    trpc.settings.getErrorLogs.queryOptions(
      { errorCode, date: selectedDate },
      {
        enabled: isAuthenticated && !!errorCode && !!selectedDate,
        retry: false
      }
    )
  );

  // 获取统计信息
  const { data: statsData, refetch: refetchStats, error: statsError } = useQuery(
    trpc.settings.getErrorLogStats.queryOptions(
      { errorCode, date: selectedDate },
      {
        enabled: isAuthenticated && !!errorCode && !!selectedDate,
        retry: false
      }
    )
  );

  const handleAuth = async () => {
    if (!errorCode.trim()) {
      setAuthError('请输入错误代码');
      return;
    }

    try {
      // 尝试获取日期列表来验证权限
      const result = await refetchDates();

      // 检查是否有数据返回，如果有则认证成功
      if (result.data?.success) {
        setIsAuthenticated(true);
        setAuthError('');
      } else {
        setAuthError('认证失败，请检查错误代码');
        setIsAuthenticated(false);
      }
    } catch (error: any) {
      // 处理API错误
      let errorMessage = '认证失败';
      if (error?.message?.includes('UNAUTHORIZED') || error?.message?.includes('无效的错误代码')) {
        errorMessage = '错误代码不正确，请重新输入';
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setAuthError(errorMessage);
      setIsAuthenticated(false);
    }
  };

  const handleDateChange = (date: string) => {
    setSelectedDate(date);
    if (isAuthenticated) {
      refetchLogs();
      refetchStats();
    }
  };

  const getErrorTypeColor = (errorType: string) => {
    return errorType === 'password_error' ? 'destructive' : 'secondary';
  };

  const getConnectionTypeColor = (connectionType: string) => {
    return connectionType === 'IMAP' ? 'default' : 'outline';
  };

  if (!isAuthenticated) {
    return (
      <div className="flex-1 flex items-center justify-center min-h-screen">
        <div className="w-full max-w-md p-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                错误日志访问
              </CardTitle>
              <CardDescription>
                请输入正确的错误代码以访问IMAP登录错误日志
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="errorCode">错误代码</Label>
                <Input
                  id="errorCode"
                  type="password"
                  placeholder="请输入错误代码"
                  value={errorCode}
                  onChange={(e) => setErrorCode(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleAuth()}
                />
              </div>
              {authError && (
                <div className="border-red-500/50 bg-red-500/10 flex items-center gap-2 rounded-md border px-3 py-2 text-sm text-red-600 dark:text-red-400">
                  <XCircle className="h-4 w-4" />
                  <span>{authError}</span>
                </div>
              )}
              <Button onClick={handleAuth} className="w-full">
                访问日志
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // 额外检查：如果认证了但是没有有效的日期数据，说明权限验证可能有问题
  if (isAuthenticated && !datesData?.success && !logsLoading) {
    return (
      <div className="flex-1 flex items-center justify-center min-h-screen">
        <div className="w-full max-w-md p-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <XCircle className="h-5 w-5" />
                访问被拒绝
              </CardTitle>
              <CardDescription>
                错误代码验证失败，请重新输入正确的错误代码
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => {
                  setIsAuthenticated(false);
                  setErrorCode('');
                  setAuthError('');
                }}
                className="w-full"
              >
                重新验证
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-screen overflow-hidden">
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 space-y-6 max-w-4xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">IMAP登录错误日志</h1>
            <p className="text-muted-foreground">查看和分析邮箱登录错误信息</p>
          </div>
          <Button
            variant="outline"
            onClick={() => {
              setIsAuthenticated(false);
              setErrorCode('');
            }}
          >
            退出
          </Button>
        </div>

      {/* 日期选择器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            选择日期
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={selectedDate} onValueChange={handleDateChange}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="选择日期" />
            </SelectTrigger>
            <SelectContent>
              {datesData?.dates?.map((date: string) => (
                <SelectItem key={date} value={date}>
                  {format(new Date(date), 'yyyy年MM月dd日', { locale: zhCN })}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      {statsData?.stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总错误数</p>
                  <p className="text-2xl font-bold">{statsData.stats.totalLogs}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">密码错误</p>
                  <p className="text-2xl font-bold text-red-600">{statsData.stats.passwordErrors}</p>
                  <p className="text-xs text-muted-foreground">{statsData.stats.passwordErrorRate}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">配置错误</p>
                  <p className="text-2xl font-bold text-orange-600">{statsData.stats.configErrors}</p>
                  <p className="text-xs text-muted-foreground">{statsData.stats.configErrorRate}</p>
                </div>
                <Server className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">IMAP/SMTP</p>
                  <p className="text-lg font-bold">{statsData.stats.imapErrors}/{statsData.stats.smtpErrors}</p>
                </div>
                <Mail className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 错误日志列表 */}
      <Card>
        <CardHeader>
          <CardTitle>错误日志详情</CardTitle>
          <CardDescription>
            {format(new Date(selectedDate), 'yyyy年MM月dd日', { locale: zhCN })} 的错误日志
          </CardDescription>
        </CardHeader>
        <CardContent>
          {logsLoading ? (
            <div className="text-center py-8">加载中...</div>
          ) : logsData?.logs?.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
              该日期没有错误日志记录
            </div>
          ) : (
            <ScrollArea className="h-[400px] w-full">
              <div className="space-y-4 pr-4">
                {logsData?.logs?.map((log: ErrorLogEntry) => (
                  <Card key={log.id} className="border-l-4 border-l-red-500">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2 flex-1">
                          <div className="flex items-center gap-2">
                            <Badge variant={getErrorTypeColor(log.errorType)}>
                              {log.errorType === 'password_error' ? '密码错误' : '配置错误'}
                            </Badge>
                            <Badge variant={getConnectionTypeColor(log.connectionType)}>
                              {log.connectionType}
                            </Badge>
                            <span className="text-sm text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {format(new Date(log.timestamp), 'HH:mm:ss', { locale: zhCN })}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{log.email}</span>
                          </div>
                          <p className="text-sm text-red-600 bg-red-50 p-2 rounded dark:bg-red-900/20 dark:text-red-400">
                            {log.errorMessage}
                          </p>
                          <div className="text-xs text-muted-foreground">
                            <span className="font-medium">服务器配置:</span> {log.config.host}:{log.config.port}
                            {log.config.secure ? ' (SSL)' : ' (非SSL)'}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
        </div>
      </div>
    </div>
  );
}
