<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>居中测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <!-- 测试当前的布局结构 -->
    <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
        <div class="w-full max-w-lg space-y-8">
            <!-- 返回按钮 -->
            <div class="text-left">
                <a href="#" class="inline-flex items-center text-sm text-slate-400 hover:text-white transition-colors duration-200">
                    ← 返回登录页面
                </a>
            </div>

            <!-- 主卡片 -->
            <div class="border border-slate-700 bg-slate-800/50 backdrop-blur-sm shadow-2xl rounded-lg">
                <div class="text-center space-y-4 p-6">
                    <div class="mx-auto w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-2xl">📧</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-white">SMTP/IMAP 登录</h1>
                        <p class="text-slate-300 mt-2">输入您的邮箱凭据，系统将自动配置邮件服务器</p>
                    </div>
                    
                    <!-- 安全连接标识 -->
                    <div class="flex items-center justify-center gap-2 text-sm text-emerald-400">
                        <span>🔒</span>
                        <span>安全加密连接</span>
                    </div>
                </div>

                <div class="space-y-6 p-6">
                    <!-- 邮箱输入框 -->
                    <div>
                        <label class="text-slate-200 font-medium block mb-2">邮箱地址</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">📧</span>
                            <input
                                type="email"
                                placeholder="<EMAIL>"
                                class="w-full pl-10 pr-4 py-3 bg-slate-700/50 border border-slate-600 text-white placeholder:text-slate-400 rounded-lg focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none"
                            />
                        </div>
                        <p class="text-xs text-slate-400 mt-1">支持163邮箱、QQ邮箱等，系统将自动配置服务器设置</p>
                    </div>

                    <!-- 密码输入框 -->
                    <div>
                        <label class="text-slate-200 font-medium block mb-2">密码/授权码</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">🔒</span>
                            <input
                                type="password"
                                placeholder="••••••••"
                                class="w-full pl-10 pr-4 py-3 bg-slate-700/50 border border-slate-600 text-white placeholder:text-slate-400 rounded-lg focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none"
                            />
                        </div>
                    </div>

                    <!-- 登录按钮 -->
                    <button class="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-3 rounded-lg transition-all duration-200 transform hover:scale-[1.02]">
                        登录
                    </button>

                    <!-- 支持的邮箱服务商 -->
                    <div class="mt-6 pt-6 border-t border-slate-700">
                        <div class="text-center">
                            <p class="text-sm text-slate-400 mb-3">支持的邮箱服务商</p>
                            <div class="flex flex-wrap justify-center gap-2">
                                <span class="px-2 py-1 bg-slate-700/50 text-slate-300 border border-slate-600 rounded text-xs">Gmail</span>
                                <span class="px-2 py-1 bg-slate-700/50 text-slate-300 border border-slate-600 rounded text-xs">Outlook</span>
                                <span class="px-2 py-1 bg-slate-700/50 text-slate-300 border border-slate-600 rounded text-xs">Yahoo</span>
                                <span class="px-2 py-1 bg-slate-700/50 text-slate-300 border border-slate-600 rounded text-xs">163</span>
                                <span class="px-2 py-1 bg-slate-700/50 text-slate-300 border border-slate-600 rounded text-xs">QQ</span>
                                <span class="px-2 py-1 bg-slate-700/50 text-slate-300 border border-slate-600 rounded text-xs">iCloud</span>
                            </div>
                            <p class="text-xs text-slate-500 mt-2">Gmail、Outlook、Yahoo、163、QQ、iCloud 等</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
