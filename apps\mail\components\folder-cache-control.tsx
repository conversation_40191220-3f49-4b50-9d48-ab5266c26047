/**
 * 文件夹缓存控制组件
 * 
 * 功能：
 * - 显示缓存进度
 * - 提供控制按钮
 * - 可折叠的详细信息
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Play, 
  Pause, 
  Square, 
  ChevronDown, 
  ChevronUp, 
  Folder, 
  CheckCircle, 
  XCircle,
  Clock,
  Settings
} from 'lucide-react';
import { useFolderAutoCache } from '@/hooks/use-folder-auto-cache';
import { cn } from '@/lib/utils';

interface FolderCacheControlProps {
  className?: string;
  compact?: boolean; // 紧凑模式
  showDetails?: boolean; // 是否显示详细信息
}

export function FolderCacheControl({ 
  className, 
  compact = false, 
  showDetails = true 
}: FolderCacheControlProps) {
  const {
    status,
    progress,
    isEnabled,
    start,
    pause,
    resume,
    stop,
    toggle,
    setEnabled,
  } = useFolderAutoCache();

  const [isExpanded, setIsExpanded] = useState(false);

  // 状态显示配置
  const statusConfig = {
    idle: { label: '待机', color: 'secondary', icon: Clock },
    running: { label: '运行中', color: 'default', icon: Play },
    paused: { label: '已暂停', color: 'outline', icon: Pause },
    completed: { label: '已完成', color: 'success', icon: CheckCircle },
    error: { label: '错误', color: 'destructive', icon: XCircle },
  } as const;

  const currentConfig = statusConfig[status];
  const StatusIcon = currentConfig.icon;

  // 计算进度百分比
  const progressPercentage = progress.total > 0 ? (progress.current / progress.total) * 100 : 0;

  // 渲染控制按钮
  const renderControlButtons = () => (
    <div className="flex items-center gap-2">
      {status === 'idle' || status === 'paused' ? (
        <Button
          size={compact ? 'sm' : 'default'}
          onClick={status === 'idle' ? start : resume}
          disabled={!isEnabled}
          className="gap-2"
        >
          <Play className="h-4 w-4" />
          {status === 'idle' ? '开始' : '恢复'}
        </Button>
      ) : null}
      
      {status === 'running' ? (
        <Button
          size={compact ? 'sm' : 'default'}
          variant="outline"
          onClick={pause}
          className="gap-2"
        >
          <Pause className="h-4 w-4" />
          暂停
        </Button>
      ) : null}
      
      {(status === 'running' || status === 'paused') ? (
        <Button
          size={compact ? 'sm' : 'default'}
          variant="destructive"
          onClick={stop}
          className="gap-2"
        >
          <Square className="h-4 w-4" />
          停止
        </Button>
      ) : null}
      
      {status === 'completed' ? (
        <Button
          size={compact ? 'sm' : 'default'}
          variant="outline"
          onClick={start}
          className="gap-2"
        >
          <Play className="h-4 w-4" />
          重新开始
        </Button>
      ) : null}
    </div>
  );

  // 紧凑模式渲染
  if (compact) {
    return (
      <div className={cn("flex items-center gap-3 p-2 bg-muted/50 rounded-lg", className)}>
        <div className="flex items-center gap-2">
          <StatusIcon className="h-4 w-4" />
          <Badge variant={currentConfig.color as any} className="text-xs">
            {currentConfig.label}
          </Badge>
        </div>
        
        {progress.total > 0 && (
          <div className="flex-1 min-w-0">
            <Progress value={progressPercentage} className="h-2" />
            <div className="text-xs text-muted-foreground mt-1">
              {progress.current}/{progress.total}
              {progress.currentFolder && ` - ${progress.currentFolder}`}
            </div>
          </div>
        )}
        
        {renderControlButtons()}
      </div>
    );
  }

  // 完整模式渲染
  return (
    <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Folder className="h-5 w-5" />
              文件夹自动缓存
              <Badge variant={currentConfig.color as any}>
                <StatusIcon className="h-3 w-3 mr-1" />
                {currentConfig.label}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setEnabled(!isEnabled)}
                className={cn(
                  "text-xs",
                  isEnabled ? "text-green-600" : "text-muted-foreground"
                )}
              >
                <Settings className="h-4 w-4 mr-1" />
                {isEnabled ? '已启用' : '已禁用'}
              </Button>

              {showDetails && (
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    {isExpanded ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
              )}
            </div>
          </CardTitle>
        </CardHeader>

      <CardContent className="space-y-4">
        {/* 进度显示 */}
        {progress.total > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>缓存进度</span>
              <span>{progress.current}/{progress.total}</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
            {progress.currentFolder && (
              <div className="text-sm text-muted-foreground">
                当前: {progress.currentFolder}
              </div>
            )}
          </div>
        )}

        {/* 控制按钮 */}
        <div className="flex justify-between items-center">
          {renderControlButtons()}
          
          <div className="text-sm text-muted-foreground">
            每2分钟缓存一个文件夹
          </div>
        </div>

        {/* 详细信息 */}
        {showDetails && (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleContent className="space-y-3 pt-3 border-t">
              {/* 已完成的文件夹 */}
              {progress.completedFolders.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    已缓存 ({progress.completedFolders.length})
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {progress.completedFolders.map(folder => (
                      <Badge key={folder} variant="success" className="text-xs">
                        {folder}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* 失败的文件夹 */}
              {progress.failedFolders.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-600" />
                    缓存失败 ({progress.failedFolders.length})
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {progress.failedFolders.map(folder => (
                      <Badge key={folder} variant="destructive" className="text-xs">
                        {folder}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* 缓存说明 */}
              <div className="text-xs text-muted-foreground space-y-1">
                <p>• 自动缓存会在登录2分钟后开始</p>
                <p>• 用户活跃时会自动暂停，空闲时恢复</p>
                <p>• 重要文件夹（收件箱、已发送）优先缓存</p>
                <p>• 缓存失败的文件夹会自动重试3次</p>
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
      </CardContent>
    </Card>
    </Collapsible>
  );
}

// 导出紧凑版本的便捷组件
export function CompactFolderCacheControl(props: Omit<FolderCacheControlProps, 'compact'>) {
  return <FolderCacheControl {...props} compact={true} />;
}
