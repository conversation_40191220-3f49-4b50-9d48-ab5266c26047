/**
 * 缓存清单管理器 - 使用 IndexedDB 记录每个账号的缓存进度
 */

import { openDB, type DBSchema, type IDBPDatabase } from 'idb';

// 缓存清单数据结构
export interface CacheManifest {
  accountId: string;           // 账号ID（邮箱地址）
  accountEmail: string;        // 账号邮箱
  lastUpdated: number;         // 最后更新时间
  folders: {
    [folderName: string]: {
      totalEmails: number;     // 文件夹总邮件数
      cachedPages: number;     // 已缓存页数
      cachedEmails: number;    // 已缓存邮件数
      lastCachedCursor: string; // 最后缓存的游标
      isCompleted: boolean;    // 是否缓存完成
      lastCacheTime: number;   // 最后缓存时间
    };
  };
}

// IndexedDB 数据库结构
interface CacheManifestDB extends DBSchema {
  manifests: {
    key: string; // accountId
    value: CacheManifest;
  };
}

export class CacheManifestManager {
  private db: IDBPDatabase<CacheManifestDB> | null = null;
  private readonly dbName = 'zero-cache-manifest';
  private readonly dbVersion = 1;

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      this.db = await openDB<CacheManifestDB>(this.dbName, this.dbVersion, {
        upgrade(db) {
          // 创建缓存清单表
          if (!db.objectStoreNames.contains('manifests')) {
            db.createObjectStore('manifests', { keyPath: 'accountId' });
          }
        },
      });
      console.log(`[${new Date().toLocaleString('zh-CN')}] 📋 缓存清单数据库初始化成功`);
    } catch (error) {
      console.error('缓存清单数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取账号的缓存清单
   */
  async getManifest(accountId: string): Promise<CacheManifest | null> {
    if (!this.db) await this.initialize();
    
    try {
      const manifest = await this.db!.get('manifests', accountId);
      return manifest || null;
    } catch (error) {
      console.error('获取缓存清单失败:', error);
      return null;
    }
  }

  /**
   * 创建或更新账号的缓存清单
   */
  async updateManifest(accountId: string, accountEmail: string, updates: Partial<CacheManifest>): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      const existing = await this.getManifest(accountId);
      
      const manifest: CacheManifest = {
        accountId,
        accountEmail,
        lastUpdated: Date.now(),
        folders: existing?.folders || {},
        ...updates,
      };

      await this.db!.put('manifests', manifest);
      console.log(`[${new Date().toLocaleString('zh-CN')}] 📋 更新缓存清单: ${accountEmail}`, manifest);
    } catch (error) {
      console.error('更新缓存清单失败:', error);
      throw error;
    }
  }

  /**
   * 初始化文件夹缓存记录
   */
  async initializeFolders(accountId: string, accountEmail: string, folders: string[]): Promise<void> {
    const manifest = await this.getManifest(accountId);
    const folderData: CacheManifest['folders'] = {};

    // 初始化每个文件夹的记录
    folders.forEach(folder => {
      folderData[folder] = manifest?.folders[folder] || {
        totalEmails: 0,
        cachedPages: 0,
        cachedEmails: 0,
        lastCachedCursor: '',
        isCompleted: false,
        lastCacheTime: 0,
      };
    });

    await this.updateManifest(accountId, accountEmail, { folders: folderData });
  }

  /**
   * 更新文件夹缓存进度
   */
  async updateFolderProgress(
    accountId: string,
    folderName: string,
    progress: {
      cachedPages?: number;
      cachedEmails?: number;
      lastCachedCursor?: string;
      totalEmails?: number;
      isCompleted?: boolean;
    }
  ): Promise<void> {
    const manifest = await this.getManifest(accountId);
    if (!manifest) {
      console.error('缓存清单不存在:', accountId);
      return;
    }

    if (!manifest.folders[folderName]) {
      manifest.folders[folderName] = {
        totalEmails: 0,
        cachedPages: 0,
        cachedEmails: 0,
        lastCachedCursor: '',
        isCompleted: false,
        lastCacheTime: 0,
      };
    }

    // 更新文件夹进度
    const updateData = { ...progress, lastCacheTime: Date.now() };

    // 只在明确提供 totalEmails 时才更新，避免覆盖为 undefined
    if (progress.totalEmails === undefined) {
      delete updateData.totalEmails;
    }

    Object.assign(manifest.folders[folderName], updateData);

    await this.updateManifest(accountId, manifest.accountEmail, { folders: manifest.folders });
  }

  /**
   * 获取下一个需要缓存的文件夹和页数
   * 轮询策略：inbox第1页 → drafts第1页 → sent第1页 → ... → inbox第2页 → ...
   */
  async getNextCacheTarget(accountId: string): Promise<{
    folder: string;
    page: number;
    cursor: string;
  } | null> {
    const manifest = await this.getManifest(accountId);
    if (!manifest) return null;

    const folders = ['inbox', 'drafts', 'sent', 'archive', 'spam', 'bin'];
    
    // 找到当前最小的缓存页数
    let minPages = Infinity;
    folders.forEach(folder => {
      if (manifest.folders[folder] && !manifest.folders[folder].isCompleted) {
        minPages = Math.min(minPages, manifest.folders[folder].cachedPages);
      }
    });

    // 如果所有文件夹都完成了，返回 null
    if (minPages === Infinity) return null;

    // 找到第一个页数等于最小页数且未完成的文件夹
    for (const folder of folders) {
      const folderData = manifest.folders[folder];
      if (folderData && !folderData.isCompleted && folderData.cachedPages === minPages) {
        return {
          folder,
          page: folderData.cachedPages + 1,
          cursor: folderData.lastCachedCursor,
        };
      }
    }

    return null;
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(accountId: string): Promise<{
    totalFolders: number;
    completedFolders: number;
    totalCachedEmails: number;
    folderStats: Array<{
      folder: string;
      cachedPages: number;
      cachedEmails: number;
      isCompleted: boolean;
    }>;
  } | null> {
    const manifest = await this.getManifest(accountId);
    if (!manifest) return null;

    const folderStats = Object.entries(manifest.folders).map(([folder, data]) => ({
      folder,
      cachedPages: data.cachedPages,
      cachedEmails: data.cachedEmails,
      isCompleted: data.isCompleted,
    }));

    return {
      totalFolders: folderStats.length,
      completedFolders: folderStats.filter(f => f.isCompleted).length,
      totalCachedEmails: folderStats.reduce((sum, f) => sum + f.cachedEmails, 0),
      folderStats,
    };
  }

  /**
   * 清理账号的缓存清单
   */
  async clearManifest(accountId: string): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      await this.db!.delete('manifests', accountId);
      console.log(`[${new Date().toLocaleString('zh-CN')}] 🗑️ 清理缓存清单: ${accountId}`);
    } catch (error) {
      console.error('清理缓存清单失败:', error);
    }
  }

  /**
   * 获取所有账号的缓存清单
   */
  async getAllManifests(): Promise<CacheManifest[]> {
    if (!this.db) await this.initialize();

    try {
      return await this.db!.getAll('manifests');
    } catch (error) {
      console.error('获取所有缓存清单失败:', error);
      return [];
    }
  }


}

// 单例实例
export const cacheManifestManager = new CacheManifestManager();
