{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "types": ["node", "vite/client"], "allowJs": true, "checkJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "verbatimModuleSyntax": true, "paths": {"@/*": ["./*"]}, "rootDirs": [".", "./.react-router/types"]}, "include": ["**/*.ts", "**/*.tsx", "worker-configuration.d.ts"], "exclude": ["node_modules"]}