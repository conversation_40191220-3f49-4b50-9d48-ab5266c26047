import {
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import { useTRPC } from '@/providers/query-provider';
import { Plus, Trash } from '../icons/icons';
import { useBrainState } from '@/hooks/use-summary';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '../ui/scroll-area';
import { toast } from 'sonner';

interface ITag {
  id: string;
  name: string;
  usecase: string;
  text: string;
}

export const defaultLabels = [
  {
    name: 'to respond',
    usecase: 'emails you need to respond to. NOT sales, marketing, or promotions.',
  },
  {
    name: 'FYI',
    usecase:
      'emails that are not important, but you should know about. NOT sales, marketing, or promotions.',
  },
  {
    name: 'comment',
    usecase:
      'Team chats in tools like Google Docs, Slack, etc. NOT marketing, sales, or promotions.',
  },
  {
    name: 'notification',
    usecase: 'Automated updates from services you use. NOT sales, marketing, or promotions.',
  },
  {
    name: 'promotion',
    usecase: 'Sales, marketing, cold emails, special offers or promotions. NOT to respond to.',
  },
  {
    name: 'meeting',
    usecase: 'Calendar events, invites, etc. NOT sales, marketing, or promotions.',
  },
  {
    name: 'billing',
    usecase: 'Billing notifications. NOT sales, marketing, or promotions.',
  },
];

interface AiLabelDialogContentProps {
  setIsAiLabelDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function AiLabelDialogContent({setIsAiLabelDialogOpen}: AiLabelDialogContentProps) {
  const trpc = useTRPC();
  // const [open, setOpen] = useState(false);
  const { data: storedLabels } = useQuery(trpc.brain.getLabels.queryOptions());
  const { mutateAsync: updateLabels, isPending } = useMutation(
    trpc.brain.updateLabels.mutationOptions(),
  );
  const [labels, setLabels] = useState<ITag[]>([]);
  const [newLabel, setNewLabel] = useState({ name: '', usecase: '' });
  const { mutateAsync: EnableBrain, isPending: isEnablingBrain } = useMutation(
    trpc.brain.enableBrain.mutationOptions(),
  );
  const { mutateAsync: DisableBrain, isPending: isDisablingBrain } = useMutation(
    trpc.brain.disableBrain.mutationOptions(),
  );
  const { data: brainState, refetch: refetchBrainState } = useBrainState();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (storedLabels) {
      setLabels(
        storedLabels.map((label) => ({
          id: label.name,
          name: label.name,
          text: label.name,
          usecase: label.usecase,
        })),
      );
    }
  }, [storedLabels]);

  const handleResetToDefault = useCallback(() => {
    setLabels(
      defaultLabels.map((label) => ({
        id: label.name,
        name: label.name,
        text: label.name,
        usecase: label.usecase,
      })),
    );
  }, [storedLabels]);

  const handleAddLabel = () => {
    if (!newLabel.name || !newLabel.usecase) return;
    setLabels([...labels, { id: newLabel.name, ...newLabel, text: newLabel.name }]);
    setNewLabel({ name: '', usecase: '' });
  };

  const handleDeleteLabel = (id: string) => {
    setLabels(labels.filter((label) => label.id !== id));
  };

  const handleUpdateLabel = (id: string, field: 'name' | 'usecase', value: string) => {
    setLabels(
      labels.map((label) =>
        label.id === id
          ? { ...label, [field]: value, text: field === 'name' ? value : label.text }
          : label,
      ),
    );
  };

  const handleSubmit = async () => {
    const updatedLabels = labels.map((label) => ({
      name: label.name,
      usecase: label.usecase,
    }));

    if (newLabel.name.trim() && newLabel.usecase.trim()) {
      updatedLabels.push({
        name: newLabel.name,
        usecase: newLabel.usecase,
      });
    }
    await updateLabels({ labels: updatedLabels });
    await queryClient.invalidateQueries({ queryKey: trpc.brain.getLabels.queryKey() });
    setNewLabel({ name: '', usecase: '' });
    setIsAiLabelDialogOpen(false);
    toast.success('Labels updated successfully, Onemails will start using them.');
  };

  const handleEnableBrain = useCallback(async () => {
    toast.promise(EnableBrain({}), {
      loading: 'Enabling autolabeling...',
      success: 'Autolabeling enabled successfully',
      error: 'Failed to enable autolabeling',
      finally: async () => {
        await refetchBrainState();
      },
    });
  }, []);

  const handleDisableBrain = useCallback(async () => {
    toast.promise(DisableBrain({}), {
      loading: 'Disabling autolabeling...',
      success: 'Autolabeling disabled successfully',
      error: 'Failed to disable autolabeling',
      finally: async () => {
        await refetchBrainState();
      },
    });
  }, []);

  const handleToggleAutolabeling = useCallback(() => {
    if (brainState?.enabled) {
      handleDisableBrain();
    } else {
      handleEnableBrain();
    }
  }, [brainState?.enabled]);

  return (
    <DialogContent showOverlay className="max-w-2xl">
      <DialogHeader>
        <DialogTitle>AI Agent Settings</DialogTitle>
      </DialogHeader>
      <DialogDescription className="mb-4 mt-2">
        These are the labels Onemails uses to autolabel your incoming emails. Feel free to modify them
        however you like. Onemails will create a new label in your account for each label you add - if
        it does not exist already.
      </DialogDescription>
      <ScrollArea className="h-[400px]">
        <div className="space-y-2">
          {labels.map((label) => (
            <div key={label.id} className="flex items-start gap-2 rounded-lg border p-3">
              <div className="flex-1 space-y-2">
                <input
                  type="text"
                  value={label.name}
                  onChange={(e) => handleUpdateLabel(label.id, 'name', e.target.value)}
                  className="w-full rounded-md border px-2 py-1 text-sm"
                  placeholder="Label name"
                />
                <textarea
                  value={label.usecase}
                  onChange={(e) => handleUpdateLabel(label.id, 'usecase', e.target.value)}
                  className="w-full rounded-md border px-2 py-1 text-sm"
                  placeholder="Label use case"
                  rows={2}
                />
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleDeleteLabel(label.id)}
              >
                <Trash className="h-4 w-4 fill-[#F43F5E]" />
              </Button>
            </div>
          ))}
          <div className="flex items-start gap-2 rounded-lg border p-3">
            <div className="flex-1 space-y-2">
              <input
                type="text"
                value={newLabel.name}
                onChange={(e) => setNewLabel({ ...newLabel, name: e.target.value })}
                className="w-full rounded-md border px-2 py-1 text-sm"
                placeholder="New label name"
              />
              <textarea
                value={newLabel.usecase}
                onChange={(e) => setNewLabel({ ...newLabel, usecase: e.target.value })}
                className="w-full rounded-md border px-2 py-1 text-sm"
                placeholder="New label use case"
                rows={2}
              />
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleAddLabel}
              disabled={!newLabel.name || !newLabel.usecase}
            >
              <Plus className="h-4 w-4 fill-slate-400" />
            </Button>
          </div>
        </div>
      </ScrollArea>
      <DialogFooter className="mt-4">
        <div className="flex w-full justify-between">
          <Button onClick={handleToggleAutolabeling} variant="outline" size="sm">
            {brainState?.enabled ? 'Disable' : 'Enable'}
          </Button>
          <div className="flex gap-2">
            <Button onClick={handleResetToDefault} variant="outline" size="sm">
              Use default labels
            </Button>
            <Button disabled={isPending} onClick={handleSubmit} size="sm">
              Save
            </Button>
          </div>
        </div>
      </DialogFooter>
    </DialogContent>
  );
}
