import { subscribePayApiSecret, subscribePayServiceHost, featureDailyLimitMap, devCode } from '../server';
import { AiChatPrompt, GmailSearchAssistantSystemPrompt } from '../lib/prompts';
import { connectionToDriver, getActiveConnection } from '../lib/server-utils';
import { streamText, generateObject, tool, generateText } from 'ai';
import { connection, userUsageDaily } from '@zero/db/schema';
import { getActiveDriver } from '../lib/driver/utils';
import { publicTools, tools } from './agent/tools';
import { getContext } from 'hono/context-storage';
import { createOpenAI } from '@ai-sdk/openai';
import type { HonoContext } from '../ctx';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

const openai = createOpenAI({
  baseURL: process.env.OPENAI_BASE_URL,
});

const buildGmailSearchQuery = tool({
  description: 'Build a Gmail search query',
  parameters: z.object({
    query: z.string().describe('The search query to build, provided in natural language'),
  }),
  execute: async ({ query }) => {
    const result = await generateObject({
      model: openai('gpt-4o'),
      system: GmailSearchAssistantSystemPrompt(),
      prompt: query,
      schema: z.object({
        query: z.string(),
      }),
    });
    return result.object;
  },
});

export const chatHandler = async () => {
  const c = getContext<HonoContext>();

  // const { session } = c.var;
  if (!c.var.sessionUser) return c.json({ error: 'Unauthorized' }, 401);

  console.log('Checking chat permissions for user:', c.var.sessionUser.id);
  // 获取用户会员信息
  const userEmail = c.var.sessionUser.email;
  const userId = c.var.sessionUser.id;
  let userSubscribeLevel: 'free' | 'Lite' | 'Pro' = 'free';
  try {
    const response = await fetch(subscribePayServiceHost + '/api/pay/subscribe/statusInfo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        secret: subscribePayApiSecret,
        appId: 2,
        appUsername: userId+devCode,
      }),
    });
    if (!response.ok) {
      throw new Error(`Error fetching data: ${response.status}`);
    }
    const jsonData: any = await response.json();
    if (jsonData?.data) {
      userSubscribeLevel = jsonData.data.planName;
    }
  } catch (error) {
    console.error('Error:', error);
  }
  const featureName = 'ai_chat';
  const totalDayLimit = featureDailyLimitMap[featureName][userSubscribeLevel];
  // 获取此刻的UTC日期
  const now = new Date();
  const utcMonthInt = now.getUTCMonth() + 1;
  let utcMonthStr = '';
  if (utcMonthInt <= 9) {
    utcMonthStr = '0' + utcMonthInt;
  } else {
    utcMonthStr = '' + utcMonthInt;
  }
  const utcDateInt = now.getUTCDate();
  let utcDateStr = '';
  if (utcDateInt <= 9) {
    utcDateStr = '0' + utcDateInt;
  } else {
    utcDateStr = '' + utcDateInt;
  }
  const todayUTCStr = `${now.getUTCFullYear()}-${utcMonthStr}-${utcDateStr}`;
  const queryResult = await c.var.db
    .select()
    .from(userUsageDaily)
    .where(
      and(
        eq(userUsageDaily.userFingerprint, userEmail),
        eq(userUsageDaily.featureName, featureName),
        eq(userUsageDaily.dayStr, todayUTCStr),
      ),
    );
  if (!queryResult?.length || !queryResult[0]) {
    // 没数据
    if (totalDayLimit > 0) {
      // 可以使用ai聊天功能，记录一次使用
      await c.var.db.insert(userUsageDaily).values({
        userId: userId,
        userFingerprint: userEmail,
        featureName: featureName,
        dailyCount: 1,
        dayStr: todayUTCStr,
      });
    } else {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }
  } else {
    // 有数据
    const todayUsage = queryResult[0];
    const currentDayUsage = todayUsage.dailyCount;
    if (totalDayLimit > currentDayUsage) {
      // 可以使用ai聊天功能，记录一次使用
      await c.var.db
        .update(userUsageDaily)
        .set({ dailyCount: todayUsage.dailyCount + 1 })
        .where(eq(userUsageDaily.id, todayUsage.id));
      if (todayUsage.userId !== userId) {
        // 在同一天，用户删除账号后又使用同一邮箱注册
        await c.var.db
          .update(userUsageDaily)
          .set({ userId: userId })
          .where(eq(userUsageDaily.id, todayUsage.id));
      }
    } else {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }
  }

  // const canSendMessages = await autumn.check({
  //   feature_id: 'chat-messages',
  //   customer_id: session.user.id,
  // });
  // console.log('Autumn check result:', JSON.stringify(canSendMessages, null, 2));

  // if (!canSendMessages.data) {
  //   console.log('No data returned from Autumn check');
  //   return c.json({ error: 'Insufficient permissions' }, 403);
  // }

  // if (canSendMessages.data.unlimited) {
  //   console.log('User has unlimited access');
  // } else if (!canSendMessages.data.balance) {
  //   console.log('No balance and not unlimited');
  //   return c.json({ error: 'Insufficient plan quota' }, 403);
  // } else if (canSendMessages.data.balance <= 0) {
  //   console.log('Balance is 0 or less');
  //   return c.json({ error: 'Insufficient plan balance' }, 403);
  // }
  //wusu 添加
  // if (!canSendMessages.data.unlimited) {
  //   try {
  //     // 在用户每次使用AI聊天时，通过Autumn服务减少一次使用次数
  //     // 这确保了用户在限定计划内使用AI功能，每次聊天会消耗一个额度
  //     console.log('Reducing usage count for user:', session.user.id);
  //     await autumn.track({
  //       customer_id: session.user.id,
  //       feature_id: 'chat-messages',
  //       value: 1,
  //     });
  //     console.log('Successfully reduced usage count');
  //   } catch (error) {
  //     console.error('Failed to reduce usage count:', error);
  //     // 继续处理请求，即使减少计数失败
  //   }
  // }

  const _conn = await getActiveConnection().catch((err) => {
    console.error('Error in getActiveConnection:', err);
    throw c.json({ error: 'Failed to get active connection' }, 500);
  });

  // 🚀 使用缓存的驱动实例，避免频繁重连
  const driver = await getActiveDriver();

  const { messages, threadId, currentFolder, currentFilter } = await c.req
    .json()
    .catch((err: Error) => {
      console.error('Error parsing JSON:', err);
      throw c.json({ error: 'Failed to parse request body' }, 400);
    });

  const result = streamText({
    model: openai('gpt-4o'),
    system: AiChatPrompt(threadId, currentFolder, currentFilter),
    messages,
    tools: {
      ...tools(driver, _conn.id),
      buildGmailSearchQuery,
    },
    onError: (error) => {
      console.error('Error in streamText:', error);
      //   throw c.json({ error: 'Failed to stream text' }, 500);
    },
  });

  return result.toDataStreamResponse();
};

export const publicChatHandler = async () => {
  const c = getContext<HonoContext>();
  const { message } = await c.req.json<{ message: string; query: string }>();
  const _connection = await c.var.db.query.connection.findFirst({
    where: eq(connection.email, '<EMAIL>'),
  });
  if (!_connection) {
    return c.json({ error: 'Connection not found' }, 404);
  }
  // 注意：这是公开聊天处理器，使用特定连接而不是缓存
  const driver = connectionToDriver(_connection);
  const result = await generateText({
    model: openai('gpt-4o'),
    system: AiChatPrompt('', '', ''),
    messages: [
      {
        role: 'user',
        content: message,
      },
    ],
    tools: { ...publicTools(driver, _connection.id), buildGmailSearchQuery },
  });

  return c.json({ response: result.text, toolResults: result.toolResults.map((r) => r.result) });
};
